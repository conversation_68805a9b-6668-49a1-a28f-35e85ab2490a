package config

import (
	"context"
	"fmt"

	tconfig "git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/config"
)

// Scurl scurl 配置
type Scurl struct {
	ScurlWhitelistConfig ScurlWhitelistConfig `yaml:"scurl_whitelist_config"`
}

// ScurlWhitelistConfig scurl 白名单配置
type ScurlWhitelistConfig struct {
	GormConfig struct {
		Name   string `yaml:"name"`
		Target string `yaml:"target"`
	} `yaml:"gorm_config"`
	ReloadSecond int32 `yaml:"reload_second"`
}

var scurl Scurl

// InitScurl 配置初始化
func InitScurl() {
	var err error
	rainbow := tconfig.Get("rainbow-comm")
	if rainbow != nil {
		scurlCh, err := rainbow.Watch(context.Background(), "scurl.yaml")
		if err != nil {
			panic(err)
		}

		go func() {
			for range scurlCh {
				scurlTmp := Scurl{}
				err = getScurlFromRainbow(&scurlTmp)
				if err != nil {
					continue
				}
				scurl = scurlTmp
			}
		}()
	}
	err = getScurlFromRainbow(&scurl)
	if err != nil {
		panic(err)
	}
}

func getScurlFromRainbow(scurl *Scurl) error {
	err := config.UnmarshalYAMLWithPlaceholder("scurl.yaml", &scurl, "rainbow-comm")
	if err != nil {
		log.Errorf("read scurl.yaml failed, error: %v", err)
		return err
	}

	log.Info("\n\n--------------------------------------------------------------------------------\n" +
		fmt.Sprintf("scurl: %+v\n", scurl) +
		"================================================================================")
	return nil
}

// GetScurl 获取 scurl 配置
func GetScurl() Scurl {
	return scurl
}
