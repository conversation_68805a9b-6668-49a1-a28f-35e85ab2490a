package config

import (
	"context"
	"fmt"

	tconfig "git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/config"
)

// Config 公共配置
type Config struct {
}

var cfg Config

// InitCfg 配置初始化
func InitCfg() {
	var err error
	rainbow := tconfig.Get("rainbow-comm")
	if rainbow != nil {
		cfgCh, err := rainbow.Watch(context.Background(), "config.yaml")
		if err != nil {
			panic(err)
		}

		go func() {
			for range cfgCh {
				cfgTmp := Config{}
				err = getCfgFromRainbow(&cfgTmp)
				if err != nil {
					continue
				}
				cfg = cfgTmp
			}
		}()
	}
	err = getCfgFromRainbow(&cfg)
	if err != nil {
		panic(err)
	}
}

func getCfgFromRainbow(cfg *Config) error {
	err := config.UnmarshalYAMLWithPlaceholder("config.yaml", &cfg, "rainbow-comm")
	if err != nil {
		log.Errorf("read config.yaml failed, error: %v", err)
		return err
	}

	log.Info("\n\n--------------------------------------------------------------------------------\n" +
		fmt.Sprintf("cfg: %+v\n", cfg) +
		"================================================================================")
	return nil
}

// GetConfig 获取 config 配置
func GetConfig() Config {
	return cfg
}
