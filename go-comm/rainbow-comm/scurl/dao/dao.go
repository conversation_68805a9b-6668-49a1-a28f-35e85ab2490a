package dao

import (
	"context"

	tgorm "git.code.oa.com/trpc-go/trpc-database/gorm"
	"git.code.oa.com/trpc-go/trpc-go/client"
	cfg "git.woa.com/dialogue-platform/go-comm/rainbow-comm/config"
	"git.woa.com/dialogue-platform/go-comm/rainbow-comm/scurl/entity"
	"gorm.io/gorm"
)

type Dao interface {
	// AddScurlDomain 添加 domain port
	AddScurlDomain(ctx context.Context, scurlDomain *entity.ScurlDomain) error
	// DelScurlDomain 删除 domain port
	DelScurlDomain(ctx context.Context, scurlDomain *entity.ScurlDomain) error

	// GetScurlDomainsByPage 获取所有的 domain port 按照id正序排序
	GetScurlDomainsByPage(ctx context.Context, page, pageSize int) ([]*entity.ScurlDomain, error)
}

var defaultDao Dao

type dao struct {
	db *gorm.DB
}

func initDao() {
	tdb, err := tgorm.NewClientProxy(cfg.GetScurl().ScurlWhitelistConfig.GormConfig.Name,
		[]client.Option{
			client.WithTarget(cfg.GetScurl().ScurlWhitelistConfig.GormConfig.Target),
		}...)
	if err != nil {
		panic(err)
	}

	defaultDao = &dao{
		db: tdb,
	}
}

// GetDao get dao
func GetDao() Dao {
	if defaultDao == nil {
		initDao()
	}
	return defaultDao
}
