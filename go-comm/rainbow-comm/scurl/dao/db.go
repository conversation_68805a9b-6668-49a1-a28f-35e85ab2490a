package dao

import (
	"context"
	"encoding/json"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/rainbow-comm/scurl/entity"
)

// AddScurlDomain 添加 domain port
func (d dao) AddScurlDomain(ctx context.Context, scurlDomain *entity.ScurlDomain) error {
	log.DebugContextf(ctx, "AddScurlDomain: %+v", scurlDomain)
	err := d.db.WithContext(ctx).Model(&entity.ScurlDomain{}).Create(scurlDomain).Error
	if err != nil {
		log.ErrorContextf(ctx, "AddScurlDomain db.Create err: %+v", err)
		return err
	}
	return nil
}

// DelScurlDomain 删除 domain port
func (d dao) DelScurlDomain(ctx context.Context, scurlDomain *entity.ScurlDomain) error {
	log.DebugContextf(ctx, "DelScurlDomain: %+v", scurlDomain)
	query := fmt.Sprintf("%s = ? AND %s = ? AND %s = ?",
		entity.ScurlDomainColumns.Domain, entity.ScurlDomainColumns.Port, entity.ScurlDomainColumns.Uin)
	err := d.db.WithContext(ctx).Model(&entity.ScurlDomain{}).
		Where(query, scurlDomain.Domain, scurlDomain.Port, scurlDomain.Uin).
		Updates(map[string]interface{}{
			entity.ScurlDomainColumns.IsDeleted: 1,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "DelScurlDomain db.Updates err: %+v", err)
		return err
	}
	return nil
}

// GetScurlDomainsByPage 获取所有的 domain port 按照id正序排序
func (d dao) GetScurlDomainsByPage(ctx context.Context, page, pageSize int) ([]*entity.ScurlDomain, error) {
	log.DebugContextf(ctx, "GetScurlDomainsByPage page: %d, pageSize: %d", page, pageSize)
	var scurlDomain []*entity.ScurlDomain

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 500
	}
	offset := (page - 1) * pageSize

	// 保证按照ID正序
	query := fmt.Sprintf("%s = ?", entity.ScurlDomainColumns.IsDeleted)
	err := d.db.WithContext(ctx).Model(&entity.ScurlDomain{}).
		Where(query, 0).
		Order(fmt.Sprintf("%s ASC", entity.ScurlDomainColumns.ID)).
		Offset(offset).Limit(pageSize).
		Find(&scurlDomain).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetScurlDomainsByPage err: %+v", err)
		return nil, err
	}

	data, _ := json.Marshal(scurlDomain)
	log.DebugContextf(ctx, "GetScurlDomainsByPage scurlDomain: %s", data)
	return scurlDomain, nil
}
