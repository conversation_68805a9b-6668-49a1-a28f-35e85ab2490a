package entity

import "time"

var ScurlDomainTable = "t_scurl_domain"

type ScurlDomain struct {
	ID         uint64    `gorm:"column:id;primaryKey;autoIncrement"`  // 自增ID
	DomainID   string    `gorm:"column:f_domain_id"`                  // Domain ID
	Domain     string    `gorm:"column:f_domain"`                     // Domain
	Port       string    `gorm:"column:f_port"`                       // Port
	Uin        string    `gorm:"column:f_uin"`                        // Uin
	DomainDesc string    `gorm:"column:f_domain_desc"`                // 描述
	IsDeleted  int32     `gorm:"column:f_is_deleted"`                 // 是否删除，0: 否，1:是
	CreateTime time.Time `gorm:"column:f_create_time;autoCreateTime"` // 创建时间，自动创建
	UpdateTime time.Time `gorm:"column:f_update_time;autoUpdateTime"` // 更新时间，自动更新
}

var ScurlDomainColumns = struct {
	ID         string
	DomainID   string
	Domain     string
	Port       string
	Uin        string
	Desc       string
	IsDeleted  string
	CreateTime string
	UpdateTime string
}{
	ID:         "id",
	DomainID:   "f_domain_id",
	Domain:     "f_domain",
	Port:       "f_port",
	Uin:        "f_uin",
	Desc:       "f_domain_desc",
	IsDeleted:  "f_is_deleted",
	CreateTime: "f_create_time",
	UpdateTime: "f_update_time",
}

// TableName table name
func (s *ScurlDomain) TableName() string {
	return ScurlDomainTable
}

// DomainPort domain:port
func (s *ScurlDomain) DomainPort() string {
	if s == nil {
		return ""
	}
	if len(s.Domain) == 0 {
		return s.Port
	}
	if len(s.Port) == 0 {
		return s.Domain
	}
	return s.Domain + ":" + s.Port
}

// IsDelete 是否已经删除
func (s *ScurlDomain) IsDelete() bool {
	if s == nil {
		return false
	}
	if s.IsDeleted != 0 {
		return true
	}
	return false
}
