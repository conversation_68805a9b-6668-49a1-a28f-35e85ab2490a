package scurl

import (
	"context"
	"errors"
	"sync"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/go-comm/rainbow-comm/config"
	"git.woa.com/dialogue-platform/go-comm/rainbow-comm/scurl/dao"
	"git.woa.com/dialogue-platform/go-comm/rainbow-comm/scurl/entity"
	"github.com/google/uuid"
)

var (
	scurlDomainMapMutex sync.RWMutex
	ctx                 = context.Background()
	scurlDomainMap      = make(map[string]map[string]*entity.ScurlDomain)
)

func initScurlDomainMap(reloadSecond int32) {
	if _, err := loadScurlDomainMap(); err != nil {
		log.ErrorContextf(ctx, "initScurlDomainMap loadScurlDomainMap failed: %v", err)
	}
	go reloadScurlDomainMap(reloadSecond)
}

func loadScurlDomainMap() (map[string]map[string]*entity.ScurlDomain, error) {
	tmpMap := make(map[string]map[string]*entity.ScurlDomain)

	page, pageSize := 1, 500
	for {
		scurlDomains, err := dao.GetDao().GetScurlDomainsByPage(ctx, page, pageSize)
		if err != nil {
			log.ErrorContextf(ctx, "loadScurlDomainMap GetScurlDomainsByPage err: %+v", err)
			return nil, err
		}

		for _, scurlDomain := range scurlDomains {
			sdMap, ok := tmpMap[scurlDomain.Uin]
			if !ok {
				sdMap = make(map[string]*entity.ScurlDomain)
			}
			sdMap[scurlDomain.DomainPort()] = scurlDomain
			tmpMap[scurlDomain.Uin] = sdMap
		}

		if len(scurlDomains) < pageSize {
			break
		}
		page++
	}

	scurlDomainMapMutex.Lock()
	scurlDomainMap = tmpMap
	scurlDomainMapMutex.Unlock()

	return tmpMap, nil
}

func reloadScurlDomainMap(reloadSecond int32) {
	ticker := time.NewTicker(time.Duration(reloadSecond) * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		log.DebugContextf(ctx, "reloadScurlDomainMap begin: %s", time.Now())
		if _, err := loadScurlDomainMap(); err != nil {
			log.ErrorContextf(ctx, "reloadScurlDomainMap loadScurlDomainMap failed: %v", err)
		}
		log.DebugContextf(ctx, "reloadScurlDomainMap end: %s", time.Now())
	}
}

// Init 初始化
func Init() {
	config.InitScurl()
	initScurlDomainMap(config.GetScurl().ScurlWhitelistConfig.ReloadSecond)
}

// InitWithReloadTime 初始化 指定轮询时间
func InitWithReloadTime(reloadSecond int32) {
	config.InitScurl()
	if reloadSecond <= 0 {
		reloadSecond = config.GetScurl().ScurlWhitelistConfig.ReloadSecond
	}
	initScurlDomainMap(reloadSecond)
}

// GetScurlDomainWL 获取scurl domain 白名单
func GetScurlDomainWL(ctx context.Context) []string {
	return getScurlWLByField(ctx, "domain")
}

// GetScurlPortWL 获取scurl port 白名单
func GetScurlPortWL(ctx context.Context) []string {
	return getScurlWLByField(ctx, "port")
}

// GetScurlDomainWLByUin 通过uin获取scurl domain 白名单 包含uin为*的
func GetScurlDomainWLByUin(ctx context.Context, uin string) []string {
	return getScurlWLByUinAndField(ctx, uin, "domain")
}

// GetScurlPortWLByUin 通过uin获取scurl port 白名单 包含uin为*的
func GetScurlPortWLByUin(ctx context.Context, uin string) []string {
	return getScurlWLByUinAndField(ctx, uin, "port")
}

// getScurlWLByField 获取所有uin对应的某字段白名单（domain或port，包含uin为*的）
func getScurlWLByField(ctx context.Context, field string) []string {
	scurlDomainMapMutex.RLock()
	tmpMap := scurlDomainMap
	scurlDomainMapMutex.RUnlock()

	uniqueSet := make(map[string]struct{})
	for _, sdMap := range tmpMap {
		for _, sd := range sdMap {
			var val string
			switch field {
			case "domain":
				val = sd.Domain
			case "port":
				val = sd.Port
			}
			if val != "" {
				uniqueSet[val] = struct{}{}
			}
		}
	}

	result := make([]string, 0, len(uniqueSet))
	for v := range uniqueSet {
		result = append(result, v)
	}
	log.DebugContextf(ctx, "getScurlWLByField %s: %+v", field, result)
	return result
}

// getScurlWLByUinAndField 通过uin获取某字段白名单（domain或port，包含uin为*的）
func getScurlWLByUinAndField(ctx context.Context, uin, field string) []string {
	scurlDomainMapMutex.RLock()
	uinMap, ok := scurlDomainMap[uin]
	commMap, commOk := scurlDomainMap["*"]
	scurlDomainMapMutex.RUnlock()

	if !ok && !commOk {
		tmpMap, err := loadScurlDomainMap()
		if err != nil {
			log.ErrorContextf(ctx, "loadScurlDomainMap failed: %v", err)
			return nil
		}
		uinMap, ok = tmpMap[uin]
		commMap, commOk = tmpMap["*"]
		// 如果还是都没有，直接返回
		if !ok && !commOk {
			return nil
		}
	}

	uniqueSet := make(map[string]struct{})
	collect := func(sdMap map[string]*entity.ScurlDomain) {
		for _, sd := range sdMap {
			var val string
			switch field {
			case "domain":
				val = sd.Domain
			case "port":
				val = sd.Port
			}
			if val != "" {
				uniqueSet[val] = struct{}{}
			}
		}
	}

	if ok {
		collect(uinMap)
	}
	if commOk {
		collect(commMap)
	}

	result := make([]string, 0, len(uniqueSet))
	for v := range uniqueSet {
		result = append(result, v)
	}
	log.DebugContextf(ctx, "getScurlWLByUinAndField uin=%s %s: %+v", uin, field, result)
	return result
}

// AddScurlDomainWL 添加scurl domain port 白名单
func AddScurlDomainWL(ctx context.Context, uin, domain, port, desc string) error {
	if err := checkParam(uin, domain, port); err != nil {
		return err
	}
	scurlDomain := &entity.ScurlDomain{
		DomainID:   uuid.NewString(),
		Domain:     domain,
		Port:       port,
		Uin:        uin,
		DomainDesc: desc,
	}
	if err := dao.GetDao().AddScurlDomain(ctx, scurlDomain); err != nil {
		log.ErrorContextf(ctx, "AddScurlDomainWL err: %v", err)
		return err
	}
	if _, err := loadScurlDomainMap(); err != nil {
		log.ErrorContextf(ctx, "AddScurlDomainWL loadScurlDomainMap failed: %v", err)
	}
	return nil
}

// DelScurlDomainWL 删除scurl domain port 白名单
func DelScurlDomainWL(ctx context.Context, uin, domain, port string) error {
	if err := checkParam(uin, domain, port); err != nil {
		return err
	}
	scurlDomain := &entity.ScurlDomain{
		DomainID: uuid.NewString(),
		Domain:   domain,
		Port:     port,
		Uin:      uin,
	}
	if err := dao.GetDao().DelScurlDomain(ctx, scurlDomain); err != nil {
		log.ErrorContextf(ctx, "DelScurlDomainWL err: %v", err)
		return err
	}
	if _, err := loadScurlDomainMap(); err != nil {
		log.ErrorContextf(ctx, "DelScurlDomainWL loadScurlDomainMap failed: %v", err)
	}
	return nil
}

func checkParam(uin, domain, port string) error {
	if len(uin) == 0 {
		return errors.New("uin empty")
	}
	if len(domain) == 0 && len(port) == 0 {
		return errors.New("domain and port both empty")
	}
	return nil
}
