# rainbow-comm

七彩石以及团队公共配置

## 使用说明

### 1. 项目`trpc_go.yaml`配置文件需要增加七彩石的配置

*!!!强依赖!!!!!!强依赖!!!!!!强依赖!!!*

```yaml
config:
    rainbow: # 七彩石配置中心
      providers:
        ...
        - name: rainbow
          appid: 3f090873-420d-4575-b9d2-cf6a72980a33 # appid
          group: xxx # 配置所属组
          type: kv   # 七彩石数据格式, kv(默认), table
          env_name: ${ENV}
          enable_client_provider: true
          timeout: 2000
        - name: rainbow-comm # 公共库配置
          appid: 3f090873-420d-4575-b9d2-cf6a72980a33 # appid
          group: rainbow-comm # 配置所属组
          type: kv   # 七彩石数据格式, kv(默认), table
          env_name: ${ENV}
          timeout: 2000
        ...
```

### 2. `main` 函数初始化

```go
import (
    scurlcomm "git.woa.com/dialogue-platform/go-comm/rainbow-comm/scurl"
)

func main() {
	...
	scurlcomm.Init()
	...
	
	// or

	...
	scurlcomm.InitWithReloadTime(10)
    ...
}
```

## 公共方法

### 白名单

#### scurl

```go
import (
    scurlcomm "git.woa.com/dialogue-platform/go-comm/rainbow-comm/scurl"
)
```

##### 1. 业务使用

获取全部的白名单Domains
```go
GetScurlDomainWL(ctx context.Context) []string

scurlcomm.GetScurlDomainWL(ctx)
```

获取全部的白名单Ports
```go
GetScurlPortWL(ctx context.Context) []string

scurlcomm.GetScurlPortWL(ctx)
```

按照uin获取白名单Domains 包含uin为*的
```go
GetScurlDomainWLByUin(ctx context.Context, uin string) []string

scurlcomm.GetScurlDomainWLByUin(ctx, uin)
```

按照uin获取白名单Port 包含uin为*的
```go
GetScurlPortWLByUin(ctx context.Context, uin string) []string

scurlcomm.GetScurlPortWLByUin(ctx, uin)
```

添加白名单
```go
AddScurlDomainWL(ctx context.Context, uin, domain, port, desc string) error

scurlcomm.AddScurlDomainWL(ctx, uin, domain, port, desc)
```

删除白名单
```go
DelScurlDomainWL(ctx context.Context, uin, domain, port string) error

scurlcomm.DelScurlDomainWL(ctx, uin, domain, port)
```