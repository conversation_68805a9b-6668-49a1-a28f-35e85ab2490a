// bot-task-config-server
//
// @(#)import_app_workflow.go  星期四, 六月 05, 2025
// Copyright(c) 2025, mikeljiang@Tencent. All rights reserved.

package bottask

import (
	"context"
	"strconv"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// CopyWorkflowByAPPIDScheduler 复制应用A的工作流到应用B（
type CopyWorkflowByAPPIDScheduler struct {
	task   task_scheduler.Task
	params entity.TaskCopyAppWorkflowParams
}

func init() {
	task_scheduler.Register(
		entity.TaskImportWorkflowByAPPID,
		func(task task_scheduler.Task, params entity.TaskCopyAppWorkflowParams) task_scheduler.TaskHandler {
			return &CopyWorkflowByAPPIDScheduler{
				task:   task,
				params: params,
			}
		})
}

// Prepare 数据准备
func (c CopyWorkflowByAPPIDScheduler) Prepare(_ context.Context) (task_scheduler.TaskKV, error) {
	return task_scheduler.TaskKV{}, nil
}

// Init 初始化
func (c CopyWorkflowByAPPIDScheduler) Init(ctx context.Context, _ task_scheduler.TaskKV) error {
	// 填充RequestID和日志信息
	util.WithRequestID(ctx, c.params.RequestID)
	ctx = log.WithContextFields(ctx, "RequestID", util.RequestID(ctx))
	log.InfoContextf(ctx, "CopyWorkflowByAPPIDScheduler Init RobotID:%s,OriginalRobotID:%s,WorkflowID:%s,"+
		"NewWorkflowID:%s,TaskID:%s", c.params.RobotID, c.params.OriginalRobotID, c.params.WorkflowID,
		c.params.NewWorkflowID, c.params.TaskID)
	return nil
}

// Process 任务处理
func (c CopyWorkflowByAPPIDScheduler) Process(ctx context.Context, _ *task_scheduler.Progress) error {
	log.InfoContextf(ctx, "CopyWorkflowByAPPIDScheduler process start")
	uin, _ := strconv.ParseUint(c.params.Uin, 10, 64)
	subUin, _ := strconv.ParseUint(c.params.SubUin, 10, 64)
	ctx = ConstructCtx(ctx, c.params.CorpID, c.params.StaffID, uin, subUin)
	// 执行同UIN下跨应用的工作流复制操作
	err := CopyAppWorkflow(ctx, c.params.RobotID, nil, c.params.OriginalRobotID,
		"", "", c.params.NewWorkflowID, c.params.WorkflowID,
		c.params.Uin, c.params.SubUin, c.params.StaffID, entity.CopyAppWorkflowType)
	if err != nil {
		log.ErrorContextf(ctx, "CopyExperienceWorkflowScheduler Process CopyAppWorkflow|RobotID:%s,"+
			"OriginalRobotID:%s,WorkflowID:%s,NewWorkflowID:%s,TaskID:%s, err:%v", c.params.RobotID,
			c.params.OriginalRobotID, c.params.WorkflowID, c.params.NewWorkflowID, c.params.TaskID, err)
		return err
	}
	log.InfoContextf(ctx, "CopyWorkflowByAPPIDScheduler process end taskID:%s", c.params.TaskID)
	return nil
}

// Done 任务完成回调
func (c CopyWorkflowByAPPIDScheduler) Done(ctx context.Context) error {
	log.InfoContextf(ctx, "CopyWorkflowByAPPIDScheduler Done start taskID:%s", c.params.TaskID)
	//importId := strconv.FormatUint(c.params.TaskID, 10)
	//if err := db.UpdateWorkflowImportStatus(ctx, importId, entity.FlowImportStatusProcessing,
	//	entity.FlowImportStatusProcessed); err != nil {
	//	return err
	//}
	// 同UIN跨应用复制工作流完成回调通知admin,失败重试三次
	appBizId, _ := strconv.ParseUint(c.params.RobotID, 10, 64)
	request := &admin.CopyAppCallbackReq{
		Type:     admin.CopyAppCallbackType_Workflow,
		AppBizId: appBizId,
	}
	for i := 0; i < 3; i++ {
		_, err := rpc.CopyAppCallback(ctx, request)
		if err == nil {
			log.InfoContextf(ctx, "CopyExperienceWorkflowScheduler Done rpc.CopyAppCallback success")
			break
		}
		log.WarnContextf(ctx, "CopyExperienceWorkflowScheduler Done rpc.CopyAppCallback failed")
	}
	log.InfoContextf(ctx, "CopyWorkflowByAPPIDScheduler Done end taskID:%s", c.params.TaskID)
	return nil
}

// Fail 任务失败
func (c CopyWorkflowByAPPIDScheduler) Fail(ctx context.Context) error {
	log.ErrorContextf(ctx, "CopyWorkflowByAPPIDScheduler Fail start taskID:%s", c.params.TaskID)
	// 同UIN跨应用复制工作流失败回调通知admin,失败重试三次
	appBizId, _ := strconv.ParseUint(c.params.RobotID, 10, 64)
	request := &admin.CopyAppCallbackReq{
		Type:     admin.CopyAppCallbackType_Workflow,
		AppBizId: appBizId,
	}
	for i := 0; i < 3; i++ {
		_, err := rpc.CopyAppCallback(ctx, request)
		if err == nil {
			log.InfoContextf(ctx, "CopyExperienceWorkflowScheduler Fail rpc.CopyAppCallback success")
			break
		}
		log.WarnContextf(ctx, "CopyExperienceWorkflowScheduler Fail rpc.CopyAppCallback failed")
	}
	log.ErrorContextf(ctx, "CopyWorkflowByAPPIDScheduler Fail end taskID:%s", c.params.TaskID)
	return nil
}

// Stop 任务停止
func (c CopyWorkflowByAPPIDScheduler) Stop(ctx context.Context) error {
	log.WarnContextf(ctx, "CopyWorkflowByAPPIDScheduler Stop taskID:%s", c.params.TaskID)
	return nil
}
