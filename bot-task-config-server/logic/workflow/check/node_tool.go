/*
 * 2024-10-15
 * Copyright (c) 2024. x<PERSON><PERSON><PERSON>n@Tencent. All rights reserved.
 *
 */

package check

import (
	"context"
	"fmt"
	"net/url"
	"regexp"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	scurlcomm "git.woa.com/dialogue-platform/go-comm/rainbow-comm/scurl"
	"git.woa.com/dialogue-platform/go-comm/security"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

const (
	APIMethodGet    = "GET"
	APIMethodPost   = "POST"
	APIMethodDelete = "DELETE"

	APIProtocolHTTP  = "http://"
	APIProtocolHTTPS = "https://"
)

var (
	checkSecurityIfDenyAccess = security.IfDenyAccess
)

func (c *WfContext) parseToolNode(wfn *KEP_WF.WorkflowNode) {
	c.checkToolNode(wfn)
}

func (c *WfContext) checkToolNode(wfn *KEP_WF.WorkflowNode) {
	c.checkToolNodeCustomApi(wfn)
	c.checkToolNodeHeader(wfn)
	c.checkToolNodeQuery(wfn)
	c.checkToolNodeBody(wfn)
	c.checkToolNodeRsp(wfn)
}

// TODO(philxu) tool 和 插件节点共用?
//func (c *WfContext) checkAndCollectToolNodeData(data *KEP_WF.ToolNodeData) {
// // 对比 Ref的Workflow 的 入参、出参的  diff；  要保持一致；
//}

func (c *WfContext) checkToolNodeCustomApi(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetToolNodeData()

	c.checkToolNodeCustomApiAuth(nodeID, node)

	apiURL := node.GetAPI().GetURL()
	if apiURL == "" {
		c.appendNodeError(nodeID, "接口链接为空")
	} else {
		// 请求API路径协议：仅限http、https,strings.ToLower
		decodeUrl, err := url.QueryUnescape(apiURL)
		if err != nil {
			c.appendNodeError(nodeID, "接口链接URL解析错误")
		}
		path := strings.TrimSpace(strings.ToLower(decodeUrl))
		if path == "" {
			c.appendNodeError(nodeID, "接口链接为空")
		} else {
			if !isValidProtocol(apiURL) {
				c.appendNodeError(nodeID, "接口链接协议需是https或http")
			}
			// 安全校验
			configWhiteIps := config.GetMainConfig().ToolNodePathSafety.WhiteIp
			ips := make([]string, 0, len(configWhiteIps)+len(c.SafeUrls))
			if len(configWhiteIps) > 0 {
				ips = append(ips, configWhiteIps...)
			}
			if len(c.SafeUrls) > 0 {
				ips = append(ips, c.SafeUrls...)
			}
			uin, _ := util.GetUinAndSubAccountUin(c.ctx)
			ips = append(ips, scurlcomm.GetScurlDomainWLByUin(context.Background(), uin)...)
			// 主要是校验内网地址
			deny := checkSecurityIfDenyAccess(path, ips)
			log.InfoContextf(c.ctx, "checkToolNodeCustomApi path:%s|deny:%+v|isSecPass:%+v", path, deny)
			if deny {
				c.appendNodeError(nodeID, fmt.Sprintf("接口地址:%s无法访问，请使用可访问的http、https公网地址", path))
			}
		}
	}
	if node.GetAPI().GetMethod() != APIMethodGet && node.GetAPI().GetMethod() != APIMethodPost &&
		node.GetAPI().GetMethod() != APIMethodDelete {
		c.appendNodeError(nodeID, "请求方式不符合规范,需要：GET或POST或DELETE方法")
	} else {
		c.checkToolNodeCallingMethodType(nodeID, node)
	}

}

func isValidProtocol(apiURL string) bool {
	return strings.HasPrefix(strings.ToLower(apiURL), APIProtocolHTTP) ||
		strings.HasPrefix(strings.ToLower(apiURL), APIProtocolHTTPS)
}

func (c *WfContext) checkToolNodeHeader(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetToolNodeData()
	maxCount := config.GetMainConfig().VerifyWorkflow.NodeToolMaxCount
	log.InfoContextf(c.ctx, "checkToolNodeHeader len:%d", len(node.GetHeader()))
	if len(node.GetHeader()) > maxCount {
		log.WarnContextf(c.ctx, "checkToolNodeHeader len=%d|maxCount=%d", len(node.GetHeader()), maxCount)
		c.appendNodeError(nodeID, fmt.Sprintf("工具节点Header数量超过%d限制", maxCount))
	}

	duplicateHeaderKey := make(map[string]struct{})
	for _, header := range node.GetHeader() {
		// 校验同级参数名称是否重复
		c.checkToolNodeParamNameDuplicate(nodeID, duplicateHeaderKey, header)
		// 校验参数及子参数
		c.checkToolNodeSubItem(nodeID, header.GetIsRequired(), header, wfn.GetNodeType(), false)
	}
}

func (c *WfContext) checkToolNodeQuery(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetToolNodeData()
	maxCount := config.GetMainConfig().VerifyWorkflow.NodeToolMaxCount
	log.InfoContextf(c.ctx, "checkToolNodeQuery len:%d", len(node.GetQuery()))
	if len(node.GetQuery()) > maxCount {
		log.WarnContextf(c.ctx, "checkToolNodeQuery len=%d|maxCount=%d", len(node.GetQuery()), maxCount)
		c.appendNodeError(nodeID, fmt.Sprintf("工具节点Query数量超过%d限制", maxCount))
	}

	duplicateHeaderKey := make(map[string]struct{})
	for _, query := range node.GetQuery() {
		// 校验同级参数名称是否重复
		c.checkToolNodeParamNameDuplicate(nodeID, duplicateHeaderKey, query)
		// 校验参数及子参数
		c.checkToolNodeSubItem(nodeID, query.GetIsRequired(), query, wfn.GetNodeType(), false)
	}
}

func (c *WfContext) checkToolNodeBody(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	node := wfn.GetToolNodeData()
	maxCount := config.GetMainConfig().VerifyWorkflow.NodeToolMaxCount
	log.InfoContextf(c.ctx, "checkToolNodeBody len:%d", len(node.GetBody()))
	if len(node.GetBody()) > maxCount {
		log.WarnContextf(c.ctx, "checkToolNodeBody len=%d|maxCount=%d", len(node.GetBody()), maxCount)
		c.appendNodeError(nodeID, fmt.Sprintf("工具节点Body数量超过%d限制", maxCount))
	}

	duplicateHeaderKey := make(map[string]struct{})
	for _, body := range node.GetBody() {
		// 校验同级参数名称是否重复
		c.checkToolNodeParamNameDuplicate(nodeID, duplicateHeaderKey, body)
		// 校验参数及子参数
		c.checkToolNodeSubItem(nodeID, body.GetIsRequired(), body, wfn.GetNodeType(), false)
	}
}

// checkToolNodeParamNameDuplicate 检验参数名是否重复
func (c *WfContext) checkToolNodeParamNameDuplicate(nodeID string, duplicateHeaderKey map[string]struct{},
	header *KEP_WF.ToolNodeData_RequestParam) {
	_, ok := duplicateHeaderKey[header.GetParamName()]
	if ok {
		log.WarnContextf(c.ctx, "checkToolNodeParamNameDuplicate duplicateParamName:%s",
			header.GetParamName())
		c.appendNodeError(nodeID, "参数名重复:"+header.GetParamName())
	}
	duplicateHeaderKey[header.GetParamName()] = struct{}{}
}

func (c *WfContext) checkToolNodeSubItem(nodeID string, isRequired bool,
	header *KEP_WF.ToolNodeData_RequestParam, nodeType KEP_WF.NodeType, isPlugin bool) {
	duplicateHeaderKey := make(map[string]struct{})
	maxSubDepth := config.GetMainConfig().VerifyWorkflow.NodeToolSubMaxDepth
	// 校验参数深度
	subDepth := c.getToolNodeDepth(header)
	subIsRequired := 0
	log.InfoContextf(c.ctx, "checkToolNodeSubItem subDepth:%d", subDepth)
	if subDepth > maxSubDepth {
		log.WarnContextf(c.ctx, "checkToolNodeSubItem subDepth %d is over limit maxSubDepth:%d",
			subDepth, maxSubDepth)
		c.appendNodeError(nodeID, fmt.Sprintf("参数层级超最大限制%d", maxSubDepth))
	}
	// 校验每一行参数相关信息（名称，类型）
	c.checkToolNodeDataParam(nodeID, header, nodeType)

	if len(header.GetSubParams()) > 0 {
		if !(isPlugin && (header.GetParamType() == KEP_WF.TypeEnum_OBJECT ||
			header.GetParamType() == KEP_WF.TypeEnum_ARRAY_STRING ||
			header.GetParamType() == KEP_WF.TypeEnum_ARRAY_INT ||
			header.GetParamType() == KEP_WF.TypeEnum_ARRAY_FLOAT ||
			header.GetParamType() == KEP_WF.TypeEnum_ARRAY_BOOL ||
			header.GetParamType() == KEP_WF.TypeEnum_ARRAY_OBJECT)) {
			for _, subHeader := range header.GetSubParams() {
				// 子级同级重复名称检测
				c.checkToolNodeParamNameDuplicate(nodeID, duplicateHeaderKey, subHeader)
				if subHeader.GetIsRequired() {
					subIsRequired += 1
				}
				c.checkToolNodeSubItem(nodeID, header.GetIsRequired(), subHeader, nodeType, isPlugin)
			}
			if isRequired && subIsRequired == 0 {
				c.appendNodeError(nodeID, fmt.Sprintf("%s 参数必填，其子参数必有至少一项必填", header.GetParamName()))
			}
		}
	}
}

// getToolNodeDepth 获取工具节点参数的层级
func (c *WfContext) getToolNodeDepth(headerParam *KEP_WF.ToolNodeData_RequestParam) int {
	var depth int
	if headerParam == nil {
		return 0
	}
	if headerParam.GetSubParams() == nil || len(headerParam.GetSubParams()) == 0 {
		return 1
	}
	depth += 1
	var subDepth int
	for _, sub := range headerParam.GetSubParams() {
		temp := c.getToolNodeDepth(sub)
		if temp > subDepth {
			// 拿到最深的
			subDepth = temp
		}
	}
	depth += subDepth
	return depth
}

// checkToolNodeDataParam 校验Param
func (c *WfContext) checkToolNodeDataParam(nodeID string, header *KEP_WF.ToolNodeData_RequestParam, nodeType KEP_WF.NodeType) {
	if len(strings.TrimSpace(header.GetParamName())) == 0 {
		//if header.GetIsRequired() {
		log.WarnContextf(c.ctx, "checkToolNodeDataParam isRequired:%+v|ParamName is empty",
			header.GetIsRequired())
		c.appendNodeError(nodeID, "参数名不能为空")
		//}
	} else {
		if !isValidJSONField(header.GetParamName()) {
			log.WarnContextf(c.ctx, "checkToolNodeDataParam isValidJSONField|ParamName:%s",
				header.GetParamName())
			c.appendNodeError(nodeID, fmt.Sprintf("%s参数名称不符合", header.GetParamName()))
		}
	}
	// 校验参数名称字数最大限制
	nameMaxLen := config.GetMainConfig().VerifyWorkflow.NodeToolParamNameMax
	if len([]rune(header.GetParamName())) > nameMaxLen {
		log.WarnContextf(c.ctx, "checkToolNodeDataParam|ParamName.len=%d|nameMaxLen=%d",
			len([]rune(header.GetParamName())), nameMaxLen)
		c.appendNodeError(nodeID, fmt.Sprintf("Header名超过最大限制%d", nameMaxLen))
	}
	switch header.GetParamType() {
	case KEP_WF.TypeEnum_OBJECT, KEP_WF.TypeEnum_ARRAY_STRING, KEP_WF.TypeEnum_ARRAY_INT,
		KEP_WF.TypeEnum_ARRAY_FLOAT, KEP_WF.TypeEnum_ARRAY_BOOL, KEP_WF.TypeEnum_ARRAY_OBJECT:
		if header.GetInput().GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT && len(header.GetSubParams()) == 0 {
			log.WarnContextf(c.ctx, "checkToolNodeDataParam ParamType:%s|SubHeaderLen:%d",
				header.GetParamType(), len(header.GetSubParams()))
			c.appendNodeError(nodeID, fmt.Sprintf("%s 参数为%s类型，其子参数至少一项必填",
				header.GetParamName(), header.GetParamType()))
		}
	case KEP_WF.TypeEnum_STRING, KEP_WF.TypeEnum_INT, KEP_WF.TypeEnum_FLOAT, KEP_WF.TypeEnum_BOOL,
		KEP_WF.TypeEnum_FILE, KEP_WF.TypeEnum_DOCUMENT, KEP_WF.TypeEnum_IMAGE, KEP_WF.TypeEnum_AUDIO:
		if len(header.GetSubParams()) > 0 {
			log.WarnContextf(c.ctx, "checkToolNodeDataParam ParamType:%s|SubHeaderLen:%d",
				header.GetParamType(), len(header.GetSubParams()))
			c.appendNodeError(nodeID, fmt.Sprintf("%s 参数为%s类型，不能存在子参数",
				header.GetParamName(), header.GetParamType()))
		}
		// 校验输入框值
		if header.GetIsRequired() {
			// 必填参数直接进入校验
			c.checkInputAndCollect(nodeID, header.GetInput())
		} else {
			// 非必填参数，当输入类型为“用户输入”且用户输入值为空时，不校验
			if header.GetInput() == nil {
				// 没有Input字段直接返回，同checkInputAndCollect函数，避免panic
				return
			}
			isUserInput := header.GetInput().GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT
			if !isUserInput {
				// 非用户输入直接进checkInputAndCollect函数校验
				c.checkInputAndCollect(nodeID, header.GetInput())
				return
			}
			if header.GetInput().GetUserInputValue() == nil {
				// 输入类型为“用户输入”，但没有UserInputValue字段，认为输入为空，直接返回，避免panic
				return
			}
			isUserInputValueEmpty := len(header.GetInput().GetUserInputValue().GetValues()) == 0
			if isUserInputValueEmpty {
				return
			} else if isUserInput && !isUserInputValueEmpty {
				// 检查是否所有值都是空，前端可能会传 [""] 这种情况
				isAllValueEmpty := true
				for _, value := range header.GetInput().GetUserInputValue().GetValues() {
					if len(value) != 0 {
						isAllValueEmpty = false
						break
					}
				}
				if isAllValueEmpty {
					return
				}
			}
			//  非必填参数，当输入类型为“用户输入”且用户输入值不为空时，校验
			c.checkInputAndCollect(nodeID, header.GetInput())
		}
	default:
		c.appendNodeError(nodeID, fmt.Sprintf("%s 参数为%s类型，不存在该类型",
			header.GetParamName(), header.GetParamType()))
	}
}

// isValidJSONField API节点出入参中 JSON字段合法性的正则
func isValidJSONField(str string) bool {
	rex := config.GetMainConfig().VerifyWorkflow.NodeToolValidJSONFieldRex
	if len(rex) == 0 {
		// 允许数字开头
		// 因为标准的json中的key允许是数字开头的，再者说有些第三方API没准儿协议中的入参真有数字开头的，所以保留
		rex = `^[a-zA-Z0-9_]+$`
		// 不允许数字开头
		//rex = `^[a-zA-Z_]+[a-zA-Z0-9_]*$`
	}

	//for _, c := range str {
	//	// 是否在 ASCII 可打印字符范围内 https://ascii-code-table.nxm.ro/
	//	if c < 0x20 || c > 0x7E {
	//		return false
	//	}
	//}
	return regexp.MustCompile(rex).MatchString(str)
}

func (c *WfContext) checkToolNodeCustomApiAuth(nodeID string, node *KEP_WF.ToolNodeData) {
	switch node.GetAPI().GetAuthType() {
	case KEP_WF.ToolNodeData_NONE:
		// nothing
	case KEP_WF.ToolNodeData_API_KEY:
		if node.GetAPI().GetKeyLocation() != KEP_WF.ToolNodeData_HEADER &&
			node.GetAPI().GetKeyLocation() != KEP_WF.ToolNodeData_QUERY {
			c.appendNodeError(nodeID, "密钥位置不符合规范")
		}
		if len(node.GetAPI().GetKeyParamName()) == 0 {
			c.appendNodeError(nodeID, "密钥参数名为空")
		}
		if len(node.GetAPI().GetKeyParamValue()) == 0 {
			c.appendNodeError(nodeID, "密钥值为空")
		}
	default:
		c.appendNodeError(nodeID, "授权方式不符合规范")
	}
}

func (c *WfContext) checkToolNodeCallingMethodType(nodeID string, node *KEP_WF.ToolNodeData) {
	if node.GetAPI().GetMethod() == APIMethodGet || node.GetAPI().GetMethod() == APIMethodPost {
		switch node.GetAPI().GetCallingMethod() {
		case KEP_WF.ToolNodeData_NON_STREAMING:
			// nothing
		case KEP_WF.ToolNodeData_STREAMING:
			// nothing
		default:
			c.appendNodeError(nodeID, "调用方式不符合规范")
		}
	}
}

func (c *WfContext) checkToolNodeRsp(wfn *KEP_WF.WorkflowNode) {
	nodeID := wfn.GetNodeID()
	depth := c.getToolNodeMaxResponseDepth(wfn)
	maxDepth := config.GetMainConfig().VerifyWorkflow.NodeToolRespMaxDepth
	log.InfoContextf(c.ctx, "checkToolNodeRsp depth len:%d|maxDepth len:%d", depth, maxDepth)
	if depth > maxDepth {
		c.appendNodeError(nodeID, fmt.Sprintf("参数最大深度不能超过%d", maxDepth))
	}
}

func (c *WfContext) getToolNodeMaxResponseDepth(wfn *KEP_WF.WorkflowNode) int {
	if len(wfn.GetOutputs()) == 0 {
		return 0
	}

	// Find max depth among all Response parameters
	maxDepth := 0
	for _, respParam := range wfn.GetOutputs() {
		c.checkToolNodeAnalysisMethod(wfn.GetNodeID(), respParam)
		depth := c.getToolNodeResponseDepth(wfn.GetNodeID(), respParam)
		if depth > maxDepth {
			maxDepth = depth
		}
	}

	return maxDepth
}

func (c *WfContext) getToolNodeResponseDepth(nodeID string, param *KEP_WF.OutputParam) int {
	if param == nil {
		return 0
	}
	if len(param.GetProperties()) == 0 {
		return 1
	}

	maxSubDepth := 0
	for _, subParam := range param.GetProperties() {
		c.checkToolNodeAnalysisMethod(nodeID, subParam)
		subDepth := c.getToolNodeResponseDepth(nodeID, subParam)
		if subDepth > maxSubDepth {
			maxSubDepth = subDepth
		}
	}

	return maxSubDepth + 1
}

func (c *WfContext) checkToolNodeAnalysisMethod(nodeID string, outputParam *KEP_WF.OutputParam) {
	switch outputParam.GetAnalysisMethod() {
	case KEP_WF.AnalysisMethodTypeEnum_COVER:
		// nothing
	case KEP_WF.AnalysisMethodTypeEnum_INCREMENT:
		// nothing
	default:
		c.appendNodeError(nodeID, "解析方式不符合规范")
	}
}
