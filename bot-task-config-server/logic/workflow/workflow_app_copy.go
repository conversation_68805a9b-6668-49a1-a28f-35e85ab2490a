// bot-task-config-server
//
// @(#)workflow_app_copy.go  星期四, 六月 05, 2025
// Copyright(c) 2025, mikeljiang@Tencent. All rights reserved.

package workflow

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/db"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	admin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
)

// CopyWorkflowByApp 复制指定应用下的工作流到另一个应用下
func CopyWorkflowByApp(ctx context.Context, req *KEP_WF.CopyWorkflowByAppReq) (
	*KEP_WF.CopyWorkflowByAppRsp, error) {
	rsp := &KEP_WF.CopyWorkflowByAppRsp{}
	staffID := util.StaffID(ctx)
	appBizId, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	robotInfo, err := rpc.GetAppInfo(ctx, 1, appBizId)
	if err != nil {
		log.WarnContext(ctx, "CopyWorkflowByApp rpc.GetAppInfo appBizId:%d|err=%+v|", appBizId, err)
		return nil, errors.ErrRobotNotFound
	}
	if len(req.GetOriginalAppBizId()) == 0 {
		log.WarnContextf(ctx, "CopyWorkflowByApp req.GetOriginalAppBizId is empty")
		return nil, errors.BadWorkflowReqError("请输入原始应用ID")
	}
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	// 1.变量复制处理
	copyVarList, err := copyOldVarToNewVar(ctx, req, uin, subUin)
	if err != nil {
		return nil, err
	}
	rsp.CopyVarList = copyVarList
	// 2.工作流复制处理，提前判断是否有工作流需要复制，不需要就直接返回；需要就创建异步任务
	workflowList, err := db.GetWorkflowIdsByAppID(ctx, req.GetOriginalAppBizId())
	if err != nil {
		return nil, err
	}
	if len(workflowList) == 0 {
		log.InfoContextf(ctx, "CopyWorkflowByApp db.GetWorkflowIdsByAppID workflowList is empty")
		// 同UIN跨应用复制工作流完成回调通知admin,失败重试三次
		request := &admin.CopyAppCallbackReq{
			Type:     admin.CopyAppCallbackType_Workflow,
			AppBizId: appBizId,
		}
		for i := 0; i < 3; i++ {
			_, err := rpc.CopyAppCallback(ctx, request)
			if err == nil {
				log.InfoContextf(ctx, "CopyWorkflowByApp rpc.CopyAppCallback success")
				break
			}
			log.WarnContextf(ctx, "CopyWorkflowByApp rpc.CopyAppCallback failed")
		}
		log.InfoContextf(ctx, "CopyWorkflowByApp rpc.CopyAppCallback success")
		return rsp, nil
	}
	var newWorkflowId string
	taskId := idgenerator.NewInt64ID()
	// 创建复制原始应用下的工作流到当前应用下的任务
	taskParams := entity.TaskCopyAppWorkflowParams{
		RequestID:       util.RequestID(ctx),
		CorpID:          robotInfo.GetCorpId(),
		StaffID:         staffID,
		RobotID:         req.GetAppBizId(),
		OriginalRobotID: req.GetOriginalAppBizId(),
		Uin:             uin,
		SubUin:          subUin,
		TaskID:          uint64(taskId),
	}
	// 根据admin请求参数，判断是否需要指定单工作流模式下替换后的工作流ID
	if len(req.GetWorkflowId()) != 0 {
		newWorkflowId = idgenerator.NewUUID()
		taskParams.WorkflowID = req.GetWorkflowId()
		taskParams.NewWorkflowID = newWorkflowId
	}
	// 3.创建任务调度
	if err := scheduler.NewImportWorkflowByAPPIDTask(ctx, req.GetAppBizId(), taskParams); err != nil {
		return rsp, err
	}

	//// 4.创建同UIN下跨应用复制记录
	//paramJSON, _ := jsoniter.MarshalToString(req)
	//importId := strconv.FormatInt(taskId, 10)
	//experienceTask := &entity.WorkflowImport{
	//	ImportID: importId,
	//	RobotID:  req.GetAppBizId(),
	//	Params:   paramJSON,
	//	Status:   entity.ImportWorkflowStatusProcessing,
	//	Uin:      uin,
	//	SubUin:   subUin,
	//}
	//db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	//if err := db.Table(entity.WorkflowImport{}.TableName()).Create(experienceTask).Error; err != nil {
	//	return rsp, nil
	//}
	rsp.WorkflowId = newWorkflowId
	//rsp.TaskId = importId
	return rsp, nil
}

// copyOldVarToNewVar 将老应用的变量复制到新应用下
func copyOldVarToNewVar(ctx context.Context, req *KEP_WF.CopyWorkflowByAppReq, uin, subUin string) (
	[]*KEP_WF.CopyVar, error) {
	// 获取原应用变量
	mapOldVarParams, err := db.GetVarParamsInfosByAppID(ctx, req.GetOriginalAppBizId())
	if err != nil {
		return nil, err
	}
	// 获取新应用变量
	mapNewVarParams, err := db.GetVarParamsInfosByAppID(ctx, req.GetAppBizId())
	if err != nil {
		return nil, err
	}
	// 找出需要新增的变量
	//varIdMap := make(map[string]string)
	list := make([]*KEP_WF.CopyVar, 0)
	varParams := make([]*entity.VarParams, 0)
	for oldVarName, oldVar := range mapOldVarParams {
		newVar, ok := mapNewVarParams[oldVarName]
		var newVarID string
		if ok {
			newVarID = newVar.VarID
		} else {
			newVarID = idgenerator.NewUUID()
			varParam := &entity.VarParams{
				VarID:              newVarID,
				VarName:            oldVar.VarName,
				VarDesc:            oldVar.VarDesc,
				VarType:            oldVar.VarType,
				VarDefaultValue:    oldVar.VarDefaultValue,
				VarDefaultFileName: oldVar.VarDefaultFileName,
				AppID:              req.GetAppBizId(),
				UIN:                uin,
				SubUIN:             subUin,
				ReleaseStatus:      entity.ReleaseStatusUnPublished,
				Action:             entity.ActionInsert,
			}
			varParams = append(varParams, varParam)
		}
		//varIdMap[oldVar.VarID] = newVarID
		copyVar := KEP_WF.CopyVar{
			NewVarId:      newVarID,
			OriginalVarId: oldVar.VarID,
		}
		list = append(list, &copyVar)
	}
	// 创建所需的变量
	if err := db.ImportVarForWorkflow(ctx, varParams, req.GetAppBizId()); err != nil {
		log.WarnContextf(ctx, "copyOldVarToNewVar db.ImportVarForWorkflow err:%+v", err)
		return nil, err
	}
	log.InfoContextf(ctx, "copyOldVarToNewVar success")
	return list, nil
}

//// GetCopyAPPWorkflowStatusByTaskId 查询跨应用工作流复制状态
//func GetCopyAPPWorkflowStatusByTaskId(ctx context.Context, req *KEP_WF.GetCopyAPPWorkflowStatusByTaskIdReq) (
//	*KEP_WF.GetCopyAPPWorkflowStatusByTaskIdRsp, error) {
//	rsp := &KEP_WF.GetCopyAPPWorkflowStatusByTaskIdRsp{}
//	appBizId, err := util.CheckReqBotBizIDUint64(ctx, req.GetAppBizId())
//	if err != nil {
//		return nil, err
//	}
//	_, err = rpc.GetAppInfo(ctx, 1, appBizId)
//	if err != nil {
//		log.WarnContext(ctx, "GetCopyAPPWorkflowStatusByTaskId rpc.GetAppInfo "+
//			"appBizId:%d|err=%+v|", appBizId, err)
//		return nil, errors.ErrRobotNotFound
//	}
//	if req.GetTaskId() == "" {
//		rsp.Status = entity.ImportWorkflowStatusProcessed
//		return rsp, nil
//	}
//	// 查询导入任务信息
//	workflowImport, err := db.GetWorkflowImportByID(ctx, req.GetTaskId())
//	if err != nil {
//		return rsp, nil
//	}
//	if workflowImport.ImportID == "" {
//		rsp.Status = entity.ImportWorkflowStatusProcessed
//		return rsp, nil
//	}
//	rsp.Status = workflowImport.Status
//	return rsp, nil
//}
