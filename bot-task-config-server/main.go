// KEP.bot-task-config-server
//
// @(#)main.go  December 07, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.

package main

import (
	"fmt"

	"gopkg.in/yaml.v3"

	"git.code.oa.com/trpc-go/trpc-database/timer"
	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/admin"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-metrics-prometheus"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/scheduler"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/filter"
	_ "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/bottask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/cron"
	workflowCron "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/cron/workflow"
	pdlSyncTask "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/pdl/synctask"
	taskFlowSynctask "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/synctask"
	workflowSynctask "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/logic/workflow/synctask"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/service"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/cos"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/idgenerator"

	_ "git.woa.com/dialogue-platform/common/v3/filters/i18n"
	"git.woa.com/dialogue-platform/go-comm/clues"
	"git.woa.com/dialogue-platform/go-comm/encode"
	"git.woa.com/dialogue-platform/go-comm/panicprinter"
	scurlcomm "git.woa.com/dialogue-platform/go-comm/rainbow-comm/scurl"
	"git.woa.com/dialogue-platform/go-comm/runtime0"
	"git.woa.com/dialogue-platform/go-comm/trpc0"
	_ "git.woa.com/dialogue-platform/go-comm/trpc0"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP"
	_ "git.woa.com/galileo/trpc-go-galileo"
)

const (
	// 编译版本号, 流水线会注入相关信息
	buildVersion = "服务编译版本号, 勿动~~"
)

var (
	// trpc logic: trpc.KEP.bot-task-config-server.task-config
	taskConfigService = fmt.Sprintf("trpc.KEP.%s.task-config", trpc0.DungeonForServer("bot-task-config-server"))
)

func init() {
	clues.Init()
}

// Init 初始化
func Init() {
	// config
	if err := config.Init(); err != nil {
		panic(err)
	}
	scurlcomm.Init()

	// database
	if err := database.Init(); err != nil {
		panic(err)
	}

	// id generator
	if err := idgenerator.Init(); err != nil {
		panic(err)
	}
	// cos
	cos.Init()
}

func main() {
	defer panicprinter.PrintPanic()
	runtime0.SetServerVersion(buildVersion)
	runtime0.PrintVersion()
	filter.Init()

	s := trpc.NewServer() // 启动一个服务实例
	// router0.Init()        // 连接类资源路由表加载

	// 注册分布式调度器
	timer.RegisterScheduler(util.TimerScheduler, cron.NewScheduler())

	// 初始化
	Init()
	// 运行调度任务
	if err := scheduler.RunTask(); err != nil {
		log.Errorf("run task fail, err: %v", err)
		return
	}

	// 同步任务定时检查
	taskFlowSynctask.CheckUnfinishedTask() // 旧版本
	workflowSynctask.CheckUnfinishedTask() // 新版本
	pdlSyncTask.CheckUnfinishedTask()      // PDL

	// 异步工作流定时检查未执行运行实例并重试
	workflowCron.CheckPendingWorkflowRun()
	workflowCron.RetryPendingWorkflowRun()

	// 注册一个对应沟通协议的服务
	svc := s.Service(taskConfigService)
	if svc == nil {
		log.Fatalf("trpc_go.yaml 中缺少 logic: %s 的配置", taskConfigService)
		// 这里不结束了, 等着下面的代码报 nil pointer deference panic
	}
	svr := &service.TaskConfigImp{}
	KEP.RegisterTaskConfigService(svc, svr)

	// 分布式定时服务
	timer.RegisterHandlerService(s.Service(util.TimerCheckTaskFlowMigration),
		cron.NewTimerService().CheckTaskFlowMigration)
	// 刷工作流向量数据
	workflowCron.StartRefreshWorkflowVectorJob()

	// 删除已删除示例问法的向量数据
	cron.StartDelExampleVectorJob()
	// 刷示例问法向量数据
	cron.StartRefreshIntentExampleCronJob()
	// 刷example数据
	cron.StartRefreshCronJob()
	// [升级对话树]
	cron.StartUpgradeCronJob()

	//
	cron.StartSyncEntityEntriesToRedisCronJob()

	logConfig()

	admin.HandleFunc("/v260/check-workflow-json", svr.CheckWorkflowJson)
	admin.HandleFunc("/v262/sync-workflow-example-rds", svr.SyncWorkflowExampleToRds)
	admin.HandleFunc("/v270/add-end-node", svr.AddEndNode)                        // [v2.7.0]刷数据 - 结束节点
	admin.HandleFunc("/v270/sync-workflow-enable", svr.SyncWorkflowEnableToRedis) // [v2.7.0]刷数据 - 同步可用状态到redis
	admin.HandleFunc("/v270/sync-example-vector", svr.SyncWorkflowExamToVector)   // [v2.7.0]刷数据 - 同步示例问法到vector
	admin.HandleFunc("/v270/check-workflow-enable", svr.CheckWorkflowEnable)      // [v2.7.1]检查-workflow可用状态在db、redis、vector是否一致
	admin.HandleFunc("/v275/populate-node-data", svr.PopulateNodeData)            // [v2.7.5]刷数据 - 将应用配置中的 生成模型刷到参数提取节点、搜索策略刷到知识检索节点和大模型问答节点
	admin.HandleFunc("/v285/iteration-node-data", svr.IterationNodeData)          // [v2.8.5]刷数据 - 循环节点历史数据Input和Input.Index数据进行处理
	admin.HandleFunc("/v290/knowledge-node-data", svr.KnowledgeNodeData)          // [v2.9.0]刷数据 - 知识检索节点&大模型知识问答节点 适配共享知识库

	// 向量库升级相关接口
	admin.HandleFunc("/embedding/create-upgrade-task", svr.UpgradeWorkflowVector)
	admin.HandleFunc("/embedding/get-upgrade-task", svr.GetUpgradeWorkflowVectorTask)
	admin.HandleFunc("/embedding/get-upgrade-tasks", svr.GetUpgradeWorkflowVectorTasks)
	admin.HandleFunc("/embedding/restart-upgrade-task", svr.RestartUpgradeWorkflowVector)

	if err := s.Serve(); err != nil {
		log.Fatal(err)
	}
}

func logConfig() {
	// log
	cfg := trpc.GlobalConfig()
	log.Info("\n-------------------------------------------------------------------------------")
	g0, _ := yaml.Marshal(cfg.Global)
	log.Infof("\nGlobal:\n%v", encode.String(g0))
	log.Info("\n-------------------------------------------------------------------------------")
	s0, _ := yaml.Marshal(cfg.Server)
	log.Infof("\nServer:\n%v", encode.String(s0))
	log.Info("\n-------------------------------------------------------------------------------")
	c0, _ := yaml.Marshal(cfg.Client)
	log.Infof("\nClient:\n%v", encode.String(c0))
	log.Info("\n-------------------------------------------------------------------------------")
	p0, _ := yaml.Marshal(cfg.Plugins)
	log.Infof("\nPlugins:\n%v", encode.String(p0))
	log.Info("\n===============================================================================")
}
