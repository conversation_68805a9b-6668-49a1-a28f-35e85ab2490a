-- 增加变量默认值文件名称字段(沙箱和正式环境都要执行)---老数据库---taskFlow
ALTER TABLE `t_var`
    ADD COLUMN `f_var_default_file_name` varchar(2000) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '变量默认值文件名称' AFTER `f_var_type`;

-- 增加变量默认值字段(沙箱和正式环境都要执行)---老数据库---taskFlow
ALTER TABLE `t_var`
    ADD COLUMN `f_var_default_value` varchar(2000) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '变量默认值' AFTER `f_var_type`;

-- 工作流文档关联关系表增加索引(当前只需要在沙箱环境执行)---新数据库---workFlow
ALTER TABLE `t_workflow_ref_knowledge`
    ADD INDEX index_biz_id(`f_biz_id`);
ALTER TABLE `t_workflow_ref_knowledge`
    ADD INDEX index_label_id(`f_label_id`);
ALTER TABLE `t_workflow_ref_knowledge`
    ADD INDEX index_type_id(`f_type`);
