package entity

import (
	"fmt"
	"time"

	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/go-comm/encode"
)

const (
	// TaskMutexWorkflowNone 无互斥
	TaskMutexWorkflowNone task_scheduler.TaskMutex = 10

	// TaskExportWorkflow 导出工作流任务
	TaskExportWorkflow task_scheduler.TaskType = 11
	// TaskImportWorkflowParent 导入工作流父任务
	TaskImportWorkflowParent task_scheduler.TaskType = 12
	// TaskImportWorkflow 导入工作流子任务
	TaskImportWorkflow task_scheduler.TaskType = 13
	// TaskImportWfExample 导入工作流示例问法
	TaskImportWfExample task_scheduler.TaskType = 15
	// TaskImportExperienceWorkflow 复制体验中心应用工作流
	TaskImportExperienceWorkflow task_scheduler.TaskType = 21
	// TaskImportWorkflowByAPPID 复制应用A的工作流到应用B（同UIN下跨应用复制）
	TaskImportWorkflowByAPPID task_scheduler.TaskType = 25
)

var (
	// WorkflowTaskTypeNameMap 任务类型名称映射, 用于在配置文件中配置 任务名 时寻找对应的 任务类型
	WorkflowTaskTypeNameMap = map[task_scheduler.TaskType]string{
		TaskExportWorkflow:           "export_workflow",
		TaskImportWorkflowParent:     "import_workflow_parent",
		TaskImportWorkflow:           "import_workflow",
		TaskImportWfExample:          "import_workflow_example",
		TaskImportExperienceWorkflow: "import_experience_workflow",
	}
)

// ExportWorkflow 导出工作流
type ExportWorkflow struct {
	WorkflowID   string `gorm:"column:f_workflow_id" json:"workflow_id"`       // 工作流ID
	WorkflowName string `gorm:"column:f_workflow_name" json:"workflow_name"`   // 工作流名称
	WorkflowDesc string `gorm:"column:f_desc" json:"workflow_desc"`            // 工作流状态
	DialogJson   string `gorm:"column:f_dialog_json_draft" json:"dialog_json"` // 对话树json字段草稿
}

// ExportWorkflowRef 导出工作流引用
type ExportWorkflowRef struct {
	WorkflowID       string `gorm:"column:f_workflow_id" json:"workflow_id"`         // 工作流ID
	NodeID           string `gorm:"column:f_node_id" json:"node_id"`                 // 工作流节点ID
	WorkflowRefID    string `gorm:"column:f_workflow_ref_id" json:"workflow_ref_id"` // 工作流引用ID
	WorkflowRefNewID string `json:"workflow_ref_new_id"`                             // 工作流引用最新ID
	WorkflowRefName  string `json:"workflow_ref_name"`                               // 工作流引用名称
}

// ExportWorkflowExample 导出示例问法
type ExportWorkflowExample struct {
	FlowID    string `gorm:"column:f_workflow_id" json:"flow_id"`   // 工作流ID
	ExampleID string `gorm:"column:f_example_id" json:"example_id"` // 示例ID
	Example   string `gorm:"column:f_example" json:"example"`       // 示例问法
}

// ExportWorkflowVar 导出变量
type ExportWorkflowVar struct {
	FlowID             string `gorm:"column:f_workflow_id" json:"flow_id"`
	VarID              string `gorm:"column:f_var_id" json:"var_id"`
	VarName            string `gorm:"column:f_var_name" json:"var_name"`
	VarDesc            string `gorm:"column:f_var_desc" json:"var_desc"`
	VarType            string `gorm:"column:f_var_type" json:"var_tye"`
	VarDefaultValue    string `gorm:"column:f_var_default_value" json:"var_default_value"`
	VarDefaultFileName string `gorm:"column:f_var_default_file_name" json:"var_default_file_name"`
}

// ExportWorkflowPDL 导出工作流PDL
type ExportWorkflowPDL struct {
	WorkflowID    string `gorm:"column:f_workflow_id" json:"workflow_id"`        // 工作流ID
	Constraints   string `gorm:"column:f_user_constraints" json:"constraints"`   // PDL的约束
	DialogJson    string `gorm:"column:f_dialog_json_enable" json:"dialog_json"` // 画布PDLJson
	ParameterJson string `gorm:"column:f_parameter" json:"parameter_json"`       // 参数PDLJson
	ContentJson   string `gorm:"column:f_pdl_content" json:"content_json"`       // 内容PDLJson
	ToolJson      string `gorm:"column:f_tools_info" json:"tool_json"`           // 工具信息PDLJson
}

// ParamMigrationInfo 参数迁移信息 -- 导入/导出使用
type ParamMigrationInfo struct {
	FlowID           string `gorm:"column:f_workflow_id" json:"flow_id"`
	NodeID           string `gorm:"column:f_node_id" json:"node_id"`                      // 节点ID
	NodeName         string `gorm:"column:f_node_name" json:"node_name"`                  // 节点名称
	ParamID          string `gorm:"column:f_parameter_id" json:"param_id"`                // 参数ID
	ParamName        string `gorm:"column:f_parameter_name" json:"param_name"`            // 参数名称
	ParamDesc        string `gorm:"column:f_parameter_desc" json:"param_desc"`            // 参数描述
	ParamType        string `gorm:"column:f_parameter_type" json:"param_type"`            // 参数类型
	CorrectExample   string `gorm:"column:f_correct_examples" json:"correct_example"`     // 正确示例
	IncorrectExample string `gorm:"column:f_incorrect_examples" json:"incorrect_example"` // 错误示例
}

// ConvertToDBCreateInfo 转换为数据库结构体用于新增数据
func (p *ParamMigrationInfo) ConvertToDBCreateInfo(appBizID, uin, subUin string) (
	*Parameter, *WorkflowParameter) {
	param := &Parameter{
		ParameterID:       p.ParamID,
		ParameterName:     p.ParamName,
		ParameterDesc:     p.ParamDesc,
		ParameterType:     p.ParamType,
		ReleaseStatus:     ReleaseStatusUnPublished,
		Action:            ActionInsert,
		AppID:             appBizID,
		UIN:               uin,
		SubUIN:            subUin,
		CorrectExamples:   p.CorrectExample,
		IncorrectExamples: p.IncorrectExample,
		CustomAsk:         "",
		CustomAskEnable:   false,
	}

	wp := &WorkflowParameter{
		AppBizID:      appBizID,
		WorkFlowID:    p.FlowID,
		NodeID:        p.NodeID,
		NodeName:      p.NodeName,
		ParameterID:   param.ParameterID,
		ReleaseStatus: ReleaseStatusUnPublished,
		Action:        ActionInsert,
	}

	return param, wp
}

// WorkflowExportParams 工作流导出参数
type WorkflowExportParams struct {
	RequestID string   `json:"request_id"` // 请求ID
	Name      string   `json:"name"`       // 任务名称
	CorpID    uint64   `json:"corp_id"`    // 企业ID
	StaffID   uint64   `json:"staff_id"`   // 员工ID
	RobotID   string   `json:"robot_id"`   // 机器人ID
	ExportID  uint64   `json:"export_id"`  // 导出ID
	FlowIDs   []string `json:"flow_ids"`   // 工作流ID
	Platform  string   `json:"platform"`   // 导出平台
}

// WorkflowImportParentParams 工作流程导入父任务
type WorkflowImportParentParams struct {
	RequestID string `json:"request_id"` // 请求ID
	Name      string `json:"name"`       // 任务名称
	CorpID    uint64 `json:"corp_id"`    // 企业ID
	StaffID   uint64 `json:"staff_id"`   // 员工ID
	RobotID   string `json:"robot_id"`   // 机器人ID
	ImportID  string `json:"import_id"`  // 导入ID
	FileName  string `json:"file_name"`  // 导入文件名
}

// WorkflowImportParams 工作流程导入子任务
type WorkflowImportParams struct {
	RequestID      string `json:"request_id"`       // 请求ID
	Name           string `json:"name"`             // 任务名称
	CorpID         uint64 `json:"corp_id"`          // 企业ID
	StaffID        uint64 `json:"staff_id"`         // 员工ID
	RobotID        string `json:"robot_id"`         // 机器人ID
	ParentImportID string `json:"parent_import_id"` // 导入父ID
	ImportID       string `json:"import_id"`        // 导入ID
	FileName       string `json:"file_name"`        // 导入文件名
}

// WorkflowSchedulerTask 调度任务
type WorkflowSchedulerTask struct {
	ID             uint64    `gorm:"id"`              // ID
	UserID         uint64    `gorm:"user_id"`         // 用户ID
	Type           int8      `gorm:"task_type"`       // 任务类型
	Mutex          int8      `gorm:"task_mutex"`      // 任务互斥
	Params         string    `gorm:"params"`          // 参数
	RetryTimes     uint      `gorm:"retry_times"`     // 重试次数
	MaxRetryTimes  uint      `gorm:"max_retry_times"` // 最大重试次数
	Timeout        uint      `gorm:"timeout"`         // 超时时间
	Runner         string    `gorm:"runner"`          // 执行器
	RunnerInstance int64     `gorm:"runner_instance"` // 执行器实例
	Result         string    `gorm:"result"`          // 本次结果
	TraceID        string    `gorm:"trace_id"`        // trace_id
	StartTime      time.Time `gorm:"start_time"`      // 任务开始执行时间
	EndTime        time.Time `gorm:"end_time"`        // 任务结束执行时间
	NextStartTime  time.Time `gorm:"next_start_time"` // 任务开始执行时间
	CreateTime     time.Time `gorm:"create_time"`     // 创建时间
	UpdateTime     time.Time `gorm:"update_time"`     // 更新时间
}

// TableName ...
func (s WorkflowSchedulerTask) TableName() string {
	return "t_bot_task"
}

// WorkflowSchedulerTaskColumns 调度任务列
var WorkflowSchedulerTaskColumns = struct {
	ID             string
	UserID         string
	Type           string
	Mutex          string
	Params         string
	RetryTimes     string
	MaxRetryTimes  string
	Timeout        string
	Runner         string
	RunnerInstance string
	Result         string
	TraceID        string
	StartTime      string
	EndTime        string
	NextStartTime  string
	CreateTime     string
	UpdateTime     string
}{
	ID:             "id",
	UserID:         "user_id",
	Type:           "task_type",
	Mutex:          "task_mutex",
	Params:         "params",
	RetryTimes:     "retry_times",
	MaxRetryTimes:  "max_retry_times",
	Timeout:        "timeout",
	Runner:         "runner",
	RunnerInstance: "runner_instance",
	Result:         "result",
	TraceID:        "trace_id",
	StartTime:      "start_time",
	EndTime:        "end_time",
	NextStartTime:  "next_start_time",
	CreateTime:     "create_time",
	UpdateTime:     "update_time",
}

const (
	// ImportWorkflowStatusWait 导入任务状态，待处理
	ImportWorkflowStatusWait = 0
	// ImportWorkflowStatusProcessing 导入任务状态，处理中
	ImportWorkflowStatusProcessing = 1
	// ImportWorkflowStatusProcessed 导入任务状态，已处理
	ImportWorkflowStatusProcessed = 2
	// ImportWorkflowStatusProcessFail 导入任务状态，处理失败
	ImportWorkflowStatusProcessFail = 3

	// ExcelWorkflowTplID 工作流程文件模板-工作流ID
	ExcelWorkflowTplID = 0
	// ExcelWorkflowTpName 工作流程文件模板-工作流名称
	ExcelWorkflowTpName = 1
	// ExcelWorkflowTplDesc 工作流程文件模板-工作流名描述
	ExcelWorkflowTplDesc = 2
	// ExcelWorkflowTplDialogJson 工作流程文件模板-画布结构
	ExcelWorkflowTplDialogJson = 3
	// ExcelWorkflowTplRefWorkflow 工作流程文件模板-引用的工作流 id
	//ExcelWorkflowTplRefWorkflow = 4

	// ExcelWorkflowRefTplWorkflowID 工作流程引用文件模板-工作流ID
	ExcelWorkflowRefTplWorkflowID = 0
	// 	ExcelWorkflowRefTplNodeID = 1 工作流程引用文件模板-节点ID
	ExcelWorkflowRefTplNodeID = 1
	// ExcelWorkflowRefTplWorkflowRefID 工作流程引用文件模板-工作流引用ID
	ExcelWorkflowRefTplWorkflowRefID = 2
	// ExcelWorkflowRefTplWorkflowRefName 工作流程引用文件模板-工作流引用名称
	ExcelWorkflowRefTplWorkflowRefName = 3

	// ExcelWorkflowPDLTplID 工作流程PDL文件模板-工作流ID
	ExcelWorkflowPDLTplID = 0
	// ExcelWorkflowPDLTplContent 工作流程PDL文件模板-工作流PDL约束内容
	ExcelWorkflowPDLTplContent = 1
	// ExcelWorkflowPDLParamTplDialogJson 工作流程PDL文件模板-PDL参数结构
	ExcelWorkflowPDLParamTplDialogJson = 2
	// ExcelWorkflowPDLContentTplDialogJson 工作流程PDL文件模板-PDL内容结构
	ExcelWorkflowPDLContentTplDialogJson = 3
	// ExcelWorkflowPDLToolTplDialogJson 工作流程PDL文件模板-PDL工具结构
	ExcelWorkflowPDLToolTplDialogJson = 4

	// ExcelParamTplFlowID 参数文件模板-工作流ID
	ExcelParamTplFlowID = 0
	// ExcelParamTplNodeID 参数文件模板-工作流的节点ID
	ExcelParamTplNodeID = 1
	// ExcelParamTplNodeName 参数文件模板-工作流的节点名称
	ExcelParamTplNodeName = 2
	// ExcelParamTplID 参数文件模板-参数ID
	ExcelParamTplID = 3
	// ExcelParamTplName 参数文件模板-参数名称
	ExcelParamTplName = 4
	// ExcelParamTplDesc 参数文件模板-参数描述
	ExcelParamTplDesc = 5
	// ExcelParamTplType 参数文件模板-参数类型
	ExcelParamTplType = 6
	// ExcelParamTplExamples 参数文件模版-参数正确示例
	ExcelParamTplExamples = 7
	// ExcelParamTplInvalidExamples 参数文件模版-参数错误示例
	ExcelParamTplInvalidExamples = 8

	// ExcelVarFlowID 自定义变量 - 工作流ID
	ExcelVarFlowID = 0
	// ExcelVarID 自定义变量 - 变量ID
	ExcelVarID = 1
	// ExcelVarName 自定义变量 - 变量名称
	ExcelVarName = 2
	// ExcelVarDesc 自定义变量 - 变量描述
	ExcelVarDesc = 3
	// ExcelVarType 自定义变量 - 变量类型
	ExcelVarType = 4
	// ExcelVarDefaultValue 自定义变量 - 变量默认值
	ExcelVarDefaultValue = 5
	// ExcelVarDefaultFileName 自定义变量 - 变量默认值文件名称
	ExcelVarDefaultFileName = 6

	// ExcelExampleFlowID 示例问法 - 工作流ID
	ExcelExampleFlowID = 0
	//ExcelExampleID 示例问法 - 示例问法ID
	ExcelExampleID = 1
	//ExcelExample 示例问法 - 示例问法
	ExcelExample = 2

	// ExcelImportExample 导入示例问法 - 示例问法
	ExcelImportExample = 0

	// ImportWorkflowKey 导入工作流程锁key
	ImportWorkflowKey = "WORKFLOW_IMPORT:%s"
	// ImportWorkflowNameKey 导入工作流程名称锁key
	ImportWorkflowNameKey = "WORKFLOW_IMPORT_NAME:%s"
	// ImportWorkflowNameKeyExpTime key处理时间
	ImportWorkflowNameKeyExpTime = 15 * time.Second

	// WorkflowJsonFileKey 导入/导出工作流对话树json文件key
	WorkflowJsonFileKey = "%s_workflow.json"
	// ParamExampleFileKey 导入/导出参数的正确示例json文件key
	ParamExampleFileKey = "%s_%s_example.json"
	// ParamInvalidExampleFileKey 导入/导出参数的错误示例json文件key
	ParamInvalidExampleFileKey = "%s_%s_invalid_example.json"
	// WorkflowPDLParamJsonFileKey 导入/导出工作流对话树pdlParam对应json文件key
	WorkflowPDLParamJsonFileKey = "%s_workflow_pdl_param.json"
	// WorkflowPDLContentJsonFileKey 导入/导出工作流对话树pdlContent对应json文件key
	WorkflowPDLContentJsonFileKey = "%s_workflow_pdl_content.json"
	// WorkflowPDLToolJsonFileKey 导入/导出工作流对话树pdlTool对应json文件key
	WorkflowPDLToolJsonFileKey = "%s_workflow_pdl_tool.json"

	ExportWorkflowPlatformOp    = "op" // OP平台
	ExportWorkflowStatusStart   = 0
	ExportWorkflowStatusSuccess = 1
	ExportWorkflowStatusFailed  = 2

	ExportWorkflowTypeFlow = "workflow"
)

// ExportWorkflowFile 导出记录
type ExportWorkflowFile struct {
	ID         uint64    `gorm:"column:f_id"`        // 主键ID
	ExportID   uint64    `gorm:"column:f_export_id"` // 导入ID
	RobotID    string    `gorm:"column:f_robot_id"`  // 机器人ID
	Params     string    `gorm:"column:f_params"`
	Status     uint32    `gorm:"column:f_status"`    // 状态 0 导出中 1 导出成功 2 导出失败
	CosUrl     string    `gorm:"column:f_cos_url"`   // cos文件地址
	FileSize   uint64    `gorm:"column:f_file_size"` // 文件size
	Type       string    `gorm:"column:f_type"`      // 下载类型：flow：工作流程
	Platform   string    `gorm:"column:f_platform"`  // 导出平台：op：op平台导出
	Uin        string    `gorm:"column:f_uin"`       // 主用户ID
	SubUin     string    `gorm:"column:f_sub_uin"`   // 子用户ID
	IsDeleted  uint      `gorm:"column:f_is_deleted"`
	CreateTime time.Time `gorm:"column:f_create_time;default:null"` // 创建时间
	UpdateTime time.Time `gorm:"column:f_update_time;default:null"` // 更新时间
}

// TableName ...
func (t ExportWorkflowFile) TableName() string {
	return "t_export_file"
}

// GetWorkflowJsonFileKey 获取导入/导出工作流对话树json文件key
func GetWorkflowJsonFileKey(workflowID string) string {
	return fmt.Sprintf(WorkflowJsonFileKey, workflowID)
}

// GetWorkflowPDLParamJsonFileKey 获取导入/导出工作流对话树PDLParamJson文件key
func GetWorkflowPDLParamJsonFileKey(workflowID string) string {
	return fmt.Sprintf(WorkflowPDLParamJsonFileKey, workflowID)
}

// GetWorkflowPDLContentJsonFileKey 获取导入/导出工作流对话树PDLContentJson文件key
func GetWorkflowPDLContentJsonFileKey(workflowID string) string {
	return fmt.Sprintf(WorkflowPDLContentJsonFileKey, workflowID)
}

// GetWorkflowPDLToolJsonFileKey 获取导入/导出工作流对话树PDLToolJson文件key
func GetWorkflowPDLToolJsonFileKey(workflowID string) string {
	return fmt.Sprintf(WorkflowPDLToolJsonFileKey, workflowID)
}

// WorkflowImport 任务流导入任务
type WorkflowImport struct {
	ID             uint64    `gorm:"column:f_id"`                       // 主键ID
	ImportID       string    `gorm:"column:f_import_id"`                // 导入ID
	RobotID        string    `gorm:"column:f_robot_id"`                 // 机器人ID
	ParentImportID string    `gorm:"column:f_parent_import_id"`         // 父导入ID
	Params         string    `gorm:"column:f_params"`                   // 任务参数
	FinalParams    string    `gorm:"column:f_final_params"`             // 任务最终的参数
	Status         uint32    `gorm:"column:f_status"`                   // 状态 0:待处理，1：处理中，2：已处理，3：处理失败
	Message        string    `gorm:"column:f_message"`                  // 错误消息
	Uin            string    `gorm:"column:f_uin"`                      // 主用户ID
	SubUin         string    `gorm:"column:f_sub_uin"`                  // 子用户ID
	CreateTime     time.Time `gorm:"column:f_create_time;default:null"` // 创建时间
	UpdateTime     time.Time `gorm:"column:f_update_time;default:null"` // 更新时间
}

// TableName ...
func (t WorkflowImport) TableName() string {
	return "t_workflow_import"
}

// TWorkflowImportColumns 任务流导入任务列
var TWorkflowImportColumns = struct {
	ID             string
	ImportID       string
	RobotID        string
	ParentImportID string
	Params         string
	FinalParams    string
	Status         string
	Message        string
	Uin            string
	SubUin         string
	CreateTime     string
	UpdateTime     string
}{
	ID:             "f_id",
	ImportID:       "f_import_id",
	RobotID:        "f_robot_id",
	ParentImportID: "f_parent_import_id",
	Params:         "f_params",
	FinalParams:    "f_final_params",
	Status:         "f_status",
	Message:        "f_message",
	Uin:            "f_uin",
	SubUin:         "f_sub_uin",
	CreateTime:     "f_create_time",
	UpdateTime:     "f_update_time",
}

// WorkflowImportData 工作流导入数据
type WorkflowImportData struct {
	WorkflowData *ExportWorkflow          `json:"workflow"`      // 工作流数据
	Examples     []*ExportWorkflowExample `json:"examples"`      // 示例数据
	Params       []*ParamMigrationInfo    `json:"params"`        // 参数数据
	Vars         []*ExportWorkflowVar     `json:"vars"`          // 自定义变量
	WorkflowRefs []*ExportWorkflowRef     `json:"workflow_refs"` // 工作流引用数据
	WorkflowPDLs []*ExportWorkflowPDL     `json:"workflow_PDLs"` // 工作流pdl数据
	Old2New      map[string]string        `json:"old_2_new"`     // 新旧工作流id映射
	Uin          string                   `json:"uin"`
	SubUin       string                   `json:"sub_uin"`
	WorkflowName string                   `json:"workflow_name"` // 自定义工作流名称
	WorkflowDesc string                   `json:"workflow_desc"` // 自定义工作流描述
}

// WfExampleImportData 工作流示例问法导入数据
type WfExampleImportData struct {
	Examples   []*ExportWorkflowExample `json:"examples"`    // 示例问法数据
	WorkflowId string                   `json:"workflow_id"` // 工作流Id
	AppId      string                   `json:"app_id"`      //应用Id
	Uin        string                   `json:"uin"`
	SubUin     string                   `json:"sub_uin"`
	StaffId    uint64                   `json:"staff_id"`
}

// GetImportWorkflowNameLockKey 获取导入任务流程名称锁key
func GetImportWorkflowNameLockKey(rootID, intentName string) string {
	return fmt.Sprintf(ImportWorkflowNameKey, encode.MD5(fmt.Sprintf("%s_%s", rootID, intentName)))
}
