// Package entity 导入/导出相关实体
// @Author: halelv
// @Date: 2024/3/1 15:34
package entity

import (
	"fmt"
	"time"
	"unicode/utf8"

	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/go-comm/encode"
	jsoniter "github.com/json-iterator/go"
)

const (
	// FlowImportStatusWait 导入任务状态，待处理
	FlowImportStatusWait = 0
	// FlowImportStatusProcessing 导入任务状态，处理中
	FlowImportStatusProcessing = 1
	// FlowImportStatusProcessed 导入任务状态，已处理
	FlowImportStatusProcessed = 2
	// FlowImportStatusProcessFail 导入任务状态，处理失败
	FlowImportStatusProcessFail = 3

	// ExcelCategoryTplID 分类文件模板-分类ID
	ExcelCategoryTplID = 0
	// ExcelCategoryTplMaxLevel 分类文件模板,最大分类层级
	ExcelCategoryTplMaxLevel = 10

	// ExcelTaskFlowTplID 任务流程文件模板-任务流ID
	ExcelTaskFlowTplID = 0
	// ExcelTaskFlowTplIntentID 任务流程文件模板-意图ID
	ExcelTaskFlowTplIntentID = 1
	// ExcelTaskFlowTplIntentName 任务流程文件模板-意图名称
	ExcelTaskFlowTplIntentName = 2
	// ExcelTaskFlowTplIntentDesc 任务流程文件模板-意图描述
	ExcelTaskFlowTplIntentDesc = 3
	// ExcelTaskFlowTplCategoryID 任务流程文件模板-分类ID
	ExcelTaskFlowTplCategoryID = 4
	// ExcelTaskFlowTplDialogJson 任务流程文件模板-画布结构
	ExcelTaskFlowTplDialogJson = 5

	// ExcelIntentTplID 意图文件模板-意图ID
	ExcelIntentTplID = 0
	// ExcelIntentTplName 意图文件模板-意图名称
	ExcelIntentTplName = 1
	// ExcelIntentTplDesc 意图文件模板-意图描述
	ExcelIntentTplDesc = 2
	// ExcelIntentTplType 意图文件模板-意图类型
	ExcelIntentTplType = 3
	// ExcelIntentTplSource 意图文件模板-业务来源
	ExcelIntentTplSource = 4

	// ExcelIntentVarIntentID 自定义变量 - 意图ID
	ExcelIntentVarIntentID = 0
	// ExcelIntentVarID 自定义变量 - 变量ID
	ExcelIntentVarID = 1
	// ExcelIntentVarName 自定义变量 - 变量名称
	ExcelIntentVarName = 2
	// ExcelIntentVarDesc 自定义变描述
	ExcelIntentVarDesc = 3
	// ExcelIntentVarType 自定义变量类型 - 变量名称
	ExcelIntentVarType = 4
	// ExcelIntentVarDefaultValue 自定义变量默认值
	ExcelIntentVarDefaultValue = 5
	// ExcelIntentVarDefaultFileName 自定义变量默认值文件名称
	ExcelIntentVarDefaultFileName = 6

	// ExcelIntentExampleIntentID 示例问法 - 意图ID
	ExcelIntentExampleIntentID = 0
	//ExcelIntentExampleID 示例问法 - 示例问法ID
	ExcelIntentExampleID = 1
	//ExcelIntentExampleCorpus 示例问法 - 示例问法
	ExcelIntentExampleCorpus = 2

	// ExcelCorpusTplIntentID 语料文件模板-意图ID
	ExcelCorpusTplIntentID = 0
	// ExcelCorpusTplContent 语料文件模板-语料内容
	ExcelCorpusTplContent = 1

	// ExcelSlotTplIntentID 槽位文件模板-意图ID
	ExcelSlotTplIntentID = 0
	// ExcelSlotTplID 槽位文件模板-槽位ID
	ExcelSlotTplID = 1
	// ExcelSlotTplName 槽位文件模板-槽位名称
	ExcelSlotTplName = 2
	// ExcelSlotTplDesc 槽位文件模板-槽位描述
	ExcelSlotTplDesc = 3
	// ExcelSlotTplExamples 槽位文件模版-槽位示例
	ExcelSlotTplExamples = 4
	// ExcelSlotTplEntityInfo 槽位文件模板-实体信息
	ExcelSlotTplEntityInfo = 5

	// ExcelEntryValue 导入词条模版-词条名
	ExcelEntryValue = 0
	// ExcelEntryAlias 导入词条模版-同义词
	ExcelEntryAlias = 1

	// ImportTaskFlowCategoryKey 导入任务流程分类缓存key
	ImportTaskFlowCategoryKey = "TASK_FLOW_IMPORT_CATEGORY:%s"
	// ImportTaskFlowKey 导入任务流程锁key
	ImportTaskFlowKey = "TASK_FLOW_IMPORT:%s"
	// ImportTaskFlowNameKey 导入任务流程名称锁key
	ImportTaskFlowNameKey = "TASK_FLOW_IMPORT_NAME:%s"
	// ImportTaskFlowNameKeyExpTime key处理时间
	ImportTaskFlowNameKeyExpTime = 15 * time.Second

	// TaskFlowJsonFileKey 导入/导出任务流对话树json文件key
	TaskFlowJsonFileKey = "%s_task_flow.json"
	// SlotEntitiesFileKey 导入/导出槽位实体json文件key
	SlotEntitiesFileKey = "%s_slot_entities.json"

	ExportTaskPlatformOp    = "op" // OP平台
	ExportTaskStatusStart   = 0
	ExportTaskStatusSuccess = 1
	ExportTaskStatusFailed  = 2

	ExportTaskTypeFlow = "flow"

	CopyExperienceWorkflowType = 0 // 复制体验中心
	CopyAppWorkflowType        = 1 // 同UIN下跨应用复制
)

// ExportFile 导出记录
type ExportFile struct {
	ID         uint64    `gorm:"column:f_id"`        // 主键ID
	ExportID   uint64    `gorm:"column:f_export_id"` // 导入ID
	AppID      string    `gorm:"column:f_app_id"`    // 机器人ID
	Params     string    `gorm:"column:f_params"`
	Status     uint32    `gorm:"column:f_status"`    // 状态 0 导出中 1 导出成功 2 导出失败
	CosUrl     string    `gorm:"column:f_cos_url"`   // cos文件地址
	FileSize   uint64    `gorm:"column:f_file_size"` // 文件size
	Type       string    `gorm:"column:f_type"`      // 下载类型：flow：任务流程
	Platform   string    `gorm:"column:f_platform"`  // 导出平台：op：op平台导出
	Uin        string    `gorm:"column:f_uin"`       // 主用户ID
	SubUin     string    `gorm:"column:f_sub_uin"`   // 子用户ID
	IsDeleted  uint      `gorm:"column:f_is_deleted"`
	CreateTime time.Time `gorm:"column:f_create_time;default:null"` // 创建时间
	UpdateTime time.Time `gorm:"column:f_update_time;default:null"` // 更新时间
}

// TableName ...
func (t ExportFile) TableName() string {
	return "t_export_file"
}

// TaskFlowImport 任务流导入任务
type TaskFlowImport struct {
	ID             uint64    `gorm:"column:f_id"`                       // 主键ID
	ImportID       string    `gorm:"column:f_import_id"`                // 导入ID
	RobotID        string    `gorm:"column:f_robot_id"`                 // 机器人ID
	ParentImportID string    `gorm:"column:f_parent_import_id"`         // 父导入ID
	Params         string    `gorm:"column:f_params"`                   // 任务参数
	FinalParams    string    `gorm:"column:f_final_params"`             // 任务最终的参数
	Status         uint32    `gorm:"column:f_status"`                   // 状态 0:待处理，1：处理中，2：已处理，3：处理失败
	Message        string    `gorm:"column:f_message"`                  // 错误消息
	Uin            string    `gorm:"column:f_uin"`                      // 主用户ID
	SubUin         string    `gorm:"column:f_sub_uin"`                  // 子用户ID
	CreateTime     time.Time `gorm:"column:f_create_time;default:null"` // 创建时间
	UpdateTime     time.Time `gorm:"column:f_update_time;default:null"` // 更新时间
}

// TableName ...
func (t TaskFlowImport) TableName() string {
	return "t_task_flow_import"
}

// TTaskFlowImportColumns 任务流导入任务列
var TTaskFlowImportColumns = struct {
	ID             string
	ImportID       string
	RobotID        string
	ParentImportID string
	Params         string
	FinalParams    string
	Status         string
	Message        string
	Uin            string
	SubUin         string
	CreateTime     string
	UpdateTime     string
}{
	ID:             "f_id",
	ImportID:       "f_import_id",
	RobotID:        "f_robot_id",
	ParentImportID: "f_parent_import_id",
	Params:         "f_params",
	FinalParams:    "f_final_params",
	Status:         "f_status",
	Message:        "f_message",
	Uin:            "f_uin",
	SubUin:         "f_sub_uin",
	CreateTime:     "f_create_time",
	UpdateTime:     "f_update_time",
}

// GetImportTaskFlowCategoryKey 获取导入任务流程分类缓存key
func GetImportTaskFlowCategoryKey(importID string) string {
	return fmt.Sprintf(ImportTaskFlowCategoryKey, importID)
}

// GetImportTaskFlowLockKey 获取导入任务流程key
func GetImportTaskFlowLockKey(importID string) string {
	return fmt.Sprintf(ImportTaskFlowKey, importID)
}

// GetImportTaskFlowNameLockKey 获取导入任务流程名称锁key
func GetImportTaskFlowNameLockKey(rootID, intentName string) string {
	return fmt.Sprintf(ImportTaskFlowNameKey, encode.MD5(fmt.Sprintf("%s_%s", rootID, intentName)))
}

// GetTaskFlowJsonFileKey 获取导入/导出任务流对话树json文件key
func GetTaskFlowJsonFileKey(taskFlowID string) string {
	return fmt.Sprintf(TaskFlowJsonFileKey, taskFlowID)
}

// GetSlotEntitiesFileKey 获取导入/导出槽位实体json文件key
func GetSlotEntitiesFileKey(slotID string) string {
	return fmt.Sprintf(SlotEntitiesFileKey, slotID)
}

// ImportEntryParamsData 词条导入任务参数
type ImportEntryParamsData struct {
	SlotID   string                `json:"slot_id"`   // 槽位ID
	EntityID string                `json:"entity_id"` // 实体ID
	Entries  []*EntryMigrationInfo `json:"entries"`   // 词条信息
}

// TaskFlowImportParamsData 任务流导入子任务参数数据
type TaskFlowImportParamsData struct {
	TaskFlow     *TaskFlowRow        `json:"task_flow"`     // 任务流程
	Intent       *IntentRow          `json:"intent"`        // 意图
	Corpora      []*CorpusRow        `json:"corpora"`       // 语料信息
	Slots        []*SlotRow          `json:"slots"`         // 槽位信息
	IntentCorpus []*IntentExampleRow `json:"intent_corpus"` // 意图(语料)示例问法
	Vars         []*IntentVarRow     `json:"vars"`          // 自定义变量
	Uin          string              `json:"uin"`           // 主用户ID
	SubUin       string              `json:"sub_uin"`       // 子用户ID
}

// TaskFlowImportParentParamsData 任务流导入父任务参数数据
type TaskFlowImportParentParamsData struct {
	Categories []*CategoryRow `json:"categories"` // 分类信息
	Uin        string         `json:"uin"`        // 主用户ID
	SubUin     string         `json:"sub_uin"`    // 子用户ID
}

// TaskFlowImportData 任务流导入数据
type TaskFlowImportData struct {
	CategoryData     []*CategoryRow      `json:"category_data"`   // 分类数据
	TaskFlowData     []*TaskFlowRow      `json:"task_flow_data"`  // 任务流数据
	IntentData       []*IntentRow        `json:"intent_data"`     // 意图数据
	CorpusData       []*CorpusRow        `json:"corpus_data"`     // 语料数据
	SlotData         []*SlotRow          `json:"slot_data"`       // 槽位数据
	IntentCorpusData []*IntentExampleRow `json:"intent_corpus"`   // 示例问法语料数据
	IntentVarData    []*IntentVarRow     `json:"intent_var_data"` // 自定义变量数据
}

// CategoryRow 导入任务流分类数据
type CategoryRow struct {
	CategoryID    string   `json:"category_id"`    // 分类ID
	CategoryNames []string `json:"category_names"` // 分类名称
}

// TaskFlowRow 导入任务流对话树数据
type TaskFlowRow struct {
	FlowID     string `json:"flow_id"`     // 任务流ID
	IntentID   string `json:"intent_id"`   // 意图ID
	IntentName string `json:"intent_name"` // 意图名称
	IntentDesc string `json:"intent_desc"` // 意图描述
	CategoryID string `json:"category_id"` // 类别ID
	DialogJson string `json:"dialog_json"` // 对话树json字段
}

// IntentRow 导入任务流意图数据
type IntentRow struct {
	IntentID   string `json:"intent_id"`   // 意图ID
	IntentName string `json:"intent_name"` // 意图名称
	IntentDesc string `json:"intent_desc"` // 意图描述
	IntentType string `json:"intent_type"` // 任务类型：FLOW对话树意图 or 非对话树意图
	Source     string `json:"source"`      // 业务来源
}

// CorpusRow 导入任务流语料数据
type CorpusRow struct {
	IntentID   string `json:"intent_id"`   // 意图ID
	Content    string `json:"content"`     // 语料内容
	IntentDesc string `json:"intent_desc"` // 意图描述
}

// SlotRow 导入任务流槽位数据
type SlotRow struct {
	IntentID     string `json:"intent_id"`     // 意图ID
	SlotID       string `json:"slot_id"`       // 槽位ID
	SlotName     string `json:"slot_name"`     // 槽位名称
	SlotDesc     string `json:"slot_desc"`     // 槽位描述
	SlotExamples string `json:"slot_examples"` // 槽位示例
	EntityInfo   string `json:"entity_info"`   // 实体信息
}

// IntentExampleRow 导入示例问法数据
type IntentExampleRow struct {
	IntentID string `json:"intent_id"` // 意图ID
	CorpusID string `json:"corpus_id"` // 示例问法ID
	Corpus   string `json:"corpus"`    // 示例问法
}

// IntentVarRow 导入变量数据
type IntentVarRow struct {
	IntentID           string `json:"intent_id"`             // 意图ID
	VarID              string `json:"var_id"`                // 变量ID
	VarName            string `json:"var_name"`              // 变量名称
	VarDesc            string `json:"var_desc"`              // 变量描述
	VarType            string `json:"var_type"`              // 变量类型
	VarDefaultValue    string `json:"var_default_value"`     // 变量默认值
	VarDefaultFileName string `json:"var_default_file_name"` // 变量默认值文件名称
}

// ConvertToSlotMigrationInfo 转换为SlotMigrationInfo
func (row SlotRow) ConvertToSlotMigrationInfo() (*SlotMigrationInfo, error) {
	var slotExamples []string
	err := jsoniter.Unmarshal([]byte(row.SlotExamples), &slotExamples)
	if err != nil {
		return nil, err
	}
	var entityInfos []EntityMigrationInfo
	err = jsoniter.Unmarshal([]byte(row.EntityInfo), &entityInfos)
	if err != nil {
		return nil, err
	}
	return &SlotMigrationInfo{
		SlotID:       row.SlotID,
		SlotName:     row.SlotName,
		SlotDesc:     row.SlotDesc,
		SlotExamples: slotExamples,
		EntityInfo:   entityInfos,
	}, nil
}

// SlotMigrationInfo 槽位迁移信息 -- 导入/导出使用
type SlotMigrationInfo struct {
	SlotID       string                `json:"slot_id"`       // 槽位ID
	SlotName     string                `json:"slot_name"`     // 槽位名称
	SlotDesc     string                `json:"slot_desc"`     // 槽位描述
	SlotExamples []string              `json:"slot_examples"` // 槽位示例
	EntityInfo   []EntityMigrationInfo `json:"entity_info"`   // 槽位关联的实体信息
}

// ConvertToDBCreateInfo 转换为DB创建所需信息
func (slot *SlotMigrationInfo) ConvertToDBCreateInfo(robotID, uin, subUin string) (
	Slot, []Entity, []Entry, []SlotEntity) {
	// 槽位
	examples, _ := jsoniter.MarshalToString(slot.SlotExamples)
	slotDB := Slot{
		SlotID:        slot.SlotID,
		SlotName:      slot.SlotName,
		SlotDesc:      slot.SlotDesc,
		Examples:      examples,
		RobotID:       robotID,
		UIN:           uin,
		SubUIN:        subUin,
		ReleaseStatus: ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        ActionInsert,
	}

	// 实体｜词条｜槽位实体关联
	entityDBs := make([]Entity, 0)
	entriesDBs := make([]Entry, 0)
	slotEntityDBs := make([]SlotEntity, 0)

	for _, entity := range slot.EntityInfo {
		entityDB, entryDBs := entity.convertToDBCreateInfo(robotID, uin, subUin)

		// 非系统实体才需要新建实体词条
		if entityDB.LevelType != SlotLevelSYS {
			entityDBs = append(entityDBs, entityDB)
			entriesDBs = append(entriesDBs, entryDBs...)
		}

		// 实体都需要新建槽位和实体关联
		slotEntityDB := SlotEntity{
			SlotID:        slotDB.SlotID,
			EntityID:      entityDB.EntityID,
			ReleaseStatus: ReleaseStatusUnPublished,
			IsDeleted:     0,
			Action:        ActionInsert,
		}

		slotEntityDBs = append(slotEntityDBs, slotEntityDB)
	}

	return slotDB, entityDBs, entriesDBs, slotEntityDBs
}

// IsEqual 判断槽位信息是否相等
func (slot *SlotMigrationInfo) IsEqual(compareSlot *SlotMigrationInfo) bool {
	if slot.SlotName != compareSlot.SlotName || slot.SlotDesc != compareSlot.SlotDesc ||
		len(slot.SlotExamples) != len(compareSlot.SlotExamples) ||
		len(slot.EntityInfo) != len(compareSlot.EntityInfo) {
		return false
	}

	if !types.EqualStringSlice(slot.SlotExamples, compareSlot.SlotExamples) {
		return false
	}

	entityMap := make(map[string]EntityMigrationInfo)
	for _, entity := range slot.EntityInfo {
		entityMap[entity.EntityName+"_"+entity.EntityType] = entity
	}

	compareEntityMap := make(map[string]EntityMigrationInfo)
	for _, entity := range compareSlot.EntityInfo {
		compareEntityMap[entity.EntityName+"_"+entity.EntityType] = entity
	}

	if len(entityMap) != len(compareEntityMap) {
		return false
	}

	for k, v := range entityMap {
		if compareV, ok := compareEntityMap[k]; !ok || !compareV.isEqual(v) {
			return false
		}
	}

	return true
}

// IsLegal 判断槽位信息是否合法
func (slot *SlotMigrationInfo) IsLegal() bool {
	// 槽位名称
	if utf8.RuneCountInString(slot.SlotName) == 0 ||
		utf8.RuneCountInString(slot.SlotName) > config.GetMainConfig().Entry.MaxEntityNameLen {
		return false
	}

	// 槽位描述
	if utf8.RuneCountInString(slot.SlotDesc) > config.GetMainConfig().Entry.MaxEntityDescLen {
		return false
	}

	// 槽位示例
	if len(slot.SlotExamples) > config.GetMainConfig().Entry.MaxEntityExampleNum ||
		types.HasDuplicateStringSlice(slot.SlotExamples) {
		return false
	}
	// 槽位示例中单个示例的长度限制
	for _, slotExample := range slot.SlotExamples {
		if utf8.RuneCountInString(slotExample) == 0 ||
			utf8.RuneCountInString(slotExample) > config.GetMainConfig().Entry.MaxEntityExampleLen {
			return false
		}
	}

	for _, entity := range slot.EntityInfo {
		if !entity.isLegal(slot.SlotName, slot.SlotDesc, slot.SlotExamples) {
			return false
		}
	}
	return true
}

// EntityMigrationInfo 实体迁移信息 -- 导入/导出使用
type EntityMigrationInfo struct {
	EntityID       string               `json:"entity_id"`       // 实体ID
	EntityName     string               `json:"entity_name"`     // 实体名称
	EntityDesc     string               `json:"entity_desc"`     // 实体描述
	EntityExamples []string             `json:"entity_examples"` // 实体示例
	EntityType     string               `json:"entity_type"`     // 实体类型(SYS｜BOT)，SYS类型导入时不需要处理
	Entries        []EntryMigrationInfo `json:"entries"`         // 实体词条，SYS类型词条为空
}

// convertToDBCreateInfo 转换为DB创建所需信息
func (entity EntityMigrationInfo) convertToDBCreateInfo(robotID, uin, subUin string) (Entity, []Entry) {
	// 实体
	examples, _ := jsoniter.MarshalToString(entity.EntityExamples)
	entityDB := Entity{
		EntityID:      entity.EntityID,
		EntityName:    entity.EntityName,
		EntityDesc:    entity.EntityDesc,
		Examples:      examples,
		LevelType:     entity.EntityType,
		RobotID:       robotID,
		UIN:           uin,
		SubUIN:        subUin,
		ReleaseStatus: ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        ActionInsert,
	}

	// 词条
	entryDBs := make([]Entry, 0)

	// 非系统实体才需要新建实体词条
	if entity.EntityType != SlotLevelSYS {
		for _, entry := range entity.Entries {
			entryDBs = append(entryDBs, entry.convertToDBCreateInfo(entityDB.EntityID, uin, subUin))
		}
	}

	return entityDB, entryDBs
}

// isEqual 判断实体信息是否相等
func (entity EntityMigrationInfo) isEqual(compareEntity EntityMigrationInfo) bool {
	if entity.EntityName != compareEntity.EntityName || entity.EntityDesc != compareEntity.EntityDesc ||
		entity.EntityType != compareEntity.EntityType ||
		len(entity.EntityExamples) != len(compareEntity.EntityExamples) ||
		len(entity.Entries) != len(compareEntity.Entries) {
		return false
	}

	if !types.EqualStringSlice(entity.EntityExamples, compareEntity.EntityExamples) {
		return false
	}

	// 系统实体不需要判断后续词条
	if entity.EntityType == SlotLevelSYS && len(compareEntity.Entries) == 0 {
		return true
	}

	entityEntryMap := make(map[string]EntryMigrationInfo)
	for _, entry := range entity.Entries {
		entityEntryMap[entry.EntryValue] = entry
	}

	compareEntityEntryMap := make(map[string]EntryMigrationInfo)
	for _, entry := range compareEntity.Entries {
		compareEntityEntryMap[entry.EntryValue] = entry
	}

	if len(entityEntryMap) != len(compareEntityEntryMap) {
		return false
	}

	for k, v := range entityEntryMap {
		if compareV, ok := compareEntityEntryMap[k]; !ok || !compareV.isEqual(v) {
			return false
		}
	}

	return true
}

// isLegal 判断实体信息是否合法
func (entity EntityMigrationInfo) isLegal(slotName, slotDesc string, slotExamples []string) bool {
	// 实体名称
	if utf8.RuneCountInString(entity.EntityName) == 0 ||
		utf8.RuneCountInString(entity.EntityName) > config.GetMainConfig().Entry.MaxEntityNameLen ||
		entity.EntityName != slotName {
		return false
	}

	// 实体描述
	if utf8.RuneCountInString(entity.EntityDesc) > config.GetMainConfig().Entry.MaxEntityDescLen ||
		entity.EntityDesc != slotDesc {
		return false
	}

	// 实体示例
	if len(entity.EntityExamples) > config.GetMainConfig().Entry.MaxEntityExampleNum ||
		types.HasDuplicateStringSlice(entity.EntityExamples) ||
		!types.EqualStringSlice(entity.EntityExamples, slotExamples) {
		return false
	}
	// 实体示例中单个示例的长度限制
	for _, entityExample := range entity.EntityExamples {
		if utf8.RuneCountInString(entityExample) == 0 ||
			utf8.RuneCountInString(entityExample) > config.GetMainConfig().Entry.MaxEntityExampleLen {
			return false
		}
	}

	// 实体类型
	if _, ok := SlotLevelMap[entity.EntityType]; !ok {
		return false
	}

	// 实体词条
	if len(entity.Entries) > config.GetMainConfig().Entry.MaxEntryNum {
		return false
	}
	for _, entry := range entity.Entries {
		if !entry.isLegal() {
			return false
		}
	}
	return true
}

// EntryMigrationInfo 词条迁移信息 -- 导入/导出使用
type EntryMigrationInfo struct {
	EntryID    string   `json:"entry_id"`    // 词条ID
	EntryValue string   `json:"entry_value"` // 词条的值
	EntryAlias []string `json:"entry_alias"` // 词条别名
}

// convertToDBCreateInfo 转换为DB创建所需信息
func (entry EntryMigrationInfo) convertToDBCreateInfo(entityID, uin, subUin string) Entry {
	// 词条
	alias, _ := jsoniter.MarshalToString(entry.EntryAlias)
	return Entry{
		EntryID:       entry.EntryID,
		EntityID:      entityID,
		EntryValue:    entry.EntryValue,
		EntryAlias:    alias,
		UIN:           uin,
		SubUIN:        subUin,
		ReleaseStatus: ReleaseStatusUnPublished,
		IsDeleted:     0,
		Action:        ActionInsert,
	}
}

// isEqual 判断词条信息是否相等
func (entry EntryMigrationInfo) isEqual(compareEntry EntryMigrationInfo) bool {
	if entry.EntryValue != compareEntry.EntryValue || len(entry.EntryAlias) != len(compareEntry.EntryAlias) {
		return false
	}

	if !types.EqualStringSlice(entry.EntryAlias, compareEntry.EntryAlias) {
		return false
	}

	return true
}

// isLegal 判断词条信息是否合法
func (entry EntryMigrationInfo) isLegal() bool {
	// 词条的值
	if utf8.RuneCountInString(entry.EntryValue) == 0 ||
		utf8.RuneCountInString(entry.EntryValue) > config.GetMainConfig().Entry.MaxEntryNameLen {
		return false
	}

	// 词条别名
	if len(entry.EntryAlias) > config.GetMainConfig().Entry.MaxEntryAliasNum ||
		types.HasDuplicateStringSlice(entry.EntryAlias) {
		return false
	}
	// 词条别名中单个别名的长度限制
	for _, alias := range entry.EntryAlias {
		if utf8.RuneCountInString(alias) == 0 ||
			utf8.RuneCountInString(alias) > config.GetMainConfig().Entry.MaxEntryAliasLen {
			return false
		}
	}
	return true
}
