package entity

import (
	"reflect"
	"time"

	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
)

const (
	// TaskMutexNone 无互斥
	TaskMutexNone task_scheduler.TaskMutex = 0

	// TaskExportTaskFlow 导出任务流任务
	TaskExportTaskFlow task_scheduler.TaskType = 1
	// TaskImportTaskFlowParent 导入任务流父任务
	TaskImportTaskFlowParent task_scheduler.TaskType = 2
	// TaskImportTaskFlow 导入任务流子任务
	TaskImportTaskFlow task_scheduler.TaskType = 3
	// TaskImportEntryFlow 导入词条任务
	TaskImportEntryFlow task_scheduler.TaskType = 4
	// WFTaskImportEntryFlow 工作流导入词条【正确示例】任务
	WFTaskImportEntryFlow task_scheduler.TaskType = 5
	// FlowDeleteTask 工作流（任务流）删除任务
	FlowDeleteTask task_scheduler.TaskType = 14
	// TaskConvertWorkflowToPDL 工作流转PDL任务
	TaskConvertWorkflowToPDL task_scheduler.TaskType = 20
	// TaskUpgradeWorkflowVector 工作流向量库升级任务
	TaskUpgradeWorkflowVector task_scheduler.TaskType = 22
)

var (
	// TaskTypeNameMap 任务类型名称映射, 用于在配置文件中配置 任务名 时寻找对应的 任务类型
	TaskTypeNameMap = map[task_scheduler.TaskType]string{
		TaskExportTaskFlow:        "export_task_flow",
		TaskImportTaskFlowParent:  "import_task_flow_parent",
		TaskImportTaskFlow:        "import_task_flow",
		TaskImportEntryFlow:       "import_entry_flow",
		WFTaskImportEntryFlow:     "wf_import_entry",
		FlowDeleteTask:            "flow_delete",
		TaskConvertWorkflowToPDL:  "convert_workflow_to_pdl",
		TaskUpgradeWorkflowVector: "upgrade_workflow_vector",
	}
)

// TaskUpgradeWorkflowVectorParams 工作流向量库升级任务参数
type TaskUpgradeWorkflowVectorParams struct {
	RequestID          string `json:"request_id"`           // 请求ID
	Name               string `json:"name"`                 // 任务名称
	CorpID             uint64 `json:"corp_id"`              // 企业ID
	RobotID            string `json:"robot_id"`             // 机器人ID
	EmbeddingModelName string `json:"embedding_model_name"` // embedding模型名称
	TestOnly           bool   `json:"test_only"`            // 是否仅测试，不写库
}

// TaskConvertWorkflowToPDLParams 工作流转PDL任务参数
type TaskConvertWorkflowToPDLParams struct {
	RequestID  string `json:"request_id"`  // 请求ID
	Name       string `json:"name"`        // 任务名称
	CorpID     uint64 `json:"corp_id"`     // 企业ID
	StaffID    uint64 `json:"staff_id"`    // 员工ID
	RobotID    string `json:"robot_id"`    // 机器人ID
	TaskID     uint64 `json:"task_id"`     // 本次操作的任务ID
	WorkflowID string `json:"workflow_id"` // 工作流ID
}

// FlowDeleteParams 任务删除任务参数
type FlowDeleteParams struct {
	Name    string `json:"name"`     // 任务名称
	RobotID uint64 `json:"robot_id"` // 机器人ID
	CorpID  uint64 `json:"corp_id"`  // 企业ID
	TaskID  uint64 `json:"task_id"`  // 本次删除操作的任务ID
}

// TaskFlowExportParams 任务流导出参数
type TaskFlowExportParams struct {
	RequestID string   `json:"request_id"` // 请求ID
	Name      string   `json:"name"`       // 任务名称
	CorpID    uint64   `json:"corp_id"`    // 企业ID
	StaffID   uint64   `json:"staff_id"`   // 员工ID
	RobotID   string   `json:"robot_id"`   // 机器人ID
	ExportID  uint64   `json:"export_id"`  // 导出ID
	FlowIDs   []string `json:"flow_ids"`   // 任务流ID
	Platform  string   `json:"platform"`   // 导出平台
}

// TaskFlowImportParentParams 任务流程导入父任务
type TaskFlowImportParentParams struct {
	RequestID string `json:"request_id"` // 请求ID
	Name      string `json:"name"`       // 任务名称
	CorpID    uint64 `json:"corp_id"`    // 企业ID
	StaffID   uint64 `json:"staff_id"`   // 员工ID
	RobotID   string `json:"robot_id"`   // 机器人ID
	ImportID  string `json:"import_id"`  // 导入ID
	FileName  string `json:"file_name"`  // 导入文件名
}

// TaskFlowImportParams 任务流程导入子任务
type TaskFlowImportParams struct {
	RequestID      string `json:"request_id"`       // 请求ID
	Name           string `json:"name"`             // 任务名称
	CorpID         uint64 `json:"corp_id"`          // 企业ID
	StaffID        uint64 `json:"staff_id"`         // 员工ID
	RobotID        string `json:"robot_id"`         // 机器人ID
	ParentImportID string `json:"parent_import_id"` // 导入父ID
	ImportID       string `json:"import_id"`        // 导入ID
	FileName       string `json:"file_name"`        // 导入文件名
}

// SchedulerTask 调度任务
type SchedulerTask struct {
	ID             uint64    `gorm:"id"`              // ID
	UserID         uint64    `gorm:"user_id"`         // 用户ID
	Type           int8      `gorm:"task_type"`       // 任务类型
	Mutex          int8      `gorm:"task_mutex"`      // 任务互斥
	Params         string    `gorm:"params"`          // 参数
	RetryTimes     uint      `gorm:"retry_times"`     // 重试次数
	MaxRetryTimes  uint      `gorm:"max_retry_times"` // 最大重试次数
	Timeout        uint      `gorm:"timeout"`         // 超时时间
	Runner         string    `gorm:"runner"`          // 执行器
	RunnerInstance int64     `gorm:"runner_instance"` // 执行器实例
	Result         string    `gorm:"result"`          // 本次结果
	TraceID        string    `gorm:"trace_id"`        // trace_id
	StartTime      time.Time `gorm:"start_time"`      // 任务开始执行时间
	EndTime        time.Time `gorm:"end_time"`        // 任务结束执行时间
	NextStartTime  time.Time `gorm:"next_start_time"` // 任务开始执行时间
	CreateTime     time.Time `gorm:"create_time"`     // 创建时间
	UpdateTime     time.Time `gorm:"update_time"`     // 更新时间
}

// TableName ...
func (s SchedulerTask) TableName() string {
	return "t_bot_task"
}

// SchedulerTaskLog 调度任务日志
type SchedulerTaskLog struct {
	ID             uint64    `gorm:"id"`              // ID
	UserID         uint64    `gorm:"user_id"`         // 用户ID
	Type           int8      `gorm:"task_type"`       // 任务类型
	Mutex          int8      `gorm:"task_mutex"`      // 任务互斥
	Params         string    `gorm:"params"`          // 参数
	RetryTimes     uint      `gorm:"retry_times"`     // 重试次数
	MaxRetryTimes  uint      `gorm:"max_retry_times"` // 最大重试次数
	Timeout        uint      `gorm:"timeout"`         // 超时时间
	Runner         string    `gorm:"runner"`          // 执行器
	RunnerInstance int64     `gorm:"runner_instance"` // 执行器实例
	Result         string    `gorm:"result"`          // 本次结果
	IsSuccess      string    `gorm:"is_success"`      // 是否成功
	TraceID        string    `gorm:"trace_id"`        // trace_id
	StartTime      time.Time `gorm:"start_time"`      // 任务开始执行时间
	EndTime        time.Time `gorm:"end_time"`        // 任务结束执行时间
	NextStartTime  time.Time `gorm:"next_start_time"` // 任务开始执行时间
	CreateTime     time.Time `gorm:"create_time"`     // 创建时间
}

// TableName ...
func (s SchedulerTaskLog) TableName() string {
	return "t_bot_task_history"
}

// TSchedulerTaskColumns 调度任务列
var TSchedulerTaskColumns = struct {
	ID             string
	UserID         string
	Type           string
	Mutex          string
	Params         string
	RetryTimes     string
	MaxRetryTimes  string
	Timeout        string
	Runner         string
	RunnerInstance string
	Result         string
	TraceID        string
	StartTime      string
	EndTime        string
	NextStartTime  string
	CreateTime     string
	UpdateTime     string
}{
	ID:             "id",
	UserID:         "user_id",
	Type:           "task_type",
	Mutex:          "task_mutex",
	Params:         "params",
	RetryTimes:     "retry_times",
	MaxRetryTimes:  "max_retry_times",
	Timeout:        "timeout",
	Runner:         "runner",
	RunnerInstance: "runner_instance",
	Result:         "result",
	TraceID:        "trace_id",
	StartTime:      "start_time",
	EndTime:        "end_time",
	NextStartTime:  "next_start_time",
	CreateTime:     "create_time",
	UpdateTime:     "update_time",
}

// TaskCopyExperienceWorkflowParams 复制体验中心下工作流参数
type TaskCopyExperienceWorkflowParams struct {
	RequestID         string `json:"request_id"`          // 请求ID
	Name              string `json:"name"`                // 任务名称
	CorpID            uint64 `json:"corp_id"`             // 企业ID
	StaffID           uint64 `json:"staff_id"`            // 员工ID
	RobotID           string `json:"robot_id"`            // 当前用户机器人ID
	ExperienceRobotID string `json:"experience_robot_id"` // 源目标体验中心机器人ID
	WorkflowID        string `json:"workflow_id"`         // 源目标体验中心当工作流模式下指定工作流ID
	NewWorkflowID     string `json:"new_workflow_id"`     // 当前应用下当工作流模式下替换后的工作流ID
	Uin               string `json:"uin"`                 // Uin
	SubUin            string `json:"sub_uin"`             // SubUin
	TaskID            uint64 `json:"task_id"`             // 本次操作的任务ID
}

// CommonSchedulerTaskFields 公共调度任务字段，工作流embedding向量升级查询任务时需要
type CommonSchedulerTaskFields struct {
	ID             uint64    `gorm:"id"`              // ID
	UserID         uint64    `gorm:"user_id"`         // 用户ID
	Type           int8      `gorm:"task_type"`       // 任务类型
	Mutex          int8      `gorm:"task_mutex"`      // 任务互斥
	Params         string    `gorm:"params"`          // 参数
	RetryTimes     uint      `gorm:"retry_times"`     // 重试次数
	MaxRetryTimes  uint      `gorm:"max_retry_times"` // 最大重试次数
	Timeout        uint      `gorm:"timeout"`         // 超时时间
	Runner         string    `gorm:"runner"`          // 执行器
	RunnerInstance int64     `gorm:"runner_instance"` // 执行器实例
	Result         string    `gorm:"result"`          // 本次结果
	TraceID        string    `gorm:"trace_id"`        // trace_id
	StartTime      time.Time `gorm:"start_time"`      // 任务开始执行时间
	EndTime        time.Time `gorm:"end_time"`        // 任务结束执行时间
	NextStartTime  time.Time `gorm:"next_start_time"` // 任务开始执行时间
	CreateTime     time.Time `gorm:"create_time"`     // 创建时间
}

// GetDBFields ...
func (c CommonSchedulerTaskFields) GetDBTags() []string {
	v := reflect.ValueOf(c)
	t := reflect.TypeOf(c)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
		t = t.Elem()
	}
	var tags []string
	for i := 0; i < v.NumField(); i++ {
		fieldType := t.Field(i)
		dbTag := fieldType.Tag.Get("gorm")
		if dbTag == "" {
			dbTag = fieldType.Tag.Get("db")
		}
		if dbTag == "" {
			continue
		}
		tags = append(tags, dbTag)
	}
	return tags
}

// TaskCopyAppWorkflowParams 复制指定应用下的工作流到另一个应用下
type TaskCopyAppWorkflowParams struct {
	RequestID       string `json:"request_id"`        // 请求ID
	Name            string `json:"name"`              // 任务名称
	CorpID          uint64 `json:"corp_id"`           // 企业ID
	StaffID         uint64 `json:"staff_id"`          // 员工ID
	RobotID         string `json:"robot_id"`          // 当前用户应用ID
	OriginalRobotID string `json:"original_robot_id"` // 原应用ID
	WorkflowID      string `json:"workflow_id"`       // 原应用下的单工作流模式下指定工作流ID
	NewWorkflowID   string `json:"new_workflow_id"`   // 当前应用下的单工作流模式下替换后的工作流ID
	Uin             string `json:"uin"`               // Uin
	SubUin          string `json:"sub_uin"`           // SubUin
	TaskID          uint64 `json:"task_id"`           // 本次操作的任务ID
}
