package entity

import (
	"encoding/json"
	"fmt"
	"time"

	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

const (
	// workflowRunKey [Redis]工作流异步任务Key（分布式锁）
	workflowRunKey = "WorkflowRun:Uin:%s"
	// workflowRunRetryKey [Redis]工作流异步任务定时重试Key(延时队列)
	workflowRunRetryKey = "WorkflowRun:Retry"
	// workflowRunRetryTimerKey [Redis]工作流异步任务定时重试Key，只有一个实例能够执行重试任务（分布式锁）
	workflowRunRetryTimerKey = "WorkflowRun:TimerLock:Retry"
	// workflowRunFallbackTimerKey [Redis]工作流异步任务定时兜底Key，只有一个实例能够执行兜底任务（分布式锁）
	workflowRunFallbackTimerKey = "WorkflowRun:TimerLock:Fallback"
)

// GetWorkflowRunLockKey获取工作流异步任务锁key
func GetWorkflowRunLockKey(uin string) string {
	return fmt.Sprintf(workflowRunKey, uin)
}

// GetWorkflowRunRetryKey 获取工作流异步任务定时重试key
func GetWorkflowRunRetryKey() string {
	return workflowRunRetryKey
}

// GetWorkflowRunRetryTimerLockKey 获取工作流异步任务定时兜底key
func GetWorkflowRunRetryTimerLockKey() string {
	return workflowRunRetryTimerKey
}

// GetWorkflowRunFallbackTimerLockKey 获取工作流异步任务定时兜底key
func GetWorkflowRunFallbackTimerLockKey() string {
	return workflowRunFallbackTimerKey
}

// WorkflowRun 对应 t_workflow_run 表，存储工作流异步任务信息
type WorkflowRun struct {
	ID              uint64     `gorm:"column:f_id"`                         // 自增ID
	UIN             string     `gorm:"column:f_uin"`                        // 主账号ID
	SubUIN          string     `gorm:"column:f_sub_uin"`                    // 子账号ID
	WorkflowRunID   string     `gorm:"column:f_workflow_run_id"`            // 工作流运行实例ID
	AppID           string     `gorm:"column:f_app_id"`                     // 应用ID
	WorkflowID      string     `gorm:"column:f_workflow_id"`                // 工作流ID
	Name            string     `gorm:"column:f_name"`                       // 工作流名称
	WorkflowJSON    string     `gorm:"column:f_workflow_json"`              // 工作流内容（JSON格式）
	ProtoVersion    uint8      `gorm:"column:f_proto_version"`              // 对话树的协议版本号
	RunEnv          string     `gorm:"column:f_run_env"`                    // 运行环境（TEST 或 PRODUCT）
	Query           string     `gorm:"column:f_query"`                      // 用户query
	MainModelName   string     `gorm:"column:f_main_model_name"`            // 主模型名称
	CustomVariables string     `gorm:"column:f_custom_variables"`           // API参数的值（JSON字符串）
	State           string     `gorm:"column:f_state"`                      // 工作流运行状态
	FailMessage     string     `gorm:"column:f_fail_message"`               // 错误信息
	TotalToken      uint32     `gorm:"column:f_total_token"`                // 当前请求消耗的token数
	WorkflowOutput  string     `gorm:"column:f_workflow_output"`            // 工作流输出
	StartTime       *time.Time `gorm:"column:f_start_time"`                 // 任务开始时间
	EndTime         *time.Time `gorm:"column:f_end_time"`                   // 任务结束时间
	CreateTime      *time.Time `gorm:"autoCreateTime;column:f_create_time"` // 创建时间
	UpdateTime      *time.Time `gorm:"autoUpdateTime;column:f_update_time"` // 更新时间
	IsDeleted       int        `gorm:"column:f_is_deleted"`                 // 0未删除 1已删除
}

// TableName Workflow表名
func (w WorkflowRun) TableName() string {
	return "t_workflow_run"
}

// 'PENDING', 'RUNNING', 'CANCELED', 'FAILED', 'SUCCESS'
const (
	WorkflowRunStatePending  string = "PENDING"
	WorkflowRunStateRunning  string = "RUNNING"
	WorkflowRunStateCanceled string = "CANCELED"
	WorkflowRunStateFailed   string = "FAILED"
	WorkflowRunStateSuccess  string = "SUCCESS"

	RunEnvTest    = "TEST"
	RunEnvProduct = "PROD"
)

func (w WorkflowRun) GetRunEnv() KEP_WF.EnvType {
	switch w.RunEnv {
	case RunEnvTest:
		return KEP_WF.EnvType_TEST
	case RunEnvProduct:
		return KEP_WF.EnvType_PROD
	}
	return KEP_WF.EnvType_PROD
}

// GetCustomVariables  将 JSON 字符串转换为 []*KEP_WF.CustomVariable
func (w WorkflowRun) GetCustomVariables() ([]*KEP_WF.CustomVariable, error) {
	if w.CustomVariables == "" {
		return make([]*KEP_WF.CustomVariable, 0), nil
	}
	var vars []KEP_WF.CustomVariable
	err := json.Unmarshal([]byte(w.CustomVariables), &vars)
	if err != nil {
		return nil, fmt.Errorf("json unmarshal error: %w", err)
	}
	var ptrVars []*KEP_WF.CustomVariable
	for i := range vars {
		ptrVars = append(ptrVars, &vars[i])
	}
	return ptrVars, nil
}

func (w WorkflowRun) GetWorkflowRunState() KEP_WF.WorkflowRunState {
	switch w.State {
	case WorkflowRunStatePending:
		return KEP_WF.WorkflowRunState_WORKFLOW_PENDING
	case WorkflowRunStateRunning:
		return KEP_WF.WorkflowRunState_WORKFLOW_RUNNING
	case WorkflowRunStateCanceled:
		return KEP_WF.WorkflowRunState_WORKFLOW_CANCELED
	case WorkflowRunStateFailed:
		return KEP_WF.WorkflowRunState_WORKFLOW_FAILED
	case WorkflowRunStateSuccess:
		return KEP_WF.WorkflowRunState_WORKFLOW_SUCCESS
	}
	return KEP_WF.WorkflowRunState_WORKFLOW_PENDING
}

// NodeRun 对应 t_node_run 表，存储工作流节点的运行信息
type NodeRun struct {
	ID               uint64     `gorm:"column:f_id"`                         // 自增ID
	WorkflowRunID    string     `gorm:"column:f_workflow_run_id"`            // 工作流运行实例ID
	NodeRunID        string     `gorm:"column:f_node_run_id"`                // 节点ID
	BelongNodeID     string     `gorm:"column:f_belong_node_id"`             // 所属的节点ID
	WorkflowID       string     `gorm:"column:f_workflow_id"`                // 所属工作流ID
	NodeID           string     `gorm:"column:f_node_id"`                    // 节点ID
	NodeName         string     `gorm:"column:f_node_name"`                  // 节点名称
	NodeType         string     `gorm:"column:f_node_type"`                  // 节点类型
	State            string     `gorm:"column:f_state"`                      // 节点运行状态
	FailCode         string     `gorm:"column:f_fail_code"`                  // 错误码
	FailMessage      string     `gorm:"column:f_fail_message"`               // 错误信息
	Input            string     `gorm:"column:f_input"`                      // 节点的输入
	InputRef         string     `gorm:"column:f_input_ref"`                  // 节点的输入的完整内容的链接
	Output           string     `gorm:"column:f_output"`                     // 节点的输出
	OutputRef        string     `gorm:"column:f_output_ref"`                 // 节点的输出的完整内容的链接
	TaskOutput       string     `gorm:"column:f_task_output"`                // 节点任务的原始输出
	TaskOutputRef    string     `gorm:"column:f_task_output_ref"`            // 节点任务的原始输出的完整内容的链接
	Log              string     `gorm:"column:f_log"`                        // 节点的日志
	LogRef           string     `gorm:"column:f_log_ref"`                    // 节点的日志的完整内容的链接
	CostMilliseconds uint32     `gorm:"column:f_cost_milliseconds"`          // 执行耗时（ms）
	StatisticInfos   string     `gorm:"column:f_statistic_infos"`            // LLM统计信息
	StartTime        *time.Time `gorm:"column:f_start_time"`                 // 运行的开始时间
	EndTime          *time.Time `gorm:"column:f_end_time"`                   // 运行的结束时间
	CreateTime       *time.Time `gorm:"autoCreateTime;column:f_create_time"` // 创建时间
	UpdateTime       *time.Time `gorm:"autoUpdateTime;column:f_update_time"` // 更新时间
}

// TableName Workflow表名
func (n NodeRun) TableName() string {
	return "t_node_run"
}

// 'INIT', 'RUNNING', 'CANCELED', 'FAILED', 'SUCCESS'
const (
	NodeRunStateInit     string = "INIT"
	NodeRunStateRunning  string = "RUNNING"
	NodeRunStateCanceled string = "CANCELED"
	NodeRunStateFailed   string = "FAILED"
	NodeRunStateSuccess  string = "SUCCESS"
)

func (n NodeRun) GetNodeRunState() KEP_WF.NodeRunState {
	// 不能通过KEP_WF.NodeRunState_value[n.State]获取，因为db里存的是字符串与枚举值不同
	// state, ok := KEP_WF.NodeRunState_value[n.State]
	// if !ok {
	// 	return KEP_WF.NodeRunState_NODE_FAILED
	// }
	// return KEP_WF.NodeRunState(state)
	switch n.State {
	case NodeRunStateInit:
		return KEP_WF.NodeRunState_NODE_INIT
	case NodeRunStateRunning:
		return KEP_WF.NodeRunState_NODE_RUNNING
	case NodeRunStateCanceled:
		return KEP_WF.NodeRunState_NODE_CANCELED
	case NodeRunStateFailed:
		return KEP_WF.NodeRunState_NODE_FAILED
	case NodeRunStateSuccess:
		return KEP_WF.NodeRunState_NODE_SUCCESS
	}
	return KEP_WF.NodeRunState_NODE_FAILED
}

func (n NodeRun) GetNodeType() KEP_WF.NodeType {
	nodeType, ok := KEP_WF.NodeType_value[n.NodeType]
	if !ok {
		return KEP_WF.NodeType_UNKNOWN
	}
	return KEP_WF.NodeType(nodeType)
}

func (n NodeRun) GetTotalTokens() (uint32, error) {
	statisticInfos, err := n.GetStatisticInfos()
	if err != nil {
		return 0, err
	}
	var totalTokens uint32 // kinvo: db里没有统计token数量,需要从 StatisticInfos 取合
	for _, info := range statisticInfos {
		totalTokens += info.GetTotalTokens()
	}
	return totalTokens, nil
}

// GetStatisticInfos  将 JSON 字符串转换为 []*StatisticInfo
func (n NodeRun) GetStatisticInfos() ([]*KEP_WF.StatisticInfo, error) {
	if n.StatisticInfos == "" {
		return make([]*KEP_WF.StatisticInfo, 0), nil
	}
	var stats []KEP_WF.StatisticInfo
	err := json.Unmarshal([]byte(n.StatisticInfos), &stats)
	if err != nil {
		return nil, fmt.Errorf("json unmarshal error: %w", err)
	}
	var ptrStats []*KEP_WF.StatisticInfo
	for i := range stats {
		ptrStats = append(ptrStats, &stats[i])
	}
	return ptrStats, nil
}

// WorkflowCustomConfig 应用的工作流配置
type WorkflowCustomConfig struct {
	ID              uint64    `gorm:"column:f_id"`                                          // 自增ID
	AppID           string    `gorm:"column:f_app_id"`                                      // 应用ID
	DebugMode       string    `gorm:"column:f_debug_mode"`                                  // 调试模式
	CustomVariables string    `gorm:"column:f_custom_variables"`                            // API参数的值（JSON字符串）
	IsDeleted       int       `gorm:"column:f_is_deleted"`                                  // 0未删除 1已删除
	Uin             string    `gorm:"column:f_uin"`                                         // 主用户ID
	SubUin          string    `gorm:"column:f_sub_uin"`                                     // 子用户ID
	CreateTime      time.Time `gorm:"column:f_create_time;type:datetime;null;default:null"` // 创建时间
	UpdateTime      time.Time `gorm:"column:f_update_time;type:datetime;null;default:null"` // 更新时间
}

// TableName WorkflowCustomConfig
func (i WorkflowCustomConfig) TableName() string {
	return "t_app_workflow_config"
}
