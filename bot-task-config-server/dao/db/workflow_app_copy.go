// bot-task-config-server
//
// @(#)workflow_experience.go  星期四, 六月 05, 2025
// Copyright(c) 2025, mikeljiang@Tencent. All rights reserved.

package db

import (
	"context"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
)

// GetWorkflowIdsByAppID 获取指定应用下存在的所有工作流
func GetWorkflowIdsByAppID(ctx context.Context, robotID string) (
	[]*entity.ExportWorkflow, error) {
	if len(robotID) == 0 {
		log.WarnContextf(ctx, "GetWorkflowIdsByAppID robotId is empty")
		return nil, errors.BadWorkflowReqError("请求参数没有应用ID")
	}
	log.InfoContextf(ctx, "GetWorkflowIdsByAppID robotID:%s", robotID)
	var workflowList []*entity.ExportWorkflow
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	// 查询符合条件的记录
	db = db.Table(entity.Workflow{}.TableName()).Select("f_workflow_id,f_workflow_name,f_desc").
		Where("f_is_deleted = 0 and f_robot_id = ?", robotID)
	err := db.Find(&workflowList).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowIdsByAppID failed err:%+v", err)
		return nil, err
	}
	return workflowList, nil
}

// GetVarParamsInfosByAppID 获取指定应用下存在的所有变量
func GetVarParamsInfosByAppID(ctx context.Context, robotID string) (map[string]*entity.VarParams, error) {
	db := database.GetLLMRobotTaskGORM().WithContext(ctx).Debug()
	if len(robotID) == 0 {
		log.WarnContextf(ctx, "GetVarParamsInfosByAppID robotId is empty")
		return nil, errors.BadWorkflowReqError("请求参数没有应用ID")
	}
	log.InfoContextf(ctx, "GetVarParamsInfosByAppID robotID:%s", robotID)
	var varParams []*entity.VarParams
	db = db.Table(entity.VarParams{}.TableName()).Select("f_var_id, f_var_name,f_var_desc,f_var_type,"+
		"f_var_default_value, f_var_default_file_name").
		Where("f_is_deleted=0 AND f_app_id = ?", robotID)
	if err := db.Find(&varParams).Error; err != nil {
		log.ErrorContextf(ctx, "GetVarParamsInfosByAppID db.Find Failed,err:%v", err)
		return nil, err
	}
	varParamsMap := make(map[string]*entity.VarParams)
	for _, v := range varParams {
		varParamsMap[v.VarName] = v
	}

	return varParamsMap, nil
}
