// bot-task-config-server
//
// @(#)workflow.go  星期四, 九月 26, 2024
// Copyright(c) 2024, mikeljiang@Tencent. All rights reserved.

package db

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/rpc"
	vdao "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/dao/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/entity"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/database"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	utilsErrors "git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/errors"
	"git.woa.com/dialogue-platform/bot-config/bot-task-config-server/util/types"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
	"gorm.io/gorm"
)

const (
	queryWorkFLowCountByName = "select count(*) from t_workflow where f_is_deleted = 0 and f_robot_id = ? " +
		"and f_workflow_name = ? and f_workflow_id != ?"

	//queryWorkFLowCountByRefFlowIds = "select count(*) from t_workflow_reference where f_is_deleted = 0"
)

// CreateWorkflow 创建工作流 及 绑定关系
func CreateWorkflow(ctx context.Context, params entity.CreateWorkflowParams) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	if err := db.Transaction(func(tx *gorm.DB) error {
		return TxCreateWorkflow(ctx, tx, params)
	}); err != nil {
		log.ErrorContextf(ctx, "CreateWorkflow fail, err:%+v", err)
		return err
	}
	return nil
}

// TxCreateWorkflow 创建工作流 及 绑定关系
func TxCreateWorkflow(ctx context.Context, tx *gorm.DB, params entity.CreateWorkflowParams) error {
	// 任务流数量限制
	var currentTotal int64
	err := tx.Model(&entity.Workflow{}).Select("f_workflow_id").
		Where("f_is_deleted = ? and f_robot_id = ?", entity.TaskFlowUnDeleted, params.RobotId).
		Count(&currentTotal).Error
	if err != nil {
		return err
	}
	maxTotal := int64(config.GetMainConfig().VerifyWorkflow.WorkflowLimit)
	sid := util.SID(ctx) // 1为腾讯云账户，2为企点集成商
	if sid == config.GetMainConfig().QidianSid {
		num, err := rpc.GetQdAccountWorkflowLimit(ctx, util.LoginUin(ctx), params.RobotId)
		if err == nil && num != -1 {
			maxTotal = num
		}
	}
	log.InfoContextf(ctx, "TxCreateWorkflow currentTotal:%d, maxTotal:%d",
		currentTotal, maxTotal)
	if currentTotal >= maxTotal {
		return utilsErrors.ErrWorkflowLimit
	}
	return createWorkflow(ctx, tx, params)
}

// createWorkflow 创建工作流相关信息入库
func createWorkflow(ctx context.Context, tx *gorm.DB, params entity.CreateWorkflowParams) error {
	// 1. 新建工作流
	err := saveWorkflow(ctx, tx, params)
	if err != nil {
		return err
	}

	// 2. 绑定机器人和工作流
	err = saveRobotWorkflow(ctx, tx, params)
	if err != nil {
		return err
	}

	// 3. 创建工作流时，获取向量GroupId，没有就创建；
	if _, _, _, err = vdao.NewDao().GetWorkflowVectorGroupId(ctx, tx, params.RobotId); err != nil {
		log.WarnContextf(ctx, "createWorkflow GetWorkflowVectorGroupId err:%v", err)
		return err
	}

	log.InfoContextf(ctx, "createWorkflow success")
	return nil
}

// GetWorkFLowCountByName 通过名称和ID查询工作流名称是否存在
func GetWorkFLowCountByName(ctx context.Context, flowId, botBizId, workFlowName string) (int, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	querySql := queryWorkFLowCountByName
	paramValues := make([]interface{}, 0)
	paramValues = append(paramValues, botBizId, workFlowName, flowId)
	var count int
	err := db.Raw(querySql, paramValues...).Scan(&count).Error
	if err != nil && err != sql.ErrNoRows {
		log.Errorf("GetWorkFLowCountByName query sql:%s|err:%v", querySql, err)
		return count, err
	}
	return count, nil
}

// GetProductWorkFLowCountByName 通过名称和ID查询工作流名称在线上是否存在
func GetProductWorkFLowCountByName(ctx context.Context, flowId, botBizId, workFlowName string) (int, error) {
	db := database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	querySql := queryWorkFLowCountByName
	paramValues := make([]interface{}, 0)
	paramValues = append(paramValues, botBizId, workFlowName, flowId)
	var count int
	err := db.Raw(querySql, paramValues...).Scan(&count).Error
	if err != nil && err != sql.ErrNoRows {
		log.Errorf("GetProductWorkFLowCountByName query sql:%s|err:%v", querySql, err)
		return count, err
	}
	return count, nil
}

// GetWorkflowNamesByFlowIds 批量查询工作流名称和描述
func GetWorkflowNamesByFlowIds(ctx context.Context, flowIds []string) (map[string]*entity.Workflow, error) {
	if len(flowIds) == 0 {
		return nil, nil
	}
	list := make([]*entity.Workflow, 0)
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Table(entity.Workflow{}.TableName()).Select("f_workflow_id,f_workflow_name,f_desc,"+
		"f_release_status, f_flow_state").
		Where("f_is_deleted = 0 ").
		Where("f_workflow_id IN (?)", flowIds).Find(&list).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowNamesByFlowIds err：%v", err)
		return nil, err
	}
	workflowMap := make(map[string]*entity.Workflow, 0)
	for _, item := range list {
		workflowMap[item.WorkflowID] = item
	}
	return workflowMap, nil
}

// GetWorkFLowRefCountByFlowIds 批量查询工作流引用合理性
func GetWorkFLowRefCountByFlowIds(ctx context.Context, workflowID string, workflowRefIds []string) error {
	if len(workflowRefIds) == 0 || workflowID == "" {
		return nil
	}
	if util.ContainsList(workflowRefIds, workflowID) {
		return errors.ErrWorkflowQuoteItself
	}

	list := make([]*entity.WorkflowReference, 0)
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Table(entity.WorkflowReference{}.TableName()).
		Where("f_is_deleted=?", 0).
		Where("f_workflow_id IN (?)", workflowRefIds).Find(&list).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkFLowRefCountByFlowIds err:%v", err)
		return err
	}
	if len(list) > 0 {
		return errors.ErrWorkflowHasRef
	}
	return nil
}

// saveWorkflow 创建工作流
func saveWorkflow(ctx context.Context, tx *gorm.DB, params entity.CreateWorkflowParams) error {
	workFlow := &entity.Workflow{
		WorkflowID:      params.WorkflowID,
		WorkflowName:    params.WorkflowName,
		WorkflowDesc:    params.WorkflowDesc,
		WorkflowState:   params.WorkflowState,
		Version:         params.Version,
		RobotId:         params.RobotId,
		DialogJsonDraft: params.DialogJsonDraft,
		Uin:             params.Uin,
		SubUin:          params.SubUin,
		ReleaseStatus:   params.ReleaseStatus,
		StaffID:         params.StaffID,
		Action:          params.Action,
		ProtoVersion:    int32(KEP_WF.WorkflowProtoVersion_V2_6),
	}
	if err := tx.Table(workFlow.TableName()).Create(workFlow).Error; err != nil {
		log.ErrorContextf(ctx, "saveWorkflow err:%v", err)
		return err
	}
	return nil
}

// saveRobotWorkflow 创建机器人与工作流关联关系
func saveRobotWorkflow(ctx context.Context, tx *gorm.DB, params entity.CreateWorkflowParams) error {
	robotWorkflow := &entity.RobotWorkflow{
		RobotID:       params.RobotId,
		WorkflowID:    params.WorkflowID,
		ReleaseStatus: params.ReleaseStatus,
		Action:        params.Action,
	}
	if err := tx.Table(robotWorkflow.TableName()).Create(robotWorkflow).Error; err != nil {
		log.ErrorContextf(ctx, "saveRobotWorkflow err:%v", err)
		return err
	}
	return nil
}

// txUpdateWorkflowByFlowInfo 通过工作流程流信息更新t_workflow表
func txUpdateWorkflowByFlowInfo(ctx context.Context, tx *gorm.DB, flowInfos []entity.Workflow, appId string) error {
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	for _, workflowInfo := range flowInfos {
		if workflowInfo.IsDeleted != 0 {
			continue
		}
		flowState, flowAction, err := TransformWorkflowState(ctx, workflowInfo, uint32(KEP_WF.SaveWorkflowReq_DRAFT))
		if err != nil {
			log.ErrorContextf(ctx, "txUpdateWorkflowByFlowInfo TransformWorkflowState Failed err:%v", err)
			return err
		}
		workflowInfo.WorkflowState = flowState
		err = SaveWorkflowVectorAndRedis(ctx, tx, &workflowInfo, appId, entity.VectorWorkflowEnable)
		if err != nil {
			log.ErrorContextf(ctx, "txUpdateWorkflowByFlowInfo|SaveWorkflowCorpusVector|err:%v", err)
			return err
		}
		err = tx.Model(&entity.Workflow{}).
			Where("f_workflow_id = ?", workflowInfo.WorkflowID).
			Updates(map[string]interface{}{
				"f_action":         flowAction,
				"f_release_status": entity.ReleaseStatusUnPublished,
				"f_flow_state":     flowState,
				"f_update_time":    time.Now(),
				"f_uin":            uin,
				"f_sub_uin":        subUin,
			}).Error
		if err != nil {
			log.ErrorContextf(ctx, "txUpdateWorkflowByFlowInfo|err:%v", err)
			return err
		}
	}
	return nil
}

// DeleteWorkflow 逻辑删除Workflow
func DeleteWorkflow(ctx context.Context, appBizId string, flowIds []string) error {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workFlows []entity.Workflow
	var err error
	if err = db.Transaction(func(tx *gorm.DB) error {
		log.InfoContextf(ctx, "DeleteWorkflow appBizId:%s flowIds:%+v", appBizId, flowIds)
		//1. 删除t_workflow表记录
		if workFlows, err = deleteWorkflowByFlowIds(tx, appBizId, flowIds, ctx); err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow deleteWorkflowByFlowIds err:%+v", err)
			return err
		}
		// 1-2 删除t_robot_workflow表记录
		if err = deleteRobotWorkflowByFlowIds(tx, appBizId, flowIds, ctx); err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow deleteRobotWorkflowByFlowIds err:%+v", err)
			return err
		}
		// 1-3 删除t_workflow_reference表记录
		if err = deleteWorkflowReferenceByFlowIds(tx, appBizId, flowIds, ctx); err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow deleteWorkflowReferenceByFlowIds err:%+v", err)
			return err
		}
		// 1-4 删除t_workflow_plugin表记录
		if err = deleteWorkflowRefPluginByFlowIds(tx, appBizId, flowIds, ctx); err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow deleteWorkflowRefPluginByFlowIds err:%+v", err)
			return err
		}
		// 1-5 删除t_workflow_var表记录
		if err = deleteWorkflowVarByFlowIds(tx, appBizId, flowIds, ctx); err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow deleteWorkflowVarByFlowIds err:%+v", err)
			return err
		}
		// 1-6 删除t_workflow_ref_knowledge表记录
		if err = deleteWorkflowRefKnowledgeByFlowIds(tx, appBizId, flowIds, ctx); err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow deleteWorkflowRefKnowledgeByFlowIds err:%+v", err)
			return err
		}
		// 1-7 删除关联的工作流反馈
		if err = DeleteWorkflowFeedbackByFlowIdTx(ctx, tx, flowIds, appBizId); err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow|DeleteWorkflowFeedbackByFlowIdTx err:%+v", err)
			return err
		}
		// 1-8 删除参数节点相关的数据
		if err = deleteWorkflowParameterEntryByFlowIds(ctx, tx, appBizId, flowIds); err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow deleteWorkflowParameterEntryByFlowIds err:%+v", err)
			return err
		}
		// 1-9 删除示例问法表
		examples, err := deleteWorkflowExampleByFlowIds(tx, appBizId, flowIds, ctx)
		if err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow|deleteWorkflowExampleByFlowIds:%+v,err:%+v", flowIds, err)
			return err
		}
		// 1-10. 找出校验过的工作流程，通知dm,组装任务流删除数据给dm
		err = deleteWorkflowToDm(workFlows, ctx, appBizId)
		if err != nil {
			return err
		}
		// 1-11 删除向量
		if err = deleteWorkflowVector(ctx, tx, appBizId, flowIds, examples); err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow|deleteWorkflowVector|err:%v", err)
			return err
		}
		// 1-12 删除redis里的可用状态
		if err = deleteWorkflowEnableRdsByFlowIds(ctx, appBizId, flowIds); err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow|deleteWorkflowEnableRdsByFlowIds|err:%v", err)
			return err
		}
		// 1-13 2.7需求:删除WorkflowPDL
		_, err = deleteWorkflowPDLByFlowIds(ctx, tx, appBizId, flowIds)
		if err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow|deleteWorkflowPDLByFlowIds|err:%v", err)
			return err
		}
		// 1-14 v2.7.1 删除工作流引用自定义模型
		if err = deleteWorkflowCustomModel(ctx, tx, appBizId, flowIds); err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow|deleteWorkflowCustomModel|err:%v", err)
			return err
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflow fail, err:%+v", err)
		return err
	}
	return nil
}

func deleteWorkflowCustomModel(ctx context.Context, tx *gorm.DB, appBizID string, flowIds []string) error {
	if err := tx.Table(entity.WorkflowCustomModel{}.TableName()).
		Where("f_is_deleted = 0 AND f_robot_id = ? AND f_workflow_id IN (?)", appBizID, flowIds).
		Updates(map[string]interface{}{
			"f_is_deleted": 1, "f_action": entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
			"f_staff_id":       util.StaffID(ctx),
			"f_update_time":    time.Now(),
		}).Error; err != nil {
		return err
	}
	return nil
}

func deleteWorkflowEnableRdsByFlowIds(ctx context.Context, appBizId string, flowIds []string) error {
	key := fmt.Sprintf(entity.WfEnableRedisKey, entity.SandboxEnv, appBizId)

	if err := database.GetRedis().HDel(ctx, key, flowIds...).Err(); err != nil {
		log.ErrorContextf(ctx, "HDEL workflow enable from redis Failed! err:%+v", err)
		return err
	}
	return nil
}
func deleteWorkflowToDm(workFlows []entity.Workflow, ctx context.Context, appBizId string) error {
	var dmWorkflows []string
	for _, flow := range workFlows {
		if flow.DialogJsonEnable != "" {
			dmWorkflows = append(dmWorkflows, flow.WorkflowID)
		}
	}
	if len(dmWorkflows) > 0 {
		log.InfoContextf(ctx, "DeleteWorkflow deleteWorkflowToDM,"+
			"appBizId:%s, flowIds:%+v", appBizId, dmWorkflows)
		_, err := deleteWorkflowToDM(ctx, appBizId, dmWorkflows)
		if err != nil {
			log.ErrorContextf(ctx, "DeleteWorkflow deleteWorkflowToDM err: %v", err)
			return err
		}
	}
	return nil
}

func deleteWorkflowVector(ctx context.Context, tx *gorm.DB, appBizId string,
	flowIds []string, examples []*entity.WorkflowExample) error {
	vdb := vdao.NewDao()
	ids := make([]string, 0)
	ids = append(ids, flowIds...)
	for _, e := range examples {
		ids = append(ids, e.ExampleID)
	}

	groupInfo, err := vdao.GetWorkflowVectorInfoByRobotIdAndType(ctx, tx, appBizId, entity.WorkflowNew)
	if err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflow|GetWorkflowVectorInfoByRobotIdAndType:%+v,err:%+v", flowIds, err)
		return err
	}
	if groupInfo == nil {
		log.ErrorContextf(ctx, "DeleteWorkflow|not get groupId")
		return errors.ErrVectorGroupNotFound
	}
	sandboxGroupId := ""
	modelName := ""
	log.DebugContextf(ctx, "DeleteWorkflow:groupInfo:%+v", groupInfo)
	for _, v := range groupInfo {
		if v.VectorGroupType == vdao.WorkflowSandboxGroupInfix {
			sandboxGroupId = v.VectorGroupID
			modelName = v.EmbeddingModeName
			break
		}
	}
	if sandboxGroupId == "" {
		log.ErrorContextf(ctx, "DeleteWorkflow|not get groupId")
		return errors.ErrVectorGroupNotFound
	}
	// 获取模型信息
	eModelInfo := config.GetUsingVectorModelInfo(ctx, modelName)
	//判断是否有 工作流+示例问法+描述 模版
	if eModelInfo != nil && len(eModelInfo.WorkflowCombinationTemplate) > 0 {
		for _, wfId := range flowIds {
			wfExamDescId := fmt.Sprintf(entity.FlowNameExamsDescID, wfId)
			ids = append(ids, wfExamDescId)
		}
	}

	log.DebugContextf(ctx, "DeleteWorkflow:sandboxGroupId:%s|modelName:%s", sandboxGroupId, modelName)
	// 删除向量处理
	if err := vdb.DelWorkflowCorpusVector(ctx, appBizId, sandboxGroupId, modelName, ids); err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflow|DelWorkflowCorpusVector:%+v,err:%+v", flowIds, err)
		return err
	}
	// 删除VectorStore
	if err := DelVectorStore(ctx, tx, appBizId, ids); err != nil {
		log.ErrorContextf(ctx, "DeleteWorkflow|DelVectorStore:%+v,err:%+v", flowIds, err)
		return err
	}
	return nil
}

// deleteWorkflowByFlowIds 根据工作流ID，批量删除工作流
func deleteWorkflowByFlowIds(tx *gorm.DB, appBizId string, flowIds []string,
	ctx context.Context) ([]entity.Workflow, error) {
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	var workFlows []entity.Workflow
	if err := tx.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted = 0 and f_robot_id = ? and f_workflow_id IN (?)", appBizId, flowIds).
		Where("f_release_status != ?", entity.ReleaseStatusPublishing).
		Find(&workFlows).Updates(map[string]interface{}{
		"f_uin":            uin,
		"f_sub_uin":        subUin,
		"f_is_deleted":     1,
		"f_action":         entity.ActionDelete,
		"f_release_status": entity.ReleaseStatusUnPublished,
	}).Error; err != nil {
		log.ErrorContextf(ctx, "deleteWorkflowByFlowIds failed:%+v,err:%+v", flowIds, err)
		return workFlows, err
	}
	// 修复已发布-草稿态，已发布的草稿态，如果删除了，需要发布删除，要不存在问题
	if err := updateDeleteWorkflowStatePublishedDraft(ctx, tx, appBizId, workFlows); err != nil {
		log.ErrorContextf(ctx, "deleteWorkflowByFlowIds failed:%+v,err:%+v", flowIds, err)
		return workFlows, err
	}
	return workFlows, nil
}

// updateDeleteWorkflowStatePublishedDraft 将已发布草稿态 更新f_flow_state 为 WorkflowStatePublishedChange
func updateDeleteWorkflowStatePublishedDraft(ctx context.Context, tx *gorm.DB, appBizId string,
	workflows []entity.Workflow) error {
	// 获取flowSate
	pDraftIds := make([]string, 0)
	for _, v := range workflows {
		// 已发布草稿态需要变为 PUBLISHED_CHANGE
		if v.WorkflowState == entity.WorkflowStatePublishedDraft {
			pDraftIds = append(pDraftIds, v.WorkflowID)
		}
	}
	if len(pDraftIds) > 0 {
		if err := tx.Table(entity.Workflow{}.TableName()).
			Where("f_flow_state = ? AND f_robot_id = ? and f_workflow_id IN (?)",
				entity.WorkflowStatePublishedDraft, appBizId, pDraftIds).
			Updates(map[string]interface{}{
				"f_flow_state": entity.WorkflowStatePublishedChange,
			}).Error; err != nil {
			log.ErrorContextf(ctx, "deleteWorkflowByFlowIds failed|err:%+v", err)
			return err
		}
	}

	return nil
}

// deleteRobotWorkflowByFlowIds 根据工作流ID，批量删除工作流和引用关联表
func deleteRobotWorkflowByFlowIds(tx *gorm.DB, appBizId string, flowIds []string, ctx context.Context) error {
	if err := tx.Table(entity.RobotWorkflow{}.TableName()).
		Where("f_is_deleted = 0 and f_robot_id = ? and f_workflow_id IN (?)", appBizId, flowIds).
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "deleteRobotWorkflowByFlowIds failed:%+v,err:%+v", flowIds, err)
		return err
	}
	return nil
}

// deleteWorkflowExampleByFlowIds 根据工作流ID，批量删除工作流示例问法
func deleteWorkflowExampleByFlowIds(tx *gorm.DB, appBizId string, flowIds []string, ctx context.Context) ([]*entity.WorkflowExample, error) {
	var examples []*entity.WorkflowExample
	if err := tx.Table(entity.WorkflowExample{}.TableName()).
		Where("f_is_deleted = 0 and f_robot_id = ? and f_workflow_id IN (?)", appBizId, flowIds).
		Find(&examples).
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "deleteWorkflowExampleByFlowIds failed:%+v,err:%+v", flowIds, err)
		return examples, err
	}

	// 删除redis中的示例问法
	delFlowMapExamIds := make(map[string][]string, 0)
	for _, v := range examples {
		delFlowMapExamIds[v.FlowID] = append(delFlowMapExamIds[v.FlowID], v.ExampleID)
	}

	for _, fId := range flowIds {
		if len(delFlowMapExamIds[fId]) > 0 {
			delFlowMapExamIds[fId] = append(delFlowMapExamIds[fId], fId)
			key := GetFlowExampleKey(entity.SandboxEnv, appBizId)
			err := database.GetRedis().HDel(ctx, key, delFlowMapExamIds[fId]...).Err()
			if err != nil {
				log.ErrorContextf(ctx, "HDEL ExampleKey Failed! err:%+v", err)
				return examples, err
			}
		}
	}

	return examples, nil
}

// deleteWorkflowReferenceByFlowIds 根据工作流ID，批量删除工作流引用工作流表
func deleteWorkflowReferenceByFlowIds(tx *gorm.DB, appBizId string, flowIds []string, ctx context.Context) error {
	if err := tx.Table(entity.WorkflowReference{}.TableName()).
		Where("f_is_deleted = 0 and f_robot_id = ? and f_workflow_id IN (?)", appBizId, flowIds).
		Updates(map[string]interface{}{"f_is_deleted": 1}).Error; err != nil {
		log.ErrorContextf(ctx, "deleteWorkflowReferenceByFlowIds failed:%+v,err:%+v", flowIds, err)
		return err
	}
	return nil
}

// deleteWorkflowRefPluginByFlowIds 根据工作流ID，批量删除工作流引用插件工具表
func deleteWorkflowRefPluginByFlowIds(tx *gorm.DB, appBizId string, flowIds []string, ctx context.Context) error {
	if err := tx.Table(entity.WorkflowRefPlugin{}.TableName()).
		Where("f_is_deleted = 0 and f_robot_id = ? and f_workflow_id IN (?)", appBizId, flowIds).
		Updates(map[string]interface{}{"f_is_deleted": 1}).Error; err != nil {
		log.ErrorContextf(ctx, "deleteWorkflowRefPluginByFlowIds failed:%+v,err:%+v", flowIds, err)
		return err
	}
	return nil
}

// deleteWorkflowVarByFlowIds 根据工作流ID，批量删除工作流引用变量表
func deleteWorkflowVarByFlowIds(tx *gorm.DB, appBizId string, flowIds []string, ctx context.Context) error {
	var workflowVarList []entity.WorkflowVar
	if err := tx.Table(entity.WorkflowVar{}.TableName()).
		Where("f_is_deleted = 0 and f_robot_id = ? and f_workflow_id IN (?)", appBizId, flowIds).
		Scan(&workflowVarList).Error; err != nil {
		log.ErrorContextf(ctx, "deleteWorkflowVarByFlowIds GetWorkflowVarListByFlowIds:err:%v", err)
		return err
	}
	if len(workflowVarList) == 0 {
		return nil
	}
	var varIdList []string
	for _, workflowVar := range workflowVarList {
		varIdList = append(varIdList, workflowVar.VarID)
	}
	if err := tx.Table(entity.WorkflowVar{}.TableName()).
		Where("f_is_deleted = 0 and f_robot_id = ? AND f_workflow_id IN (?)", appBizId, flowIds).
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "deleteWorkflowVarByFlowIds failed:%+v,err:%+v", flowIds, err)
		return err
	}
	return nil
}

// deleteWorkflowRefKnowledgeByFlowIds 根据工作流ID，批量删除工作流引用知识型记录
func deleteWorkflowRefKnowledgeByFlowIds(tx *gorm.DB, appBizId string, flowIds []string, ctx context.Context) error {
	if err := tx.Table(entity.WorkflowRefKnowledge{}.TableName()).
		Where("f_is_deleted = 0 and f_robot_id = ? and f_workflow_id IN (?)", appBizId, flowIds).
		Updates(map[string]interface{}{
			"f_is_deleted": 1,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "deleteWorkflowRefKnowledgeByFlowIds failed:%+v,err:%+v", flowIds, err)
		return err
	}
	return nil
}

// deleteWorkflowParameterEntryByFlowIds 根据工作流ID，批量删除工作流参数提取节点，参数正确示例，参数错误示例信息
func deleteWorkflowParameterEntryByFlowIds(ctx context.Context, tx *gorm.DB, robotID string, flowIds []string) error {
	// 1. 根据workflow查找paramIds
	var paramIds []string
	err := tx.Model(&entity.WorkflowParameter{}).
		Where("f_workflow_id IN (?) ", flowIds).Pluck("f_parameter_id", &paramIds).Error
	if err != nil {
		log.ErrorContextf(ctx, "deleteWorkflowParameterEntryByFlowIds find workflowParam Failed! err:%+v", err)
		return err
	}
	if len(paramIds) == 0 {
		return nil
	}
	// 更新
	if err := tx.Model(&entity.WorkflowParameter{}).Where("f_workflow_id IN (?)", flowIds).
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "delete WorkflowParameter failed:flowIDs:%+v,err:%+v", flowIds, err)
		return err
	}

	if err := tx.Model(&entity.Parameter{}).Where("f_parameter_id IN (?)", paramIds).
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "delete Parameter failed:,paramIds:%+v,err:%+v", paramIds, err)
		return err
	}

	// 删除参数通知dm
	deleteParametersReq := &KEP_WF_DM.DeleteParametersInSandboxRequest{
		AppID:        robotID,
		ParameterIDs: paramIds,
	}
	_, err = rpc.DeleteParametersInSandbox(ctx, deleteParametersReq)
	if err != nil {
		return err
	}

	return nil
}

// UpdateWorkflowVarParamsRef 更新自定义变量与工作流的关系
func UpdateWorkflowVarParamsRef(ctx context.Context, tx *gorm.DB,
	robotId, workflowID string, newVarParamsIDs []string) error {
	log.InfoContextf(ctx, "UpdateWorkflowVarParamsRef workflowID:%s|newVarParamsIDs:%+v",
		workflowID, newVarParamsIDs)
	if len(workflowID) > 0 {
		// 查询历史数据
		var oldDataList []entity.WorkflowVar
		if err := tx.Table(entity.WorkflowVar{}.TableName()).
			Where("f_is_deleted = 0 AND f_workflow_id = ? AND f_robot_id = ? ", workflowID, robotId).
			Scan(&oldDataList).Error; err != nil {
			log.ErrorContextf(ctx, "updateWorkflowVarParamsRef get old err:%v", err)
			return err
		}
		// 对比新旧数据，筛选新增和删除的数据
		insertData, deleteData, updateData := compareWorkflowVarParamsRefRef(newVarParamsIDs, oldDataList)
		log.InfoContextf(ctx, "updateWorkflowVarParamsRef compareWorkflowVarParamsRefRef "+
			"insertData:%+v|deleteData:%+v|updateData:%+v", insertData, deleteData, updateData)
		// 删除历史引用关系
		if len(deleteData) > 0 {
			if err := tx.Table(entity.WorkflowVar{}.TableName()).
				Where("f_id in ? ", deleteData).
				Where("f_is_deleted = 0").Updates(map[string]interface{}{
				"f_is_deleted": 1, "f_action": entity.ActionDelete, "f_release_status": entity.ReleaseStatusUnPublished,
				"f_update_time": time.Now(),
			}).Error; err != nil {
				log.ErrorContextf(ctx, "updateWorkflowVarParamsRef delete err:%+v", err)
				return err
			}
		}
		//// 更新引用关系
		//if len(updateData) > 0 {
		//	if err := tx.Table(entity.WorkflowVar{}.TableName()).
		//		Where("f_workflow_id = ? ", workflowID).Where("f_var_id IN (?)", updateData).
		//		Where("f_is_deleted = 0").Updates(map[string]interface{}{
		//		"f_action":         entity.ActionUpdate,
		//		"f_release_status": entity.ReleaseStatusUnPublished,
		//		"f_update_time":    time.Now(),
		//	}).Error; err != nil {
		//		log.ErrorContextf(ctx, "updateWorkflowVarParamsRef update :%+v|err:%+v", updateData, err)
		//		return err
		//	}
		//}
		// 创建新引用关系
		if len(insertData) > 0 {
			insertWorkflowVars := make([]entity.WorkflowVar, 0)
			for _, varID := range insertData {
				// 创建新的记录
				insertWorkflowVars = append(insertWorkflowVars, entity.WorkflowVar{
					WorkflowID: workflowID, VarID: varID, RobotID: robotId,
					ReleaseStatus: entity.ReleaseStatusUnPublished,
					IsDeleted:     0, Action: entity.ActionInsert,
					CreateTime: time.Now(), UpdateTime: time.Now(),
				})

			}
			if err := tx.Table(entity.WorkflowVar{}.TableName()).Create(&insertWorkflowVars).Error; err != nil {
				log.ErrorContextf(ctx, "updateWorkflowVarParamsRef insert err:%+v", insertWorkflowVars, err)
				return err
			}
		}

	}
	return nil
}

// compareWorkflowVarParamsRefRef 对比工作流引用自定义变量新老数据
func compareWorkflowVarParamsRefRef(newDataList []string,
	oldDataList []entity.WorkflowVar) ([]string, []uint64, []string) {
	insertData := make([]string, 0)
	deleteData := make([]uint64, 0)
	updateData := make([]string, 0)
	for _, newData := range newDataList {
		insertFlag := false
		for _, oldData := range oldDataList {
			if oldData.VarID == newData {
				insertFlag = true
				updateData = append(updateData, newData)
				break
			}
		}
		if !insertFlag {
			insertData = append(insertData, newData)
		}
	}
	for _, oldData := range oldDataList {
		deleteFlag := false
		for _, newData := range newDataList {
			if oldData.VarID == newData {
				deleteFlag = true
				break
			}
		}
		if !deleteFlag {
			deleteData = append(deleteData, oldData.ID)
		}
	}
	return insertData, deleteData, updateData
}

// UpdateWorkflowCustomModels 更新自定义变量与工作流的关系
func UpdateWorkflowCustomModels(ctx context.Context, tx *gorm.DB,
	robotId, workflowID string, newCustomModels []entity.WorkflowNodeCustomModel) error {
	if len(strings.TrimSpace(workflowID)) > 0 && len(strings.TrimSpace(robotId)) > 0 {
		// 查询历史数据
		var oldDataList []entity.WorkflowCustomModel
		if err := tx.Table(entity.WorkflowCustomModel{}.TableName()).
			Where("f_is_deleted = 0 AND f_workflow_id = ? AND f_robot_id = ? ", workflowID, robotId).
			Scan(&oldDataList).Error; err != nil {
			log.ErrorContextf(ctx, "UpdateWorkflowCustomModels get old err:%v", err)
			return err
		}
		// 对比新旧数据，筛选新增和删除的数据
		log.DebugContextf(ctx, "compareWorkflowCustomModelRef|newDataList:%+v|oldDataList:%+v", newCustomModels, oldDataList)
		insertData, deleteData, updateData := compareWorkflowCustomModelRef(newCustomModels, oldDataList)
		log.InfoContextf(ctx, "compareWorkflowCustomModelRef compareWorkflowVarParamsRefRef "+
			"insertData:%+v|deleteData:%+v|updateData:%+v", insertData, deleteData, updateData)
		// 删除历史引用关系
		if len(deleteData) > 0 {
			if err := tx.Table(entity.WorkflowCustomModel{}.TableName()).
				Where("f_id in ? ", deleteData).
				Where("f_is_deleted = 0").Updates(map[string]interface{}{
				"f_is_deleted": 1, "f_staff_id": util.StaffID(ctx),
				"f_action": entity.ActionDelete, "f_release_status": entity.ReleaseStatusUnPublished,
				"f_update_time": time.Now(),
			}).Error; err != nil {
				log.ErrorContextf(ctx, "updateWorkflowVarParamsRef delete err:%+v", err)
				return err
			}
		}
		//// 更新引用关系（关联关系可以不更新）
		//if len(updateData) > 0 {
		//	var mns = make([]string, len(updateData))
		//	for _, v := range updateData {
		//		,mns = append(mns, v.ModeName)
		//	}
		//	if err := tx.Table(entity.WorkflowCustomModel{}.TableName()).
		//		Where("f_workflow_id = ? ", workflowID).Where("f_model_name IN (?)", updateData).
		//		Where("f_is_deleted = 0").Updates(map[string]interface{}{
		//		"f_action":         entity.ActionUpdate,
		//		"f_release_status": entity.ReleaseStatusUnPublished,
		//		"f_update_time":    time.Now(),
		//	}).Error; err != nil {
		//		log.ErrorContextf(ctx, "updateWorkflowVarParamsRef update :%+v|err:%+v", updateData, err)
		//		return err
		//	}
		//}

		// 创建新引用关系
		if len(insertData) > 0 {
			insertCustomModel := make([]entity.WorkflowCustomModel, 0, len(insertData))
			for _, v := range insertData {
				// 创建新的记录
				insertCustomModel = append(insertCustomModel, entity.WorkflowCustomModel{
					WorkflowID:    workflowID,
					NodeID:        v.NodeID,
					NodeType:      v.NodeType,
					ModelName:     v.ModelName,
					AppBizID:      robotId,
					ReleaseStatus: entity.ReleaseStatusUnPublished,
					StaffID:       util.StaffID(ctx),
					CorpID:        util.CorpID(ctx),
					IsDeleted:     0,
					Action:        entity.ActionInsert,
					CreateTime:    time.Now(),
					UpdateTime:    time.Now(),
				})
			}
			if err := tx.Table(entity.WorkflowCustomModel{}.TableName()).Create(&insertCustomModel).Error; err != nil {
				log.ErrorContextf(ctx, "UpdateWorkflowCustomModels insert err:%+v", err)
				return err
			}
		}

	}
	return nil
}

// compareWorkflowCustomModelRef 对比工作流引用自定义模型新老数据
func compareWorkflowCustomModelRef(newDataList []entity.WorkflowNodeCustomModel,
	oldDataList []entity.WorkflowCustomModel) ([]entity.WorkflowNodeCustomModel,
	[]uint64, []entity.WorkflowNodeCustomModel) {
	insertData := make([]entity.WorkflowNodeCustomModel, 0)
	deleteData := make([]uint64, 0)
	updateData := make([]entity.WorkflowNodeCustomModel, 0)
	for _, newData := range newDataList {
		insertFlag := false
		for _, oldData := range oldDataList {
			if oldData.ModelName == newData.ModelName && oldData.NodeID == newData.NodeID {
				insertFlag = true
				updateData = append(updateData, newData)
				break
			}
		}
		if !insertFlag {
			insertData = append(insertData, newData)
		}
	}
	for _, oldData := range oldDataList {
		deleteFlag := false
		for _, newData := range newDataList {
			if oldData.ModelName == newData.ModelName && oldData.NodeID == newData.NodeID {
				deleteFlag = true
				break
			}
		}
		if !deleteFlag {
			deleteData = append(deleteData, oldData.ID)
		}
	}

	return insertData, deleteData, updateData
}

// UpdateWorkflowRef 更新工作流与工作流引用的关系
func UpdateWorkflowRef(ctx context.Context, tx *gorm.DB,
	robotId, workflowID string, newDataList []entity.WorkflowReferenceParams) error {
	log.InfoContextf(ctx, "updateWorkflowRef workflowID:%s|workflowIDRefs:%+v",
		workflowID, newDataList)
	if len(workflowID) > 0 {
		// 查询历史数据
		var oldDataList []entity.WorkflowReference
		if err := tx.Table(entity.WorkflowReference{}.TableName()).
			Where("f_is_deleted = 0 AND f_workflow_id = ? AND f_robot_id = ? ", workflowID, robotId).
			Scan(&oldDataList).Error; err != nil {
			log.ErrorContextf(ctx, "UpdateWorkflowRef get old err:%v", err)
			return err
		}
		// 对比新旧数据，筛选新增和删除的数据
		insertData, deleteData := compareWorkflowRef(newDataList, oldDataList)
		log.InfoContextf(ctx, "UpdateWorkflowRef compareWorkflowRef insertData:%+v|deleteData:%+v",
			insertData, deleteData)
		// 1. 删除历史引用关系
		if len(deleteData) > 0 {
			if err := tx.Table(entity.WorkflowReference{}.TableName()).
				Where("f_id in ?", deleteData).Updates(map[string]interface{}{
				"f_is_deleted": 1, "f_update_time": time.Now(),
			}).Error; err != nil {
				log.ErrorContextf(ctx, "updateWorkflowRef delete workflowIDRefs err:%+v", err)
				return err
			}
		}
		// 2. 创建新引用关系
		if len(insertData) > 0 {
			insertWorkflowRefs := make([]entity.WorkflowReference, 0)
			for _, workflowRef := range insertData {
				// 创建新的记录
				insertWorkflowRefs = append(insertWorkflowRefs, entity.WorkflowReference{
					WorkflowID:    workflowID,
					NodeID:        workflowRef.NodeID,
					WorkflowRefID: workflowRef.WorkflowRefID,
					RobotID:       robotId,
					CreateTime:    time.Now(), UpdateTime: time.Now(),
				})
			}
			if err := tx.Table(entity.WorkflowReference{}.TableName()).Create(&insertWorkflowRefs).Error; err != nil {
				log.ErrorContextf(ctx, "updateWorkflowRef insert err:%+v", err)
				return err
			}
		}
	}
	return nil
}

// compareWorkflowRef 对比工作流引用工作流新老数据
func compareWorkflowRef(newDataList []entity.WorkflowReferenceParams,
	oldDataList []entity.WorkflowReference) ([]entity.WorkflowReferenceParams, []uint64) {
	insertData := make([]entity.WorkflowReferenceParams, 0)
	deleteData := make([]uint64, 0)
	for _, newData := range newDataList {
		insertFlag := false
		for _, oldData := range oldDataList {
			if oldData.NodeID == newData.NodeID && oldData.WorkflowRefID == newData.WorkflowRefID {
				insertFlag = true
				break
			}
		}
		if !insertFlag {
			insertData = append(insertData, newData)
		}
	}
	for _, oldData := range oldDataList {
		deleteFlag := false
		for _, newData := range newDataList {
			if oldData.NodeID == newData.NodeID && oldData.WorkflowRefID == newData.WorkflowRefID {
				deleteFlag = true
				break
			}
		}
		if !deleteFlag {
			deleteData = append(deleteData, oldData.ID)
		}
	}
	return insertData, deleteData
}

// UpdateWorkflowRefPlugin 更新工作流与插件工具的引用关系,保存工作流与插件的引用关系
func UpdateWorkflowRefPlugin(ctx context.Context, tx *gorm.DB,
	robotId, workflowID string, newDataList []entity.WorkflowRefPluginParams) error {
	log.InfoContextf(ctx, "UpdateWorkflowRefPlugin workflowID:%s|newDataList:%+v",
		workflowID, newDataList)
	if len(workflowID) > 0 {
		// 查询历史数据
		var oldDataList []entity.WorkflowRefPlugin
		if err := tx.Table(entity.WorkflowRefPlugin{}.TableName()).
			Where("f_is_deleted = 0 AND f_workflow_id = ? AND f_robot_id = ? ", workflowID, robotId).
			Scan(&oldDataList).Error; err != nil {
			log.ErrorContextf(ctx, "UpdateWorkflowRefPlugin get old err:%v", err)
			return err
		}
		// 对比新旧数据，筛选新增和删除的数据
		insertData, deleteData := compareWorkflowRefPlugin(newDataList, oldDataList)
		log.InfoContextf(ctx, "UpdateWorkflowRefPlugin compareWorkflowRefPlugin insertData:%+v|deleteData:%+v",
			insertData, deleteData)
		// 1. 删除历史引用关系
		if len(deleteData) > 0 {
			if err := tx.Table(entity.WorkflowRefPlugin{}.TableName()).
				Where("f_id in ?", deleteData).Updates(map[string]interface{}{
				"f_is_deleted": 1, "f_update_time": time.Now(),
			}).Error; err != nil {
				log.ErrorContextf(ctx, "UpdateWorkflowRefPlugin delete workflowRefPlugins err:%+v", err)
				return err
			}
		}
		// 2. 创建新引用关系
		if len(insertData) > 0 {
			insertWorkflowPlugins := make([]entity.WorkflowRefPlugin, 0)
			for _, workFlowRefPlugin := range insertData {
				// 创建新的记录
				insertWorkflowPlugins = append(insertWorkflowPlugins, entity.WorkflowRefPlugin{
					WorkflowID: workflowID,
					NodeID:     workFlowRefPlugin.NodeID,
					PluginType: workFlowRefPlugin.PluginType,
					PluginID:   workFlowRefPlugin.PluginID,
					ToolID:     workFlowRefPlugin.ToolID,
					RobotID:    robotId,
					CreateTime: time.Now(), UpdateTime: time.Now(),
				})
			}
			if err := tx.Table(entity.WorkflowRefPlugin{}.TableName()).Create(&insertWorkflowPlugins).Error; err != nil {
				log.ErrorContextf(ctx, "UpdateWorkflowRefPlugin insert err:%+v", err)
				return err
			}
		}
	}
	return nil
}

// compareWorkflowRefPlugin 对比工作流引用插件新老数据
func compareWorkflowRefPlugin(newDataList []entity.WorkflowRefPluginParams,
	oldDataList []entity.WorkflowRefPlugin) ([]entity.WorkflowRefPluginParams, []uint64) {
	insertData := make([]entity.WorkflowRefPluginParams, 0)
	deleteData := make([]uint64, 0)
	for _, newData := range newDataList {
		insertFlag := false
		for _, oldData := range oldDataList {
			if oldData.NodeID == newData.NodeID && oldData.PluginType == newData.PluginType &&
				oldData.PluginID == newData.PluginID && oldData.ToolID == newData.ToolID {
				insertFlag = true
				break
			}
		}
		if !insertFlag {
			insertData = append(insertData, newData)
		}
	}
	for _, oldData := range oldDataList {
		deleteFlag := false
		for _, newData := range newDataList {
			if oldData.NodeID == newData.NodeID && oldData.PluginType == newData.PluginType &&
				oldData.PluginID == newData.PluginID && oldData.ToolID == newData.ToolID {
				deleteFlag = true
				break
			}
		}
		if !deleteFlag {
			deleteData = append(deleteData, oldData.ID)
		}
	}
	return insertData, deleteData
}

// UpdateWorkflowRefKnowledge 更新工作流与知识型的引用关系
func UpdateWorkflowRefKnowledge(ctx context.Context, tx *gorm.DB,
	robotId, workflowID string, newDataList []entity.WorkflowRefKnowledgeParams) error {
	log.InfoContextf(ctx, "UpdateWorkflowRefKnowledge workflowID:%s|newDataList:%+v",
		workflowID, newDataList)
	if len(workflowID) > 0 {
		// 查询历史数据
		var oldDataList []entity.WorkflowRefKnowledge
		if err := tx.Table(entity.WorkflowRefKnowledge{}.TableName()).
			Where("f_is_deleted = 0 AND f_workflow_id = ? AND f_robot_id = ? ", workflowID, robotId).
			Scan(&oldDataList).Error; err != nil {
			log.ErrorContextf(ctx, "UpdateWorkflowRefKnowledge get old err:%v", err)
			return err
		}
		// 对比新旧数据，筛选新增和删除的数据
		insertData, deleteData := compareKnowledgeRef(newDataList, oldDataList)
		log.InfoContextf(ctx, "UpdateWorkflowRefKnowledge compareKnowledgeRef insertData:%+v|deleteData:%+v",
			insertData, deleteData)
		// 1. 删除历史引用关系
		if len(deleteData) > 0 {
			if err := tx.Table(entity.WorkflowRefKnowledge{}.TableName()).
				Where("f_id in ?", deleteData).Updates(map[string]interface{}{
				"f_is_deleted": 1, "f_update_time": time.Now(),
			}).Error; err != nil {
				log.ErrorContextf(ctx, "UpdateWorkflowRefKnowledge delete workflowRefKnowledge err:%+v", err)
				return err
			}
		}
		// 2. 创建新引用关系
		if len(insertData) > 0 {
			insertWorkflowRefKnowledgeList := make([]entity.WorkflowRefKnowledge, 0)
			for _, workflowRefKnowledge := range insertData {
				// 创建新的记录
				insertWorkflowRefKnowledgeList = append(insertWorkflowRefKnowledgeList, entity.WorkflowRefKnowledge{
					WorkflowID:     workflowID,
					NodeID:         workflowRefKnowledge.NodeID,
					KnowledgeType:  workflowRefKnowledge.KnowledgeType,
					KnowledgeBizId: workflowRefKnowledge.KnowledgeBizId,
					BizId:          workflowRefKnowledge.BizId,
					LabelId:        workflowRefKnowledge.LabelId,
					InputParamName: workflowRefKnowledge.InputParamName,
					Type:           workflowRefKnowledge.Type,
					RobotId:        robotId,
					CreateTime:     time.Now(), UpdateTime: time.Now(),
				})
			}
			if err := tx.Table(entity.WorkflowRefKnowledge{}.TableName()).
				Create(&insertWorkflowRefKnowledgeList).Error; err != nil {
				log.ErrorContextf(ctx, "UpdateWorkflowRefKnowledge insert err:%+v", err)
				return err
			}
		}
	}
	return nil
}

// compareKnowledgeRef 对比知识型引用新老数据
// v2.9.0 从check处parse大模型问答节点和知识检索节点传进来的数据，新增f_knowledge_type和f_knowledge_biz_id的处理
// 最外层选择全部知识时，f_knowledge_type为ALL_KNOWLEDGE，f_knowledge_biz_id为"",f_type为"ALL"
func compareKnowledgeRef(newDataList []entity.WorkflowRefKnowledgeParams,
	oldDataList []entity.WorkflowRefKnowledge) ([]entity.WorkflowRefKnowledgeParams, []uint64) {
	insertData := make([]entity.WorkflowRefKnowledgeParams, 0)
	deleteData := make([]uint64, 0)
	for _, newData := range newDataList {
		insertFlag := false
		for _, oldData := range oldDataList {
			if oldData.NodeID == newData.NodeID && oldData.KnowledgeType == newData.KnowledgeType &&
				oldData.KnowledgeBizId == newData.KnowledgeBizId && oldData.BizId == newData.BizId &&
				oldData.Type == newData.Type && oldData.LabelId == newData.LabelId &&
				oldData.InputParamName == newData.InputParamName {
				insertFlag = true
				break
			}
		}
		if !insertFlag {
			insertData = append(insertData, newData)
		}
	}
	for _, oldData := range oldDataList {
		deleteFlag := false
		for _, newData := range newDataList {
			if oldData.NodeID == newData.NodeID && oldData.KnowledgeType == newData.KnowledgeType &&
				oldData.KnowledgeBizId == newData.KnowledgeBizId && oldData.BizId == newData.BizId &&
				oldData.Type == newData.Type && oldData.LabelId == newData.LabelId &&
				oldData.InputParamName == newData.InputParamName {
				deleteFlag = true
				break
			}
		}
		if !deleteFlag {
			deleteData = append(deleteData, oldData.ID)
		}
	}
	return insertData, deleteData
}

// wfTransformState 工作流流转状态
type wfTransformState int

const (
	draftState           wfTransformState = 1 // 草稿
	enableState          wfTransformState = 2 // 待发布
	publishingState      wfTransformState = 3 // 发布中
	publishedState       wfTransformState = 4 // 已发布
	publishFailedState   wfTransformState = 5 // 发布失败
	publishedDraftState  wfTransformState = 6 // 已发布-草稿
	publishedChangeState wfTransformState = 7 // 已发布-更新
)

// 状态转换规则
type transformRule struct {
	nextState  string
	nextAction string
}

// transformRules 状态流转规则
// https://iwiki.woa.com/p/4012998976#%E6%96%B9%E6%A1%88%E4%BA%94%EF%BC%9A%E5%A2%9E%E5%8A%A0%E5%86%85%E9%83%A8%E9%9A%90%E8%97%8F%E7%9A%84%E7%8A%B6%E6%80%81
var transformRules = map[wfTransformState]map[KEP_WF.SaveWorkflowReq_SaveTypeEnum]transformRule{
	draftState: {
		KEP_WF.SaveWorkflowReq_DRAFT:  {nextState: entity.WorkflowStateDraft, nextAction: entity.ActionInsert},
		KEP_WF.SaveWorkflowReq_ENABLE: {nextState: entity.WorkflowStateEnable, nextAction: entity.ActionInsert},
	},
	enableState: {
		KEP_WF.SaveWorkflowReq_DRAFT:  {nextState: entity.WorkflowStateDraft, nextAction: entity.ActionInsert},
		KEP_WF.SaveWorkflowReq_ENABLE: {nextState: entity.WorkflowStateEnable, nextAction: entity.ActionInsert},
	},
	publishedState: {
		KEP_WF.SaveWorkflowReq_DRAFT:  {nextState: entity.WorkflowStatePublishedDraft, nextAction: entity.ActionUpdate},
		KEP_WF.SaveWorkflowReq_ENABLE: {nextState: entity.WorkflowStatePublishedChange, nextAction: entity.ActionUpdate},
	},
	publishFailedState: { // 默认为从来没有发布成功时的流转 如果已经发布成功过，需要特殊判断和流转
		KEP_WF.SaveWorkflowReq_DRAFT:  {nextState: entity.WorkflowStateDraft, nextAction: entity.ActionInsert},
		KEP_WF.SaveWorkflowReq_ENABLE: {nextState: entity.WorkflowStateEnable, nextAction: entity.ActionInsert},
	},
	publishedDraftState: {
		KEP_WF.SaveWorkflowReq_DRAFT:  {nextState: entity.WorkflowStatePublishedDraft, nextAction: entity.ActionUpdate},
		KEP_WF.SaveWorkflowReq_ENABLE: {nextState: entity.WorkflowStatePublishedChange, nextAction: entity.ActionUpdate},
	},
	publishedChangeState: {
		KEP_WF.SaveWorkflowReq_DRAFT:  {nextState: entity.WorkflowStatePublishedDraft, nextAction: entity.ActionUpdate},
		KEP_WF.SaveWorkflowReq_ENABLE: {nextState: entity.WorkflowStatePublishedChange, nextAction: entity.ActionUpdate},
	},
}

// getWorkflowTransformState 获取工作流状态
func getWorkflowTransformState(wf entity.Workflow) (wfTransformState, error) {
	// 发布状态
	switch wf.ReleaseStatus {
	case entity.WorkflowReleaseStatusUnPublished:
		// 发布状态待发布
		// 根据WorkflowState确定状态
		switch wf.WorkflowState {
		case entity.WorkflowStateDraft:
			return draftState, nil
		case entity.WorkflowStateEnable:
			return enableState, nil
		case entity.WorkflowStatePublishedDraft:
			return publishedDraftState, nil
		case entity.WorkflowStatePublishedChange:
			return publishedChangeState, nil
		default:
			return 0, fmt.Errorf("workflow WorkflowState:%s illegal", wf.WorkflowState)
		}
	case entity.WorkflowReleaseStatusPublishing:
		// 发布状态发布中
		return publishingState, nil
	case entity.WorkflowReleaseStatusPublished:
		// 发布状态已发布
		return publishedState, nil
	case entity.WorkflowReleaseStatusFail:
		// 发布状态发布失败
		return publishFailedState, nil
	default:
		return 0, fmt.Errorf("workflow ReleaseStatus:%s illegal", wf.ReleaseStatus)
	}
}

// isWorkflowPublished 工作流是否发布过
func isWorkflowPublished(wf entity.Workflow) bool {
	if wf.WorkflowState == entity.WorkflowStateDraft || wf.WorkflowState == entity.WorkflowStateEnable {
		return false
	}
	return true
}

// TransformWorkflowState 工作流任务流转
func TransformWorkflowState(ctx context.Context, wf entity.Workflow, isDebug uint32) (nextState, nextAction string, err error) {
	log.InfoContextf(ctx, "TransformWorkflowState wf:%+v, isDebug:%d", wf, isDebug)
	if isDebug != uint32(KEP_WF.SaveWorkflowReq_DRAFT) && isDebug != uint32(KEP_WF.SaveWorkflowReq_ENABLE) {
		err = fmt.Errorf("workflow isDebug:%d illegal", isDebug)
		log.ErrorContextf(ctx, "TransformWorkflowState failed, err:%+v", err)
		return "", "", err
	}
	state, err := getWorkflowTransformState(wf)
	if err != nil {
		log.ErrorContextf(ctx, "TransformWorkflowState getWorkflowTransformState err:%+v", err)
		return "", "", err
	}
	log.InfoContextf(ctx, "TransformWorkflowState state:%+v", state)

	rules, ok := transformRules[state]
	if !ok {
		err = fmt.Errorf("workflow transformState:%d not allow trans", state)
		log.ErrorContextf(ctx, "TransformWorkflowState failed, err:%+v", err)
		return "", "", err
	}

	rule, ok := rules[KEP_WF.SaveWorkflowReq_SaveTypeEnum(isDebug)]
	if !ok {
		err = fmt.Errorf("workflow isDebug:%d illegal", isDebug)
		log.ErrorContextf(ctx, "TransformWorkflowState failed, err:%+v", err)
		return "", "", err
	}
	log.InfoContextf(ctx, "TransformWorkflowState rule:%+v", rule)

	// 针对发布失败的特殊处理
	if state == publishFailedState && isWorkflowPublished(wf) {
		switch KEP_WF.SaveWorkflowReq_SaveTypeEnum(isDebug) {
		case KEP_WF.SaveWorkflowReq_DRAFT:
			rule.nextState = entity.WorkflowStatePublishedDraft
		case KEP_WF.SaveWorkflowReq_ENABLE:
			rule.nextState = entity.WorkflowStatePublishedChange
		}

		rule.nextAction = entity.ActionUpdate
		log.InfoContextf(ctx, "TransformWorkflowState rule:%+v", rule)
	}
	return rule.nextState, rule.nextAction, nil
}

// GetWorkflowDetail 获取工作流详情
// SELECT w.*
// FROM t_workflow w
// JOIN t_robot_workflow r ON w.f_workflow_id = r.f_workflow_id
// WHERE r.f_robot_id = ? AND r.f_workflow_id = ? AND r.f_is_deleted = 0;
func GetWorkflowDetail(ctx context.Context, workflowID, botBizId string) (*entity.Workflow, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflow entity.Workflow
	// 查询符合条件的记录
	err := db.Table("t_workflow w").
		Joins("JOIN t_robot_workflow r ON w.f_workflow_id = r.f_workflow_id").
		Where("r.f_robot_id = ? AND r.f_workflow_id = ? AND r.f_is_deleted = 0", botBizId, workflowID).
		Take(&workflow).Error // 使用 Take 只获取一个记录
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowDetail|botId:%s|:workflowID:%s|err:%+v",
			botBizId, workflowID, err)
		return nil, err
	}
	return &workflow, nil
}

// GetWorkflowDetailTx ...
func GetWorkflowDetailTx(ctx context.Context, tx *gorm.DB,
	workflowID, botBizId string) (*entity.Workflow, error) {
	var workflow entity.Workflow
	// 查询符合条件的记录
	err := tx.Table("t_workflow w").
		Joins("JOIN t_robot_workflow r ON w.f_workflow_id = r.f_workflow_id").
		Where("r.f_robot_id = ? AND r.f_workflow_id = ? AND r.f_is_deleted = 0", botBizId, workflowID).
		Take(&workflow).Error // 使用 Take 只获取一个记录
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowDetail|botId:%s|:workflowID:%s|err:%+v",
			botBizId, workflowID, err)
		return nil, err
	}
	return &workflow, nil
}

// GetProdWorkflowDetail 发布库中工作流的具体信息
func GetProdWorkflowDetail(ctx context.Context, workflowID, botBizId string) (*entity.Workflow, error) {
	db := database.GetLLMRobotWorkflowProdGORM().WithContext(ctx).Debug()
	var workflow entity.Workflow
	// 查询符合条件的记录
	err := db.Table("t_workflow w").
		Joins("JOIN t_robot_workflow r ON w.f_workflow_id = r.f_workflow_id").
		Where("r.f_robot_id = ? AND r.f_workflow_id = ? AND r.f_is_deleted = 0", botBizId, workflowID).
		Take(&workflow).Error // 使用 Take 只获取一个记录
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil // 没有找到记录
	}
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowDetail|botId:%s|:workflowID:%s|err:%+v", botBizId, workflowID, err)
		return nil, utilsErrors.ErrTaskFlowNotFound
	}
	return &workflow, nil
}

// GetWorkflowDetails 批量获取Workflow详情
func GetWorkflowDetails(ctx context.Context, flowIds []string,
	appBizId string) (map[string]*entity.Workflow, error) {
	if len(flowIds) == 0 {
		return nil, nil
	}
	list := make([]*entity.Workflow, 0)
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	err := db.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted = ?", 0).
		Where("f_robot_id = ?", appBizId).
		Where("f_workflow_id IN (?)", flowIds).Find(&list).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowDetails err：%v", err)
		return nil, err
	}
	workflowMap := make(map[string]*entity.Workflow, 0)
	for _, item := range list {
		workflowMap[item.WorkflowID] = item
	}
	return workflowMap, nil
}

// getPublishStatusState ...
func getPublishStatusState(releaseStatuses []string) []string {
	releaseStatus := make([]string, 0)

	if len(releaseStatuses) > 0 {
		for _, val := range releaseStatuses {
			// 已发布
			if val == entity.WorkflowFrontStatusPublished {
				releaseStatus = append(releaseStatus, entity.WorkflowReleaseStatusPublished)
			}
			// 发布中
			if val == entity.WorkflowFrontStatusPublishing {
				releaseStatus = append(releaseStatus, entity.WorkflowReleaseStatusPublishing)
			}
			// 发布失败
			if val == entity.WorkflowFrontStatusPublishedFail {
				releaseStatus = append(releaseStatus, entity.WorkflowReleaseStatusFail)
			}
		}
	}
	return releaseStatus
}

// getPublishStatusAndFlowState ...
func getPublishStatusAndFlowState(releaseStatuses []string) ([]string, []string) {
	flowState := make([]string, 0)
	releaseStatus := make([]string, 0)

	// 草稿态
	if entity.WorkflowIsExitFrontStatusDraft(releaseStatuses) {
		releaseStatus = append(releaseStatus, entity.WorkflowReleaseStatusUnPublished)
		flowState = append(flowState, entity.WorkflowStateDraft, entity.WorkflowStatePublishedDraft)
	}
	// 待发布 & 待更新发布 合并为 待发布  v2.6 11.4
	if entity.WorkflowIsExitFrontStatusUnPublished(releaseStatuses) {
		releaseStatus = append(releaseStatus, entity.WorkflowReleaseStatusUnPublished)
		flowState = append(flowState, entity.WorkflowStateEnable, entity.WorkflowStatePublishedChange)
	}
	return types.Unique(releaseStatus), types.Unique(flowState)
}

// ListWorkflow 获取工作流列表
func ListWorkflow(ctx context.Context, params entity.ListWorkflowParams) ([]*entity.Workflow, int64, error) {
	workflows := make([]*entity.Workflow, 0)
	var total int64
	var workflowIds, releaseStatus []string
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	sid := util.RequestID(ctx)
	pageSize, page := uint32(15), uint32(1)

	// 获取机器人下单工作流Ids
	if err := db.Table(entity.RobotWorkflow{}.TableName()).Select("f_workflow_id").
		Where("f_is_deleted=0 AND f_robot_id=?", params.BotBizId).Scan(&workflowIds).Error; err != nil {
		log.ErrorContextf(ctx, "ListWorkflow|GetRobotId|%s|err:%+v", sid, err)
		return nil, 0, err
	}
	res := db.Table(entity.Workflow{}.TableName())

	if len(params.WorkflowIds) > 0 {
		res = res.Where("f_is_deleted=0 AND f_workflow_id IN ?", params.WorkflowIds)
	} else {
		res = res.Where("f_is_deleted=0 AND f_workflow_id IN ?", workflowIds)
	}
	res = res.Order("f_update_time DESC").Order("f_id DESC")

	releaseStatus = getPublishStatusState(params.ReleaseStatus)
	if len(params.Query) > 0 {
		res = res.Where("f_workflow_name Like ? escape '*'", "%*"+params.Query+"%")
	}

	if len(releaseStatus) > 0 {
		res = res.Where("f_release_status IN ?", releaseStatus)
		// 草稿
		if entity.WorkflowIsExitFrontStatusDraft(params.ReleaseStatus) {
			flowState := []string{entity.WorkflowStateDraft, entity.WorkflowStatePublishedDraft}
			if len(params.Query) > 0 {
				res = res.Or("f_is_deleted=0 AND f_flow_state IN ? AND f_robot_id = ? AND f_workflow_name"+
					" Like ? escape '*'", flowState, params.BotBizId, "%*"+params.Query+"%")
			} else {
				res = res.Or("f_is_deleted=0 AND f_flow_state IN ? AND f_robot_id = ?", flowState, params.BotBizId)
			}
		}
		// v2.6 11.4日 待发布 & 待更新发布 合并为 "待发布"
		if entity.WorkflowIsExitFrontStatusUnPublished(params.ReleaseStatus) {
			flowState := []string{entity.WorkflowStateEnable, entity.WorkflowStatePublishedChange}
			if len(params.Query) > 0 {
				res = res.Or("f_is_deleted=0 AND f_release_status=? AND f_flow_state IN ? AND f_robot_id=? AND f_workflow_name"+
					" Like ? escape '*'", entity.WorkflowReleaseStatusUnPublished, flowState, params.BotBizId, "%*"+params.Query+"%")
			} else {
				res = res.Or("f_is_deleted=0 AND f_release_status=? AND f_flow_state IN ? AND f_robot_id=?",
					entity.WorkflowReleaseStatusUnPublished, flowState, params.BotBizId)
			}
		}
	} else {
		var flowState []string
		releaseStatus, flowState = getPublishStatusAndFlowState(params.ReleaseStatus)
		if len(releaseStatus) > 0 {
			res = res.Where("f_release_status IN ?", releaseStatus)
		}
		if len(flowState) > 0 {
			res = res.Where("f_flow_state IN ?", flowState)
		}
	}
	if len(params.Actions) > 0 {
		res = res.Where("f_action IN ?", params.Actions)
	}
	res = res.Count(&total)
	if params.PageSize > 0 {
		pageSize = params.PageSize
	}
	if params.Page > 0 {
		page = params.Page
	}
	offset := (page - 1) * pageSize
	res = res.Offset(int(offset)).Limit(int(pageSize)).Scan(&workflows)
	if res.Error != nil {
		log.ErrorContextf(ctx, "ListWorkflow|%s|err:%+v", sid, res.Error)
		return nil, 0, res.Error
	}
	return workflows, total, nil
}

// GetWorkflowsByFlowIdsWithNoAppId ...
func GetWorkflowsByFlowIdsWithNoAppId(ctx context.Context, flowIds []string) ([]*entity.Workflow, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	workflows := make([]*entity.Workflow, 0)

	if err := db.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted=0 AND f_workflow_id IN ?", flowIds).
		Scan(&workflows).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowsByFlowIds|err:%+v", err)
		return workflows, errors.OpDataFromDBError("获取工作流失败")
	}
	return workflows, nil
}

// GetWorkflowsByFlowIds ...
func GetWorkflowsByFlowIds(ctx context.Context, flowIds []string, robotId string) ([]*entity.Workflow, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	workflows := make([]*entity.Workflow, 0)

	if err := db.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id IN ?", robotId, flowIds).
		Scan(&workflows).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowsByFlowIds|err:%+v", err)
		return workflows, errors.OpDataFromDBError("获取工作流失败")
	}
	return workflows, nil
}

// GetWorkflowsByState 通过状态获取工作流(后面如果要判断草稿态不能停启用这里用得到暂留)
func GetWorkflowsByState(ctx context.Context, robotId string, flowIds, flowState,
	releaseStatus []string) ([]*entity.Workflow, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	workflows := make([]*entity.Workflow, 0)

	res := db.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id IN ?", robotId, flowIds)
	if len(flowState) > 0 {
		res = res.Where("f_flow_state IN ?", flowState)
	}
	if len(releaseStatus) > 0 {
		res = res.Where("f_release_status IN ?", releaseStatus)
	}
	if err := res.Scan(&workflows).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowsByState|%+v", err)
		return nil, errors.OpDataFromDBError("获取工作流失败")
	}
	return workflows, nil
}

// GetWorkflowsNotSwitchProd 获取不能发布到生产环境的工作流
//
//	(后面如果要判断草稿态不能停启用这里用得到暂留)
func GetWorkflowsNotSwitchProd(ctx context.Context, robotId string,
	flowIds []string) ([]*entity.Workflow, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	workflows := make([]*entity.Workflow, 0)

	// 一次都没发布的 or 发布中 发布失败的不能切换
	res := db.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id IN ?", robotId, flowIds).
		Where("f_release_status IN ? AND f_flow_state IN ?", []string{"UNPUBLISHED"},
			[]string{"DRAFT", "ENABLE", "CHANGE_UNPUBLISHED"}).
		Or("f_is_deleted=0 AND f_robot_id=? AND f_workflow_id IN ? AND f_release_status IN ?",
			robotId, flowIds, []string{"PUBLISHING", "FAIL"})

	if err := res.Scan(&workflows).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowsNotSwitchProd|err:%+v", err)
		return nil, errors.OpDataFromDBError("获取工作流失败")
	}
	return workflows, nil
}

// UpdateWorkflowParameterRef 更新参数与工作流的关系
func UpdateWorkflowParameterRef(ctx context.Context, tx *gorm.DB,
	appBizID, workflowID string, newNodeParams []entity.WorkflowNodeParam, needNotifyDM bool) error {
	if len(newNodeParams) == 0 {
		return handleEmptyParameterNode(ctx, tx, appBizID, workflowID, needNotifyDM)
	}

	//1. save操作时 更新工作流参数节点的关系 【redo undo】
	inUseParamIDs, needDelParamIDs, err := txUpdateWorkflowParameterRef(ctx, tx, workflowID, newNodeParams)
	if err != nil {
		return err
	}

	// 2. save操作时 更新参数的数据, 并通知dm 【redo,undo】
	if needNotifyDM {
		err = txUpdateParameterInfo(ctx, appBizID, tx, inUseParamIDs, needDelParamIDs)
		if err != nil {
			return err
		}
	}

	return nil

}

// handleEmptyParameterNode 处理空参数提取节点信息
func handleEmptyParameterNode(ctx context.Context, tx *gorm.DB, appBizID, workflowID string, needNotifyDM bool) error {
	err := tx.Model(entity.WorkflowParameter{}).
		Where(" f_workflow_id = ? ", workflowID).
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "Save Workflow UpdateWorkflowParameter Failed! err:%+v", err)
		return err
	}

	var paramIds []string
	err = tx.Model(entity.WorkflowParameter{}).
		Where("f_workflow_id = ? ", workflowID).Pluck("f_parameter_id", &paramIds).Error
	if err != nil {
		return err
	}

	if len(paramIds) > 0 {
		err = tx.Model(entity.Parameter{}).
			Where(" f_parameter_id in ? ", paramIds).
			Updates(map[string]interface{}{
				"f_is_deleted":     1,
				"f_action":         entity.ActionDelete,
				"f_release_status": entity.ReleaseStatusUnPublished,
			}).Error
		if err != nil {
			log.ErrorContextf(ctx, "Save Workflow UpdateWorkflowParameter Failed! err:%+v", err)
			return err
		}
	}

	if needNotifyDM && len(paramIds) > 0 {
		deleteParametersReq := &KEP_WF_DM.DeleteParametersInSandboxRequest{
			AppID:        appBizID,
			ParameterIDs: paramIds,
		}
		_, err = rpc.DeleteParametersInSandbox(ctx, deleteParametersReq)
		if err != nil {
			return err
		}
	}

	return nil
}

// txUpdateWorkflowParameterRef save 操作时更新工作流参数节点的关系 【redo undo】
func txUpdateWorkflowParameterRef(ctx context.Context, tx *gorm.DB, workflowID string, newNodeParams []entity.WorkflowNodeParam) (inUseParamIds, needDelParamIds []string, err error) {
	log.InfoContextf(ctx, "txUpdateWorkflowParameterRef workflowID:%s, newNodeParams:%+v", workflowID, newNodeParams)

	var inUseNodeIds []string
	for _, v := range newNodeParams {
		inUseParamIds = append(inUseParamIds, v.ParameterIds...)
		inUseNodeIds = append(inUseNodeIds, v.NodeID)

		// 1. 更新基础关系的值,更新一次状态
		err = tx.WithContext(ctx).Debug().
			Exec("UPDATE t_workflow_parameter "+
				"SET f_action = CASE WHEN (f_release_status IN (?, ?) AND f_action = ?) THEN ? ELSE ? END,"+
				"f_node_name = ?,"+
				"f_is_deleted = ?,"+
				"f_release_status = ?"+
				" WHERE f_workflow_id = ? and f_node_id = ? and f_parameter_id in ?",
				entity.ReleaseStatusFail, entity.ReleaseStatusUnPublished, entity.ActionInsert, entity.ActionInsert,
				entity.ActionUpdate, v.NodeName, 0, entity.ReleaseStatusUnPublished, workflowID, v.NodeID, v.ParameterIds).
			Error
		if err != nil {
			log.ErrorContextf(ctx, "Save Workflow UpdateWorkflowParameter Failed! err:%+v", err)
			return nil, nil, err
		}
	}

	// 将inUseParamIds置为启用状态
	err = tx.WithContext(ctx).Debug().
		Exec("UPDATE t_parameter "+
			"SET f_action = CASE WHEN (f_release_status IN (?, ?) AND f_action = ?) THEN ? ELSE ? END,"+
			"f_is_deleted = ?,"+
			"f_release_status = ?"+
			" WHERE f_is_deleted = 1 and f_parameter_id in ?",
			entity.ReleaseStatusFail, entity.ReleaseStatusUnPublished, entity.ActionInsert, entity.ActionInsert,
			entity.ActionUpdate, 0, entity.ReleaseStatusUnPublished, inUseParamIds).
		Error
	if err != nil {
		log.ErrorContextf(ctx, "Save Workflow UpdateWorkflowParameter Failed! err:%+v", err)
		return nil, nil, err
	}

	// 删除undo后没被使用的值, 不能带f_node_id，因为有些node_id直接在操作画布的时候就删除了，这个时候不会传到这里
	err = tx.Model(entity.WorkflowParameter{}).
		Where(" f_workflow_id = ?  and f_node_id not in ?", workflowID, inUseNodeIds).
		Updates(map[string]interface{}{
			"f_is_deleted":     1,
			"f_action":         entity.ActionDelete,
			"f_release_status": entity.ReleaseStatusUnPublished,
		}).Error
	if err != nil {
		log.ErrorContextf(ctx, "Save Workflow UpdateWorkflowParameter Failed! err:%+v", err)
		return nil, nil, err
	}

	// 找到需要删除的paramIDs
	err = tx.Model(entity.WorkflowParameter{}).
		Where(" f_workflow_id = ?  and f_parameter_id not in ?", workflowID, inUseParamIds).
		Pluck("f_parameter_id", &needDelParamIds).Error
	if err != nil {
		log.ErrorContextf(ctx, "Save Workflow Get NeedDelParamIds Failed! err:%+v", err)
		return nil, nil, err
	}
	// 将needDelParamIds 置为删除状态
	if len(needDelParamIds) > 0 {
		err = tx.Model(entity.Parameter{}).
			Where(" f_parameter_id in ? ", needDelParamIds).
			Updates(map[string]interface{}{
				"f_is_deleted":     1,
				"f_action":         entity.ActionDelete,
				"f_release_status": entity.ReleaseStatusUnPublished,
			}).Error
		if err != nil {
			log.ErrorContextf(ctx, "Save Workflow UpdateParameter Failed! err:%+v", err)
			return nil, nil, err
		}
	}

	log.InfoContextf(ctx, "SaveWorkflow inUseParamIds:%+v,needDelParamIds:%+v", inUseParamIds, needDelParamIds)
	return inUseParamIds, needDelParamIds, nil
}

func upsertParameterToDM(ctx context.Context, paramInfos []*entity.Parameter, appBizID string) error {
	if len(paramInfos) > 0 {
		upsertParameters := &KEP_WF_DM.UpsertParametersToSandboxRequest{
			AppID:      appBizID,
			Parameters: make([]*KEP_WF_DM.Parameter, 0),
		}
		upsertParameters.Parameters = make([]*KEP_WF_DM.Parameter, 0)
		for _, v := range paramInfos {
			tmpParam := &KEP_WF_DM.Parameter{
				ParameterID:       v.ParameterID,
				ParameterName:     v.ParameterName,
				ParameterDesc:     v.ParameterDesc,
				ValueType:         KEP_WF.TypeEnum(KEP_WF.TypeEnum_value[v.ParameterType]),
				CorrectExamples:   util.GetParameterExamplesArr(ctx, v.CorrectExamples),
				IncorrectExamples: util.GetParameterExamplesArr(ctx, v.IncorrectExamples),
			}
			// 如果开启固定话术回复则通知dm
			if v.CustomAskEnable {
				tmpParam.CustomAsk = v.CustomAsk
			}
			upsertParameters.Parameters = append(upsertParameters.Parameters, tmpParam)
		}
		_, err := rpc.UpsertParametersToSandbox(ctx, upsertParameters)
		if err != nil {
			return err
		}
	}
	return nil
}

// txUpdateParameterInfo save 更新工参数的信息并通知DM 【redo undo】
func txUpdateParameterInfo(ctx context.Context, appBizID string, tx *gorm.DB,
	inUseParamIDs, needDelParamIDS []string) (err error) {
	//2. 通知dm
	var paramInfos []*entity.Parameter
	err = tx.Model(entity.Parameter{}).
		Where("f_parameter_id in ? ", inUseParamIDs).
		Find(&paramInfos).Error
	if err != nil {
		log.ErrorContextf(ctx, "Save Workflow UpdateParameter Failed! err:%+v", err)
		return err
	}

	if err = upsertParameterToDM(ctx, paramInfos, appBizID); err != nil {
		return err
	}
	if len(needDelParamIDS) > 0 {
		deleteParametersReq := &KEP_WF_DM.DeleteParametersInSandboxRequest{
			AppID:        appBizID,
			ParameterIDs: needDelParamIDS,
		}
		_, err = rpc.DeleteParametersInSandbox(ctx, deleteParametersReq)
		if err != nil {
			return err
		}
	}

	return nil
}

// GetWorkflowVarByFlowId 根据工作流ID获取引用的变量
func GetWorkflowVarByFlowId(ctx context.Context, robotId, flowId string) ([]*entity.WorkflowVar, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var varList []*entity.WorkflowVar
	if err := db.Table(entity.WorkflowVar{}.TableName()).
		Where("f_is_deleted = 0").
		Where("f_robot_id = ?", robotId).
		Where("f_workflow_id = ?", flowId).
		Find(&varList).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowVarByFlowId failed:%+v,err:%+v", flowId, err)
		return nil, err
	}
	return varList, nil
}

// GetWorkflowRefIdsByFlowIds 查询工作流引用的其他工作流的id
func GetWorkflowRefIdsByFlowIds(ctx context.Context, robotId string, flowIDs []string) (
	[]*entity.WorkflowReference, error) {
	if len(flowIDs) == 0 {
		return nil, nil
	}
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	var workflowList []*entity.WorkflowReference
	err := db.Table(entity.WorkflowReference{}.TableName()).
		Where("f_is_deleted = 0").
		Where("f_robot_id = ?", robotId).
		Where("f_workflow_id IN (?)", flowIDs).
		Find(&workflowList).Error
	if err != nil {
		log.ErrorContextf(ctx, "GetWorkflowRefIdsByFlowIds failed:%+v, err:%v", flowIDs, err)
		return nil, err
	}
	return workflowList, nil
}

// TxInsertWorkflowVar 插入新的工作流与变量的引用关系
func TxInsertWorkflowVar(ctx context.Context, tx *gorm.DB, insertWorkflowVars []*entity.WorkflowVar) error {
	if len(insertWorkflowVars) == 0 {
		return nil
	}
	return tx.Table(entity.WorkflowVar{}.TableName()).Create(&insertWorkflowVars).Error
}

// TxInsertWorkflowRef 插入新的工作流与工作流引用关系
func TxInsertWorkflowRef(ctx context.Context, tx *gorm.DB, insertWorkflowRefs []*entity.WorkflowReference) error {
	if len(insertWorkflowRefs) == 0 {
		return nil
	}
	return tx.Table(entity.WorkflowReference{}.TableName()).Create(&insertWorkflowRefs).Error
}

// TxInsertWorkflowRefPlugin 插入新的工作流与插件工具的引用关系
func TxInsertWorkflowRefPlugin(ctx context.Context, tx *gorm.DB, insertWorkflowRefPlugins []*entity.WorkflowRefPlugin) error {
	if len(insertWorkflowRefPlugins) == 0 {
		return nil
	}
	return tx.Table(entity.WorkflowRefPlugin{}.TableName()).Create(&insertWorkflowRefPlugins).Error
}

// TxInsertWorkflowRefCustomModel 插入新的工作流与自定义的引用关系
func TxInsertWorkflowRefCustomModel(ctx context.Context, tx *gorm.DB, insertWorkflowCustomModelsRef []*entity.WorkflowCustomModel) error {
	if len(insertWorkflowCustomModelsRef) == 0 {
		return nil
	}
	return tx.Table(entity.WorkflowCustomModel{}.TableName()).Create(&insertWorkflowCustomModelsRef).Error
}

// UpdateWorkflowStatusFromSubAtom 增删改工作流下面的元素(示例问法等)修改工作流状态
func UpdateWorkflowStatusFromSubAtom(ctx context.Context, tx *gorm.DB, workflowId string) (error, *entity.Workflow) {
	uin, subUin := util.GetUinAndSubAccountUin(ctx)
	workflow := entity.Workflow{}
	sid := util.RequestID(ctx)
	if err := tx.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted=0 AND f_workflow_id=?", workflowId).
		Find(&workflow).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|UpdateWorkflowStatus|get workflow error:%+v", sid, err)
		return err, &workflow
	}

	// 发布状态中的工作流，不允许更改状态
	if workflow.ReleaseStatus == entity.WorkflowReleaseStatusPublishing {
		return errors.ErrWorkflowPublishing, &workflow
	}
	// 自动保存及非调试状态，通过工作流的状态，判断action
	flowState, action, err := TransformWorkflowState(ctx, workflow, uint32(KEP_WF.SaveWorkflowReq_DRAFT))
	if err != nil {
		log.ErrorContextf(ctx, "UpdateWorkflowStatusFromSubAtom TransformWorkflowState Failed err:%v", err)
		return err, &workflow
	}

	if err := tx.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted=0 AND f_workflow_id=?", workflowId).
		Updates(map[string]interface{}{
			"f_action":         action,
			"f_release_status": entity.WorkflowReleaseStatusUnPublished,
			"f_flow_state":     flowState,
			"f_staff_id":       util.StaffID(ctx),
			"f_update_time":    time.Now(),
			"f_uin":            uin,
			"f_sub_uin":        subUin,
		}).Error; err != nil {
		log.ErrorContextf(ctx, "sid:%s|UpdateWorkflowStatus|get workflow error:%+v", sid, err)
		return err, &workflow
	}

	log.InfoContextf(ctx, "UpdateWorkflowStatusFromSubAtom|workflowId:%s|%s|%s", workflowId, action, flowState)
	// 更新工作流状态
	workflow.WorkflowState = flowState
	workflow.ReleaseStatus = entity.WorkflowReleaseStatusUnPublished

	return nil, &workflow
}

// GetWorkflowsByRobotId 获取应用下的工作流
func GetWorkflowsByRobotId(ctx context.Context, robotId string) ([]*entity.Workflow, error) {
	db := database.GetLLMRobotWorkflowGORM().WithContext(ctx).Debug()
	workflows := make([]*entity.Workflow, 0)

	if err := db.Table(entity.Workflow{}.TableName()).
		Where("f_is_deleted=0 AND f_robot_id=?", robotId).
		Scan(&workflows).Error; err != nil {
		log.ErrorContextf(ctx, "GetWorkflowsByFlowIds|err:%+v", err)
		return workflows, errors.OpDataFromDBError("获取工作流失败")
	}
	return workflows, nil
}
