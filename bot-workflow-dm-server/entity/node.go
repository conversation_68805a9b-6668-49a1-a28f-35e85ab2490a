package entity

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/model"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

var (
	// OutputField 节点的output字段名称，对应前端的引用配置
	OutputField = "Output"
	// ErrorField 节点的error字段名称，对应前端的引用配置
	ErrorField = "Error"
	// ErrorCodeField 节点的error字段里错误码字段名称，对应前端的引用配置
	ErrorCodeField = "Error.FailCode"
	// ErrorMessageField 节点的output字段里错误信息字段名称，对应前端的引用配置
	ErrorMessageField = "Error.FailMessage"
	// TagNamePrefix 节点标签的前缀
	TagNamePrefix = "output."
)

// NodeStatus 节点状态
type NodeStatus string

const (
	// NodeStatusInit 初始状态
	NodeStatusInit NodeStatus = NodeStatus(model.NodeRunStateInit)
	// NodeStatusRunning 运行中
	NodeStatusRunning NodeStatus = NodeStatus(model.NodeRunStateRunning)
	// NodeStatusSuccess 成功
	NodeStatusSuccess NodeStatus = NodeStatus(model.NodeRunStateSuccess)
	// NodeStatusFailed 失败
	NodeStatusFailed NodeStatus = NodeStatus(model.NodeRunStateFailed)
	// NodeStatusCanceled 取消
	NodeStatusCanceled NodeStatus = NodeStatus(model.NodeRunStateCanceled)
	// NodeStatusWaitingReply 等待下轮触发
	NodeStatusWaitingReply NodeStatus = NodeStatus(model.NodeRunStateWaitingInput)
)

// NodeRun 节点的运行
type NodeRun struct {
	BelongNodeID                      string // 节点所属工作流被引用时的引用节点的ID，所属的工作流是被引用的时候非空。（后面如果要支持多层嵌套的话，需要修改成数组）
	NodeID                            string
	Status                            NodeStatus
	Input                             string
	Output                            string
	TaskOutput                        string
	Log                               string // 运行日志，部分节点才有，如：代码节点
	Thought                           string // 思考的内容
	ThoughtStartTime                  int64  // 思考开始时间
	ThoughtEndTime                    int64  // 思考结束时间
	Reply                             string
	OptionContents                    []string
	LastRequiredParameterResult       string // 上次的必填参数的值
	LastRequiredParameterRepeatedTime int    // 上次的必填参数相同的次数
	FailMessage                       string
	ErrorCode                         string
	FailTimes                         int                               // 失败的次数
	ReferencesMap                     map[string][]*KEP_WF_DM.Reference // 参考来源的数据。key为jsonPath的值，取值如： output（知识问答节点 or 检索节点）、output.context[0]（检索）
	StartTime                         time.Time                         // 开始时间
	CostMilliSeconds                  int64                             // 耗时
	StatisticInfo                     []*KEP_WF_DM.StatisticInfo        // LLM的消耗统计
	SubWorkflowCompleteOnce           bool                              // 表示子工作流完成一轮，下个运行是返回到父节点
	LoopInfo                          *LoopInfo                         // 循环节点的循环信息
	ParallelInfo                      *ParallelInfo                     // 并行节点的并行信息
	mu                                *sync.RWMutex
}

// LoopInfo 循环信息
type LoopInfo struct {
	ExitLoop       bool           // 退出循环
	Index          int            // 当前的循环Index，从1开始
	Log            string         // 循环的输出日志
	ErrorCode      int            // 循环的输出结果，0表示成功，非0表示失败
	Output         map[string]any // 循环的子工作流输出
	RunningIndexes []int          // 正在运行的子工作流的index数组
}

// ParallelInfo 并行信息
type ParallelInfo struct {
	MaxIndex        int                    // 并行的最大Index
	Index           int                    // 当前并行的Index，从1开始
	log             map[int]string         // 并行的输出日志，当ErrorCode不为空时，返回错误信息
	errorCode       map[int]string         // 并行的输出结果，为空表示成功，非空表示失败
	output          map[int]map[string]any // 并行的子工作流输出
	runningIndexes  map[int]struct{}       // 正在运行的子工作流的index set
	completeIndexes map[int]struct{}       // 结束运行的子工作流index set
	mu              *sync.RWMutex          `json:"-"`
}

// NewParallelInfo NewParallelInfo
func NewParallelInfo() *ParallelInfo {
	return &ParallelInfo{
		log:             make(map[int]string),
		errorCode:       make(map[int]string),
		output:          make(map[int]map[string]any),
		runningIndexes:  make(map[int]struct{}),
		completeIndexes: make(map[int]struct{}),
		mu:              new(sync.RWMutex),
	}
}

// Clone Clone
func (p ParallelInfo) Clone() *ParallelInfo {
	p.mu.RLock()
	defer p.mu.RUnlock()
	newParallelInfo := &ParallelInfo{
		MaxIndex:        p.MaxIndex,
		Index:           p.Index,
		log:             make(map[int]string),
		errorCode:       make(map[int]string),
		output:          make(map[int]map[string]any),
		runningIndexes:  make(map[int]struct{}),
		completeIndexes: make(map[int]struct{}),
		mu:              new(sync.RWMutex),
	}
	for index, log := range p.log {
		newParallelInfo.log[index] = log
	}
	for index, errorCode := range p.errorCode {
		newParallelInfo.errorCode[index] = errorCode
	}
	for index, output := range p.output {
		newParallelInfo.output[index] = output
	}
	for index := range p.runningIndexes {
		newParallelInfo.runningIndexes[index] = struct{}{}
	}
	for index := range p.completeIndexes {
		newParallelInfo.completeIndexes[index] = struct{}{}
	}
	return newParallelInfo
}

// MarshalJSON MarshalJSON
func (p ParallelInfo) MarshalJSON() ([]byte, error) {
	// 私有变量不会被序列化到json中，通过map的方式重载MarshalJSON函数，方便日志排查
	return json.Marshal(map[string]interface{}{
		"MaxIndex":        p.MaxIndex,
		"Index":           p.Index,
		"log":             p.log,
		"errorCode":       p.errorCode,
		"output":          p.output,
		"runningIndexes":  p.runningIndexes,
		"completeIndexes": p.completeIndexes,
	})
}

// GetLog GetLog
func (p ParallelInfo) GetLog(index int) string {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.log[index]
}

// GetErrorCode GetErrorCode
func (p ParallelInfo) GetErrorCode(index int) string {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.errorCode[index]
}

// GetOutput GetOutput
func (p ParallelInfo) GetOutput(index int) map[string]any {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.output[index]
}

// GetRunningIndexes GetRunningIndexes
func (p ParallelInfo) GetRunningIndexes() []int {
	p.mu.RLock()
	defer p.mu.RUnlock()
	indexes := make([]int, 0)
	for index := range p.runningIndexes {
		indexes = append(indexes, index)
	}
	return indexes
}

// GetCompleteIndexes GetCompleteIndexes
func (p ParallelInfo) GetCompleteIndexes() []int {
	p.mu.RLock()
	defer p.mu.RUnlock()
	indexes := make([]int, 0)
	for index := range p.completeIndexes {
		indexes = append(indexes, index)
	}
	return indexes
}

// SetLog SetLog
func (p ParallelInfo) SetLog(index int, log string) {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.log[index] = log
}

// SetErrorCode SetErrorCode
func (p ParallelInfo) SetErrorCode(index int, errorCode string) {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.errorCode[index] = errorCode
}

// SetOutput SetOutput
func (p ParallelInfo) SetOutput(index int, output map[string]any) {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.output[index] = output
}

// SetRunningIndex SetRunningIndex
func (p ParallelInfo) SetRunningIndex(index int) {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.runningIndexes[index] = struct{}{}
}

// DeleteRunningIndex DeleteRunningIndex
func (p ParallelInfo) DeleteRunningIndex(index int) {
	p.mu.Lock()
	defer p.mu.Unlock()
	delete(p.runningIndexes, index)
}

// SetCompleteIndex SetCompleteIndex
func (p ParallelInfo) SetCompleteIndex(index int) {
	p.mu.Lock()
	defer p.mu.Unlock()
	p.completeIndexes[index] = struct{}{}
}

// IsIndexRunning IsIndexRunning
func (p ParallelInfo) IsIndexRunning(index int) bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	_, ok := p.runningIndexes[index]
	return ok
}

// IsIndexCompleted IsIndexCompleted
func (p ParallelInfo) IsIndexCompleted(index int) bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	_, ok := p.completeIndexes[index]
	return ok
}

// NodeTask 节点任务
type NodeTask struct {
	BelongNodeID string // 节点所属工作流被引用时的引用节点的ID，所属的工作流是被引用的时候非空。（后面如果要支持多层嵌套的话，需要修改成数组）
	Node         *KEP_WF.WorkflowNode
}

// IsInit 节点是否初始状态
func (r *NodeRun) IsInit() bool {
	if r == nil {
		return true
	}
	return r.Status == NodeStatusInit
}

// IsRunning 节点是否运行中
func (r *NodeRun) IsRunning() bool {
	if r == nil {
		return false
	}
	return r.Status.IsRunning()
}

// IsFailed 节点是否失败
func (r *NodeRun) IsFailed() bool {
	if r == nil {
		return false
	}
	return r.Status == NodeStatusFailed
}

// IsWaiting 节点是否等待中
func (r *NodeRun) IsWaiting() bool {
	if r == nil {
		return false
	}
	return r.Status == NodeStatusWaitingReply
}

// IsFinished 节点是否结束
func (r *NodeRun) IsFinished() bool {
	if r == nil {
		return false
	}
	return r.Status.IsFinished()
}

// CanCurNodeRun 判断工作流节点、循环节点是否应该入到执行队列
func (r *NodeRun) CanCurNodeRun() bool {
	// 还没运行过的
	if r == nil || r.Status == NodeStatusInit {
		return true
	}
	return r.SubWorkflowCompleteOnce
}

// GetReferencesByPath 获取参考来源
func (r *NodeRun) GetReferencesByPath(jsonPath string) []*KEP_WF_DM.Reference {
	if r == nil || r.ReferencesMap == nil {
		return nil
	}
	if r.mu == nil {
		r.mu = new(sync.RWMutex)
	}
	r.mu.RLock()
	defer r.mu.RUnlock()

	// TODO 目前支持知识问答节点
	if strings.HasPrefix(jsonPath, OutputField+".") {
		jsonPath = jsonPath[len(OutputField)+1:]
	}

	return r.ReferencesMap[jsonPath]
}

// SetReferencesMap 设置referencesMap
func (r *NodeRun) SetReferencesMap(referencesMap map[string][]*KEP_WF_DM.Reference) {
	if r.mu == nil {
		r.mu = new(sync.RWMutex)
	}
	r.mu.Lock()
	defer r.mu.Unlock()
	r.ReferencesMap = referencesMap
}

// Reset 重置数据
func (r *NodeRun) Reset() {
	if r.mu == nil {
		r.mu = new(sync.RWMutex)
	}
	r.mu.Lock()
	defer r.mu.Unlock()
	r.Status = NodeStatusInit
	r.Input = ""
	r.Output = ""
	r.TaskOutput = ""
	r.Reply = ""
	r.FailMessage = ""
	r.ErrorCode = ""
	r.OptionContents = nil
	r.LastRequiredParameterResult = ""
	r.LastRequiredParameterRepeatedTime = 0
	r.ReferencesMap = nil
	r.LoopInfo = nil
	r.ParallelInfo = nil
}

// // MarshalJSON 避免并发问题
// func (r *NodeRun) MarshalJSON() ([]byte, error) {
//	if r.mu == nil {
//		r.mu = new(sync.RWMutex)
//	}
//	r.mu.RLock()
//	defer r.mu.RUnlock()
//	return json.Marshal(*r)
// }

// IsRunning 是否运行中
func (s NodeStatus) IsRunning() bool {
	return s == NodeStatusWaitingReply || s == NodeStatusRunning
}

// IsFinished 是否结束
func (s NodeStatus) IsFinished() bool {
	return s == NodeStatusSuccess || s == NodeStatusFailed || s == NodeStatusCanceled
}

// GetApiNodeStatus 转换API的节点状态
func GetApiNodeStatus(nodeStatus NodeStatus) KEP_WF_DM.RunNodeInfo_StatusType {
	switch nodeStatus {
	case NodeStatusRunning, NodeStatusWaitingReply:
		return KEP_WF_DM.RunNodeInfo_RUNNING
	case NodeStatusSuccess:
		return KEP_WF_DM.RunNodeInfo_SUCCESS
	case NodeStatusFailed:
		return KEP_WF_DM.RunNodeInfo_FAILED
	case NodeStatusCanceled:
		return KEP_WF_DM.RunNodeInfo_FAILED // TODO 等前端支持后，改为CANCELED
	case NodeStatusInit:
		return KEP_WF_DM.RunNodeInfo_INIT
	default:
		return KEP_WF_DM.RunNodeInfo_INIT
	}
}

// GetNodeRunKey NodeRun的唯一key
func GetNodeRunKey(belongNodeID, nodeID string) string {
	return fmt.Sprintf("%s_%s", belongNodeID, nodeID)
}
