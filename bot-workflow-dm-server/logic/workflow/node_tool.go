package workflow

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/http/httputil"
	"net/url"
	"reflect"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	scurlcomm "git.woa.com/dialogue-platform/go-comm/rainbow-comm/scurl"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	secapi "git.woa.com/sec-api/go/scurl"
)

// executeToolNode 执行工具节点，输出结构map[string]any，解析的api输出
func executeToolNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	start := time.Now()
	// 节点状态初始化
	nodeResult := entity.NodeResult{
		BelongNodeID: nodeTask.BelongNodeID,
		NodeID:       node.GetNodeID(),
		Status:       entity.NodeStatusRunning,
	}
	nodeResultQueue <- nodeResult

	defer func() {
		nodeResult.CostMilliSeconds = time.Since(start).Milliseconds()
		nodeResultQueue <- nodeResult
	}()
	// 参数初始化
	data := node.GetToolNodeData()
	if data == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrMissingParam.Msg
		nodeResult.ErrorCode = entity.ErrMissingParam.Code
		return
	}
	// api调用
	headerValues, queryValues, bodyValues, err := parseToolParams(session, nodeTask.BelongNodeID, data)
	if err != nil {
		LogWorkflow(ctx).Warnf("invalid node data: %v", err)
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.ErrInvalidParam.Msg + ": " + err.Error()
		nodeResult.ErrorCode = entity.ErrInvalidParam.Code
		return
	}
	nodeResult.Input = constructToolInput(headerValues, queryValues, bodyValues)

	outputs := node.GetOutputs()
	// 过滤掉第一层的Output
	if len(outputs) == 1 && outputs[0].GetTitle() == entity.OutputField &&
		outputs[0].GetType() == KEP_WF.TypeEnum_OBJECT {
		outputs = outputs[0].GetProperties()
	}
	if data.GetAPI().GetCallingMethod() == KEP_WF.ToolNodeData_STREAMING {
		// 处理流式请求

		chunks, errCh, err := streamAPIResponse(ctx, data, headerValues, queryValues, bodyValues, session.Uin)
		if err != nil {
			LogWorkflow(ctx).Warnf("streamAPIResponse error: %v", err)
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.ErrCallAPIFail.Msg + ": " + err.Error()
			nodeResult.ErrorCode = entity.ErrCallAPIFail.Code
			nodeResultQueue <- nodeResult
		}

		var lastOutput map[string]any
		for {
			select {
			case chunk, ok := <-chunks:
				if !ok {
					nodeResult.Status = entity.NodeStatusSuccess
					return
				}
				nodeResult.TaskOutput += chunk

				// 解析原始SSE消息
				rspValue := processSSEEvent(chunk)

				if lastOutput == nil {
					lastOutput = util.ParseOutputValue(rspValue, outputs)
					nodeResult.Output = util.ToJsonString(lastOutput)
				} else {
					newOutput := util.ParseOutputValue(rspValue, outputs)
					newOutput = deepMergeOutput(lastOutput, newOutput, outputs)
					nodeResult.Output = util.ToJsonString(newOutput)
					lastOutput = newOutput
				}
				nodeResult.Status = entity.NodeStatusRunning
				nodeResultQueue <- nodeResult

			case err := <-errCh:
				if err != nil {
					LogWorkflow(ctx).Warnf("streamAPIResponse error: %v", err)
					nodeResult.Status = entity.NodeStatusFailed
					nodeResult.FailMessage = entity.ErrCallAPIFail.Msg + ": " + err.Error()
					nodeResult.ErrorCode = entity.ErrCallAPIFail.Code
					nodeResultQueue <- nodeResult
				}
				return

			case <-ctx.Done():
				LogWorkflow(ctx).Warnf("streaming interrupted by context")
				nodeResult.Status = entity.NodeStatusFailed
				nodeResult.FailMessage = entity.ErrLLMTimeout.Msg
				nodeResult.ErrorCode = entity.ErrLLMTimeout.Code
				nodeResultQueue <- nodeResult
				return
			}
		}
	} else {
		// Handle non-streaming response
		rspRaw, err := callAPI(ctx, data, headerValues, queryValues, bodyValues, session.Uin)
		if err != nil {
			LogWorkflow(ctx).Warnf("callAPI error: %v", err)
			nodeResult.Status = entity.NodeStatusFailed
			nodeResult.FailMessage = entity.ErrCallAPIFail.Msg + ": " + err.Error()
			nodeResult.ErrorCode = entity.ErrCallAPIFail.Code
			return
		}
		nodeResult.TaskOutput = rspRaw
		rspValue := make(map[string]any)
		_ = json.Unmarshal([]byte(rspRaw), &rspValue)
		nodeResult.Output = util.ToJsonString(util.ParseOutputValue(rspValue, outputs))
		nodeResult.Status = entity.NodeStatusSuccess
	}
}

func parseToolParams(session *entity.Session, belongID string, data *KEP_WF.ToolNodeData) (
	map[string]any, map[string]any, map[string]any, error) {
	var err error
	var headerValues, queryValues, bodyValues map[string]any
	var debugNodeHeader, debugNodeQuery, debugNodeBody string
	if session.IsDebuggingNode() && belongID == "" {
		debugNodeHeader = session.DebuggingNode.ToolInput.Header
		debugNodeQuery = session.DebuggingNode.ToolInput.Query
		debugNodeBody = session.DebuggingNode.ToolInput.Body
	}
	// 解析header
	headerValues, err = parseReqParams(session, belongID, data.GetHeader(), debugNodeHeader)
	if err != nil {
		return nil, nil, nil, err
	}
	// 解析query
	queryValues, err = parseReqParams(session, belongID, data.GetQuery(), debugNodeQuery)
	if err != nil {
		return nil, nil, nil, err
	}
	// 解析body
	bodyValues, err = parseReqParams(session, belongID, data.GetBody(), debugNodeBody)
	if err != nil {
		return nil, nil, nil, err
	}
	// 判断鉴权方式，增加鉴权参数
	if data.GetAPI().GetAuthType() == KEP_WF.ToolNodeData_API_KEY {
		if data.GetAPI().GetKeyLocation() == KEP_WF.ToolNodeData_HEADER {
			headerValues[data.GetAPI().GetKeyParamName()] = data.GetAPI().GetKeyParamValue()
		} else {
			queryValues[data.GetAPI().GetKeyParamName()] = data.GetAPI().GetKeyParamValue()
		}
	}
	return headerValues, queryValues, bodyValues, nil
}

func getValueFromMapStr(mapStr, name string, valueType KEP_WF.TypeEnum) (any, error) {
	orgMap := make(map[string]any)
	err := json.Unmarshal([]byte(mapStr), &orgMap)
	if err != nil {
		return nil, err
	}
	value, ok := orgMap[name]
	if !ok {
		return nil, nil
	}
	convertedValue, err := util.ConvertValue(value, valueType)
	if err != nil {
		return nil, err
	}
	return convertedValue, nil
}

// func mapStrToParam(mapStr string, params []*KEP_WF.ToolNodeData_RequestParam) (map[string]any, error) {
//	orgMap := make(map[string]any)
//	err := json.Unmarshal([]byte(mapStr), &orgMap)
//	if err != nil {
//		return nil, err
//	}
//	convertedMap := make(map[string]any)
//	for _, param := range params {
//		paramName := param.GetParamName()
//		value, ok := orgMap[paramName]
//		if !ok {
//			continue
//		}
//		convertedValue, err := util.ConvertValue(value, param.GetParamType())
//		if err != nil {
//			return nil, err
//		}
//		convertedMap[paramName] = convertedValue
//	}
//	return convertedMap, nil
// }

// callAPI 发起http请求，返回字符串
func callAPI(ctx context.Context, node *KEP_WF.ToolNodeData, headerValues, queryValues, bodyValues map[string]any,
	uin uint64) (string, error) {

	req, err := createHTTPRequest(ctx, node, headerValues, queryValues, bodyValues)
	if err != nil {
		return "", err
	}

	reqData, _ := httputil.DumpRequestOut(req, true)

	// URL白名单
	uinStr := fmt.Sprintf("%d", uin)
	whiteURLs := append(config.GetMainConfig().API.WhiteURLs, scurlcomm.GetScurlDomainWLByUin(ctx, uinStr)...)
	whitePorts := append(config.GetMainConfig().API.Ports, scurlcomm.GetScurlPortWLByUin(ctx, uinStr)...)
	timeout := time.Duration(config.GetMainConfig().API.Timeout) * time.Second
	options := []secapi.SecOptions{
		secapi.WithConfTimeout(timeout),
		secapi.WithUnsafeDomain(whiteURLs),
		secapi.WithAllowPorts(whitePorts),
	}
	if allowAllURL(whiteURLs) {
		options = append(options, secapi.WithUnsafeInner(true))
	}
	httpClient := secapi.NewSafeClient(options...)
	var body []byte
	step := trace.StartStep(ctx, trace.StepKeyAPICustomHttp, string(reqData))
	rsp, err := httpClient.Do(req)
	defer func() {
		step.RecordEnd(string(body), err)
	}()
	if err != nil {
		return "", fmt.Errorf("do http request failed, url: %s, header: %s, body: %s, error: %w",
			req.URL, util.ToJsonString(headerValues), util.ToJsonString(bodyValues), err)
	}
	defer func() { _ = rsp.Body.Close() }()
	if rsp.StatusCode < 200 || rsp.StatusCode > 299 {
		// 固定报错格式，富图项目需求，需要获取到状态码和其他信息
		return "", fmt.Errorf("http status code is not 2XX, status code: %d, url: %s, header: %s, body: %s",
			rsp.StatusCode, req.URL, util.ToJsonString(headerValues), util.ToJsonString(bodyValues))
	}
	body, err = io.ReadAll(rsp.Body)
	if err != nil {
		return "", fmt.Errorf("read response body failed, url: %s, header: %s, body: %s",
			req.URL, util.ToJsonString(headerValues), util.ToJsonString(bodyValues))
	}
	return string(body), nil
}

// streamAPIResponse handles streaming API responses using Server-Sent Events (SSE)
// It reads the response body line by line and sends each chunk through the provided channel
func streamAPIResponse(ctx context.Context, nodeData *KEP_WF.ToolNodeData, headerValues, queryValues,
	bodyValues map[string]any, uin uint64) (chunks chan string, errCh chan error, err error) {
	// Set up SSE headers
	headerValues["Accept"] = "text/event-stream"
	headerValues["Cache-Control"] = "no-cache"
	headerValues["Connection"] = "keep-alive"

	// Make the HTTP request
	req, err := createHTTPRequest(ctx, nodeData, headerValues, queryValues, bodyValues)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	uinStr := fmt.Sprintf("%d", uin)
	whiteURLs := append(config.GetMainConfig().API.WhiteURLs, scurlcomm.GetScurlDomainWLByUin(ctx, uinStr)...)
	whitePorts := append(config.GetMainConfig().API.Ports, scurlcomm.GetScurlPortWLByUin(ctx, uinStr)...)
	// Create HTTP client with timeout
	timeout := time.Duration(config.GetMainConfig().API.Timeout) * time.Second
	options := []secapi.SecOptions{
		secapi.WithConfTimeout(timeout),
		secapi.WithUnsafeDomain(whiteURLs),
		secapi.WithAllowPorts(whitePorts),
	}
	if allowAllURL(whiteURLs) {
		options = append(options, secapi.WithUnsafeInner(true))
	}
	httpClient := secapi.NewSafeClient(options...)

	// Execute the request
	LogWorkflow(ctx).Debugf("Starting streaming request to %s", req.URL)
	rsp, err := httpClient.Do(req)
	if err != nil {
		return nil, nil, fmt.Errorf("HTTP request failed: %w", err)
	}

	// Check response status code
	if rsp.StatusCode < 200 || rsp.StatusCode > 299 {
		body, _ := io.ReadAll(rsp.Body)
		_ = rsp.Body.Close() // 错误情况下关闭 body
		return nil, nil, fmt.Errorf("HTTP status %d: %s", rsp.StatusCode, string(body))
	}

	// Process the streaming response
	reader := bufio.NewReader(rsp.Body)
	chunks = make(chan string)
	errCh = make(chan error, 1)

	go func() {
		defer func() {
			close(chunks)
			close(errCh)
			_ = rsp.Body.Close()
			LogWorkflow(ctx).Debugf("Closed response body")
		}()

		sseEvent := bytes.NewBuffer(nil)

		for {
			select {
			case <-ctx.Done():
				errCh <- ctx.Err()
				return
			default:
				line, err := reader.ReadBytes('\n')
				if err != nil {
					if errors.Is(err, io.EOF) {
						// 如果缓冲区中还有数据，发送最后一个事件
						if sseEvent.Len() > 0 {
							chunks <- sseEvent.String()
						}
						return
					}
					errCh <- fmt.Errorf("failed to read from stream: %w", err)
					return
				}

				// 将行添加到当前事件
				sseEvent.Write(line)

				// 检查是否为空行（表示事件结束）
				lineStr := strings.TrimSpace(string(line))
				if lineStr == "" {
					// 如果缓冲区不为空，发送完整事件
					if sseEvent.Len() > 0 {
						eventStr := sseEvent.String()
						// 检查是否为 [DONE] 消息
						if strings.Contains(eventStr, "data: [DONE]") {
							LogWorkflow(ctx).Debugf("Received stream end marker")
							return
						}
						chunks <- eventStr
						sseEvent.Reset()
					}
				} else if strings.HasPrefix(lineStr, "data: [DONE]") {
					// 特殊处理 [DONE] 消息
					LogWorkflow(ctx).Debugf("Received stream end marker")
					return
				}
			}
		}
	}()

	return chunks, errCh, nil
}

// processSSEEvent 处理 SSE 事件，将其转换为map[string]any
// 原始内容：
// event: notification
// data: {"text": "alert 2", "content": "今天"}
// 转成的结果： {"event": "notification", "data": {"text": "alert 2", "content": "今天"}}
func processSSEEvent(sseEvent string) map[string]any {
	result := make(map[string]any)
	scanner := bufio.NewScanner(strings.NewReader(sseEvent))

	// 逐行解析 SSE 事件
	for scanner.Scan() {
		line := scanner.Text()
		line = strings.TrimSpace(line)

		// 跳过空行
		if line == "" {
			continue
		}

		// 查找第一个冒号的位置
		colonIdx := strings.Index(line, ":")
		if colonIdx == -1 {
			continue // 跳过没有冒号的行
		}

		// 提取字段名和值
		fieldName := strings.TrimSpace(line[:colonIdx])
		fieldValue := strings.TrimSpace(line[colonIdx+1:])
		var dataI any
		if err := json.Unmarshal([]byte(fieldValue), &dataI); err == nil {
			result[fieldName] = dataI
		} else {
			result[fieldName] = fieldValue
		}
	}

	return result
}

// createHTTPRequest creates an HTTP request with the given parameters
func createHTTPRequest(ctx context.Context, node *KEP_WF.ToolNodeData, headerValues, queryValues,
	bodyValues map[string]any) (*http.Request, error) {
	apiData := node.GetAPI()
	method := apiData.GetMethod()

	// Build URL with query parameters
	newURL, err := addQueryToURL(apiData.URL, queryValues)
	if err != nil {
		return nil, fmt.Errorf("append query to URL failed, url: %s, query: %s, error: %w",
			apiData.URL, util.ToJsonString(queryValues), err)
	}
	LogWorkflow(ctx).Debugf("callAPI|url: %s", newURL)

	// Create request with appropriate body based on method
	var req *http.Request
	switch method {
	case "GET":
		req, err = http.NewRequest(apiData.Method, newURL, nil)
		if err != nil {
			return nil, fmt.Errorf("make up request failed, url: %s, method: %s, body: %s, error: %w",
				apiData.URL, apiData.GetMethod(), util.ToJsonString(bodyValues), err)
		}

	case "POST", "DELETE":
		postBodyBytes := []byte(util.ToJsonString(bodyValues))
		req, err = http.NewRequest(apiData.Method, newURL, bytes.NewReader(postBodyBytes))
		if err != nil {
			return nil, fmt.Errorf("make up request failed, url: %s, method: %s, body: %s, eror: %w",
				apiData.URL, apiData.GetMethod(), util.ToJsonString(bodyValues), err)
		}

		// Set Content-Type if not already set
		if req.Header.Get("Content-Type") == "" {
			req.Header.Set("Content-Type", "application/json")
		}

	default:
		return nil, fmt.Errorf("unsupported HTTP method: %s", method)
	}

	// 添加header。用户没设置的Content-Type，才加上默认的
	noContentType := true
	for headKey, headValue := range headerValues {
		if strings.EqualFold(headKey, "Content-Type") {
			noContentType = false
		}
		req.Header.Set(headKey, util.ToJsonStringNotNull(headValue))
	}
	if apiData.Method != "GET" && noContentType {
		req.Header.Set("Content-Type", "application/json")
	}

	return req, nil
}

func allowAllURL(whiteURLs []string) bool {
	for _, whiteURL := range whiteURLs {
		if whiteURL == "allow_all" {
			return true
		}
	}
	return false
}

func addQueryToURL(reqURL string, queryParams map[string]any) (string, error) {
	parsed, err := url.Parse(reqURL)
	if err != nil {
		return "", err
	}

	values := parsed.Query()
	for key, value := range queryParams {
		values.Add(key, util.ToJsonString(value))
	}

	newURL := url.URL{
		Scheme:      parsed.Scheme,
		Opaque:      parsed.Opaque,
		User:        parsed.User,
		Host:        parsed.Host,
		Path:        parsed.Path,
		RawPath:     parsed.RawPath,
		ForceQuery:  parsed.ForceQuery,
		RawQuery:    values.Encode(),
		Fragment:    parsed.Fragment,
		RawFragment: parsed.RawFragment,
	}

	return newURL.String(), nil
}

func parseReqParams(session *entity.Session, belongID string, params []*KEP_WF.ToolNodeData_RequestParam,
	debugMapStr string) (
	map[string]any, error) {
	res := make(map[string]any)
	for _, param := range params {
		if shouldGetFromDebug(session.IsDebuggingNode(), belongID, param.GetInput().GetInputType()) {
			// 从字符串数组中取字段的值
			value, err := getValueFromMapStr(debugMapStr, param.GetParamName(), param.GetParamType())
			if err != nil {
				return nil, fmt.Errorf("param %s is invalid", param.GetParamName())
			}
			res[param.GetParamName()] = value
			continue
		}
		// 非“输入”的（节点引用、系统参数、自定义参数等），不需要判断类型，直接读取，因为上游可能没传子结构
		if param.GetInput().GetInputType() != KEP_WF.InputSourceEnum_USER_INPUT {
			value := getInputValue(session, belongID, param.GetInput(), nil)
			if param.GetIsRequired() && (value == nil || value == "") {
				return nil, fmt.Errorf("param %s is required", param.GetParamName())
			}
			res[param.GetParamName()] = value
			continue
		}

		// 非引用的，下面都是“输入”的
		switch param.GetParamType() {
		case KEP_WF.TypeEnum_OBJECT:
			subRes, err := parseReqParams(session, belongID, param.GetSubParams(), "")
			if err != nil {
				return nil, err
			}
			if param.GetIsRequired() && len(subRes) == 0 {
				return nil, fmt.Errorf("param %s is required", param.GetParamName())
			}
			res[param.GetParamName()] = subRes
		case KEP_WF.TypeEnum_ARRAY_STRING, KEP_WF.TypeEnum_ARRAY_INT, KEP_WF.TypeEnum_ARRAY_FLOAT,
			KEP_WF.TypeEnum_ARRAY_BOOL, KEP_WF.TypeEnum_ARRAY_OBJECT:
			subRes := make([]any, 0)
			for _, subParam := range param.GetSubParams() {
				var subResItem any
				var err error
				value := reflect.ValueOf(getInputValue(session, belongID, subParam.GetInput(), nil))
				if param.GetParamType() == KEP_WF.TypeEnum_ARRAY_STRING &&
					subParam.GetParamType() == KEP_WF.TypeEnum_STRING {
					subResItem, err = util.GetStringFromValue(value)
				} else if param.GetParamType() == KEP_WF.TypeEnum_ARRAY_INT &&
					subParam.GetParamType() == KEP_WF.TypeEnum_INT {
					subResItem, err = util.GetIntFromValue(value)
				} else if param.GetParamType() == KEP_WF.TypeEnum_ARRAY_FLOAT &&
					subParam.GetParamType() == KEP_WF.TypeEnum_FLOAT {
					subResItem, err = util.GetFloatFromValue(value)
				} else if param.GetParamType() == KEP_WF.TypeEnum_ARRAY_BOOL &&
					subParam.GetParamType() == KEP_WF.TypeEnum_BOOL {
					subResItem, err = util.GetBoolFromValue(value)
				} else if param.GetParamType() == KEP_WF.TypeEnum_ARRAY_OBJECT &&
					subParam.GetParamType() == KEP_WF.TypeEnum_OBJECT {
					subResItem, err = parseReqParams(session, belongID, subParam.GetSubParams(), "")
				} else {
					return nil, fmt.Errorf("invalid subParam type: %v", subParam.GetParamType())
				}
				if err != nil {
					log.Warnf("parse failed, ouput value:%v, type:%v, err:%v", value, param.GetParamType(), err)
					if param.GetIsRequired() {
						return nil, err
					}
				}
				subRes = append(subRes, subResItem)
			}
			if param.GetIsRequired() && len(subRes) == 0 {
				return nil, fmt.Errorf("param %s is required", param.GetParamName())
			}
			res[param.GetParamName()] = subRes
		default:
			var err error
			paramValue := getInputValue(session, belongID, param.GetInput(), nil)
			if param.GetIsRequired() && (paramValue == nil || paramValue == "") {
				return nil, fmt.Errorf("param %s is required", param.GetParamName())
			}
			value := reflect.ValueOf(paramValue)
			// 不管原来什么类型，可以统一转成字符串，再转成对应的类型。
			switch param.GetParamType() {
			case KEP_WF.TypeEnum_STRING:
				res[param.GetParamName()], err = util.GetStringFromValue(value)
			case KEP_WF.TypeEnum_INT:
				res[param.GetParamName()], err = util.GetIntFromValue(value)
			case KEP_WF.TypeEnum_FLOAT:
				res[param.GetParamName()], err = util.GetFloatFromValue(value)
			case KEP_WF.TypeEnum_BOOL:
				res[param.GetParamName()], err = util.GetBoolFromValue(value)
			default:
				return nil, fmt.Errorf("invalid paramValue: %v", paramValue)
			}
			if err != nil {
				log.Warnf("parse failed, ouput value:%v, type:%v, err:%v", value, param.GetParamType(), err)
				if param.GetIsRequired() {
					return nil, err
				}
			}
		}
	}
	return res, nil
}

func constructToolInput(headerValues, queryValues, bodyValues map[string]any) string {
	input := make(map[string]any)
	if len(headerValues) > 0 {
		input["header"] = headerValues
	}
	if len(queryValues) > 0 {
		input["query"] = queryValues
	}
	if len(bodyValues) > 0 {
		input["body"] = bodyValues
	}
	return util.ToJsonString(input)
}

// deepMergeOutput 合并输出结果
// 如果第一层的key不同，直接并列合并
// 如果第一层的key相同，则当需要"追加"的时候才会把字符串拼接，否则都是以newOutput的值为最终值
// "追加"的条件：在同一个字符串类型的字段有值，outputs里面对应的OutputParam的AnalysisMethod为AnalysisMethodTypeEnum_INCREMENT
func deepMergeOutput(lastOutput, newOutput map[string]any, outputs []*KEP_WF.OutputParam) map[string]any {
	// 如果lastOutput为nil，直接返回newOutput
	if lastOutput == nil {
		return newOutput
	}

	// 如果newOutput为nil，直接返回lastOutput
	if newOutput == nil {
		return lastOutput
	}

	// 创建结果map
	result := make(map[string]any)

	// 先复制lastOutput中的所有键值对
	for k, v := range lastOutput {
		result[k] = v
	}

	// 遍历newOutput中的键值对
	for key, newValue := range newOutput {
		// 检查lastOutput中是否存在相同的key
		lastValue, exists := lastOutput[key]
		if !exists {
			// 如果lastOutput中不存在该key，直接添加
			result[key] = newValue
			continue
		}

		// 查找对应的OutputParam
		var outputParam *KEP_WF.OutputParam
		for _, param := range outputs {
			if param.GetTitle() == key {
				outputParam = param
				break
			}
		}

		// 根据值的类型进行不同的处理
		switch lastValueTyped := lastValue.(type) {
		case map[string]any:
			// 如果lastValue是map，检查newValue是否也是map
			if newValueMap, ok := newValue.(map[string]any); ok {
				// 递归合并map
				var childOutputs []*KEP_WF.OutputParam
				if outputParam != nil {
					childOutputs = outputParam.GetProperties()
				}
				result[key] = deepMergeOutput(lastValueTyped, newValueMap, childOutputs)
			} else {
				// 类型不匹配，使用新值
				result[key] = newValue
			}

		case []map[string]any:
			// 如果lastValue是数组，检查newValue是否也是数组
			if newValueArray, ok := newValue.([]map[string]any); ok {
				// 如果是数组类型，我们需要根据数组元素的索引进行合并
				// 这里假设数组元素可以通过索引对应起来
				if len(lastValueTyped) == len(newValueArray) {
					mergedArray := make([]map[string]any, len(newValueArray))
					var childOutputs []*KEP_WF.OutputParam
					if outputParam != nil && outputParam.GetType() == KEP_WF.TypeEnum_ARRAY_OBJECT {
						childOutputs = outputParam.GetProperties()
					}

					for i := 0; i < len(lastValueTyped); i++ {
						// 对数组中的每个元素递归调用合并函数
						lastItem := lastValueTyped[i]
						newItem := newValueArray[i]

						mergedArray[i] = deepMergeOutput(lastItem, newItem, childOutputs)
					}
					result[key] = mergedArray
				} else {
					// 数组长度不同，使用新值
					result[key] = newValue
				}
			} else {
				// 类型不匹配或不需要递归合并，使用新值
				result[key] = newValue
			}

		case string:
			// 如果lastValue是字符串，检查newValue是否也是字符串
			if newValueStr, ok := newValue.(string); ok && outputParam != nil &&
				outputParam.GetAnalysisMethod() == KEP_WF.AnalysisMethodTypeEnum_INCREMENT {
				// 字符串追加
				result[key] = lastValueTyped + newValueStr
			} else {
				// 类型不匹配或不需要递归合并，使用新值
				result[key] = newValue
			}

		default:
			// 其他类型，使用新值
			result[key] = newValue
		}
	}

	return result
}

// // mergeOpenAIStreamResponses 特殊处理OpenAI流式响应格式的合并
// func mergeOpenAIStreamResponses(lastOutput, newOutput map[string]interface{}) map[string]interface{} {
//	result := make(map[string]interface{})
//
//	// 复制newOutput的基本属性（model, service_tier等）
//	for k, v := range newOutput {
//		if k != "choices" {
//			result[k] = v
//		}
//	}
//
//	// 处理choices数组
//	lastChoices, lastHasChoices := lastOutput["choices"].([]interface{})
//	newChoices, newHasChoices := newOutput["choices"].([]interface{})
//
//	if !lastHasChoices || !newHasChoices {
//		// 如果任一输入没有choices，直接使用newOutput的choices
//		result["choices"] = newOutput["choices"]
//		return result
//	}
//
//	// 合并choices数组
//	mergedChoices := make([]interface{}, 0, len(newChoices))
//
//	// 创建索引映射
//	choiceIndexMap := make(map[float64]int)
//	for i, choice := range lastChoices {
//		choiceMap, ok := choice.(map[string]interface{})
//		if !ok {
//			continue
//		}
//
//		index, ok := choiceMap["index"].(float64)
//		if !ok {
//			continue
//		}
//
//		choiceIndexMap[index] = i
//	}
//
//	// 处理每个新的choice
//	for _, newChoice := range newChoices {
//		newChoiceMap, ok := newChoice.(map[string]interface{})
//		if !ok {
//			mergedChoices = append(mergedChoices, newChoice)
//			continue
//		}
//
//		newIndex, ok := newChoiceMap["index"].(float64)
//		if !ok {
//			mergedChoices = append(mergedChoices, newChoice)
//			continue
//		}
//
//		// 查找对应的旧choice
//		if lastIndex, exists := choiceIndexMap[newIndex]; exists {
//			lastChoiceMap, ok := lastChoices[lastIndex].(map[string]interface{})
//			if !ok {
//				mergedChoices = append(mergedChoices, newChoice)
//				continue
//			}
//
//			// 合并delta
//			lastDelta, lastHasDelta := lastChoiceMap["delta"].(map[string]interface{})
//			newDelta, newHasDelta := newChoiceMap["delta"].(map[string]interface{})
//
//			if lastHasDelta && newHasDelta {
//				// 创建合并后的choice
//				mergedChoice := make(map[string]interface{})
//				for k, v := range newChoiceMap {
//					if k != "delta" {
//						mergedChoice[k] = v
//					}
//				}
//
//				// 合并delta
//				mergedDelta := make(map[string]interface{})
//				for k, v := range newDelta {
//					mergedDelta[k] = v
//				}
//
//				// 特殊处理content字段
//				lastContent, lastHasContent := lastDelta["content"].(string)
//				newContent, newHasContent := newDelta["content"].(string)
//
//				if lastHasContent && newHasContent {
//					mergedDelta["content"] = lastContent + newContent
//				}
//
//				mergedChoice["delta"] = mergedDelta
//				mergedChoices = append(mergedChoices, mergedChoice)
//			} else {
//				// 如果没有delta，使用新choice
//				mergedChoices = append(mergedChoices, newChoice)
//			}
//		} else {
//			// 如果没有对应的旧choice，使用新choice
//			mergedChoices = append(mergedChoices, newChoice)
//		}
//	}
//
//	result["choices"] = mergedChoices
//	return result
// }
