package workflow

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	secapi "git.woa.com/sec-api/go/scurl"
	"github.com/agiledragon/gomonkey/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func Test_executeToolNode(t *testing.T) {
	// sss := "{\"id\":\"chatcmpl-672cbefb46c9d6a09223c779\",\"object\":\"chat.completion\",\"created\":1730985723,\"model\":\"moonshot-v1-8k\",\"choices\":[{\"index\":0,\"message\":{\"role\":\"assistant\",\"content\":\"中国的国庆节假期通常为7天，从10月1日开始，到10月7日结束。这个假期也被称为“黄金周”，是中国最长的公共假期之一。不过，具体的放假安排可能会根据每年的具体情况有所调整，比如调休等。2023年的国庆节假期安排尚未公布，但可以预期会遵循类似的模式。\"},\"finish_reason\":\"stop\"}],\"usage\":{\"prompt_tokens\":11,\"completion_tokens\":72,\"total_tokens\":83}}"
	// obj := []*KEP_WF.OutputParam{
	// 	{
	// 		Title: "model",
	// 	},
	// 	{
	// 		Title: "choices",
	// 		Type:  KEP_WF.TypeEnum_ARRAY_OBJECT,
	// 		Properties: []*KEP_WF.OutputParam{
	// 			{
	// 				Title: "finish_reason",
	// 			},
	// 			{
	// 				Title: "message",
	// 				Type:  KEP_WF.TypeEnum_OBJECT,
	// 				Properties: []*KEP_WF.OutputParam{
	// 					{
	// 						Title: "role",
	// 					},
	// 					{
	// 						Title: "content",
	// 					},
	// 				},
	// 			},
	// 		},
	// 	},
	// }
	// m := make(map[string]any)
	// _ = json.Unmarshal([]byte(sss), &m)
	// fmt.Println("ddddd|", util.ToJsonString(parseOutputValue(m, obj)))

	type args struct {
		ctx             context.Context
		session         *entity.Session
		nodeTask        *entity.NodeTask
		nodeResultQueue chan entity.NodeResult
	}
	type tCase struct {
		args       args
		wantStatus entity.NodeStatus
		wantOutput string
	}
	runTest := func(t *testing.T, tt tCase) {
		executeToolNode(tt.args.ctx, tt.args.session, tt.args.nodeTask, tt.args.nodeResultQueue)
		close(tt.args.nodeResultQueue)
		// 断言，只取最后一个结果
		var res entity.NodeResult
		for res = range tt.args.nodeResultQueue {
			fmt.Printf("%+v\n", res)
		}
		assert.Equal(t, tt.wantStatus, res.Status)
		assert.Equal(t, tt.wantOutput, res.Output)
	}

	t.Run("success_api", func(t *testing.T) {
		session := &entity.Session{}
		p := gomonkey.ApplyFunc(config.GetMainConfig, func() config.MainConfig {
			return config.MainConfig{
				API: config.APIConfig{
					Ports: []string{"443"},
				},
			}
		})
		defer p.Reset()

		testCase := tCase{
			args: args{
				ctx:     context.Background(),
				session: session,
				nodeTask: &entity.NodeTask{
					Node: &KEP_WF.WorkflowNode{
						NodeID:   "tool_1",
						NodeName: "tool_1",
						NodeType: KEP_WF.NodeType_TOOL,
						NodeData: &KEP_WF.WorkflowNode_ToolNodeData{
							ToolNodeData: &KEP_WF.ToolNodeData{
								API: &KEP_WF.ToolNodeData_APIInfo{
									URL:           "https://raw.githubusercontent.com/konodd/test/refs/heads/master/test.json",
									Method:        "GET",
									AuthType:      KEP_WF.ToolNodeData_API_KEY,
									KeyLocation:   KEP_WF.ToolNodeData_HEADER,
									KeyParamName:  "name",
									KeyParamValue: "dd",
								},
								Header: []*KEP_WF.ToolNodeData_RequestParam{},
								Query: []*KEP_WF.ToolNodeData_RequestParam{
									{
										ParamName: "config",
										ParamType: KEP_WF.TypeEnum_OBJECT,
										SubParams: []*KEP_WF.ToolNodeData_RequestParam{
											{
												ParamName: "name",
												ParamType: KEP_WF.TypeEnum_STRING,
												Input: &KEP_WF.Input{
													InputType: KEP_WF.InputSourceEnum_USER_INPUT,
													Source: &KEP_WF.Input_UserInputValue{
														UserInputValue: &KEP_WF.UserInputContent{
															Values: []string{"dd"},
														},
													},
												},
											},
											{
												ParamName: "age",
												ParamType: KEP_WF.TypeEnum_INT,
												Input: &KEP_WF.Input{
													InputType: KEP_WF.InputSourceEnum_USER_INPUT,
													Source: &KEP_WF.Input_UserInputValue{
														UserInputValue: &KEP_WF.UserInputContent{
															Values: []string{"18"},
														},
													},
												},
											},
											{
												ParamName: "grade",
												ParamType: KEP_WF.TypeEnum_FLOAT,
												Input: &KEP_WF.Input{
													InputType: KEP_WF.InputSourceEnum_USER_INPUT,
													Source: &KEP_WF.Input_UserInputValue{
														UserInputValue: &KEP_WF.UserInputContent{
															Values: []string{"98.65"},
														},
													},
												},
											},
											{
												ParamName: "male",
												ParamType: KEP_WF.TypeEnum_BOOL,
												Input: &KEP_WF.Input{
													InputType: KEP_WF.InputSourceEnum_USER_INPUT,
													Source: &KEP_WF.Input_UserInputValue{
														UserInputValue: &KEP_WF.UserInputContent{
															Values: []string{"true"},
														},
													},
												},
											},
											{
												ParamName: "array of object",
												ParamType: KEP_WF.TypeEnum_ARRAY_OBJECT,
												SubParams: []*KEP_WF.ToolNodeData_RequestParam{
													{
														ParamName: "",
														ParamType: KEP_WF.TypeEnum_OBJECT,
														SubParams: []*KEP_WF.ToolNodeData_RequestParam{
															{
																ParamName: "dd",
																ParamType: KEP_WF.TypeEnum_STRING,
																Input: &KEP_WF.Input{
																	InputType: KEP_WF.InputSourceEnum_USER_INPUT,
																	Source: &KEP_WF.Input_UserInputValue{
																		UserInputValue: &KEP_WF.UserInputContent{
																			Values: []string{"8年级"},
																		},
																	},
																},
															},
														},
													},
												},
											},
											{
												ParamName: "array of string",
												ParamType: KEP_WF.TypeEnum_ARRAY_STRING,
												SubParams: []*KEP_WF.ToolNodeData_RequestParam{
													{
														ParamName: "",
														ParamType: KEP_WF.TypeEnum_STRING,
														Input: &KEP_WF.Input{
															InputType: KEP_WF.InputSourceEnum_USER_INPUT,
															Source: &KEP_WF.Input_UserInputValue{
																UserInputValue: &KEP_WF.UserInputContent{
																	Values: []string{"8年级"},
																},
															},
														},
													},
												},
											},
											{
												ParamName: "array of int",
												ParamType: KEP_WF.TypeEnum_ARRAY_INT,
												SubParams: []*KEP_WF.ToolNodeData_RequestParam{
													{
														ParamName: "",
														ParamType: KEP_WF.TypeEnum_INT,
														Input: &KEP_WF.Input{
															InputType: KEP_WF.InputSourceEnum_USER_INPUT,
															Source: &KEP_WF.Input_UserInputValue{
																UserInputValue: &KEP_WF.UserInputContent{
																	Values: []string{"1"},
																},
															},
														},
													},
												},
											},
											{
												ParamName: "array of float",
												ParamType: KEP_WF.TypeEnum_ARRAY_FLOAT,
												SubParams: []*KEP_WF.ToolNodeData_RequestParam{
													{
														ParamName: "",
														ParamType: KEP_WF.TypeEnum_FLOAT,
														Input: &KEP_WF.Input{
															InputType: KEP_WF.InputSourceEnum_USER_INPUT,
															Source: &KEP_WF.Input_UserInputValue{
																UserInputValue: &KEP_WF.UserInputContent{
																	Values: []string{"1.1"},
																},
															},
														},
													},
												},
											},
											{
												ParamName: "array of bool",
												ParamType: KEP_WF.TypeEnum_ARRAY_BOOL,
												SubParams: []*KEP_WF.ToolNodeData_RequestParam{
													{
														ParamName: "",
														ParamType: KEP_WF.TypeEnum_BOOL,
														Input: &KEP_WF.Input{
															InputType: KEP_WF.InputSourceEnum_USER_INPUT,
															Source: &KEP_WF.Input_UserInputValue{
																UserInputValue: &KEP_WF.UserInputContent{
																	Values: []string{"true"},
																},
															},
														},
													},
												},
											},
										},
									},
								},
							},
						},
						NextNodeIDs: []string{"answer_1"},
						Outputs: []*KEP_WF.OutputParam{
							{
								Title: "Output",
								Type:  KEP_WF.TypeEnum_OBJECT,
								Properties: []*KEP_WF.OutputParam{
									{
										Title: "name",
										Type:  KEP_WF.TypeEnum_STRING,
									},
									{
										Title: "age",
										Type:  KEP_WF.TypeEnum_INT,
									},
									{
										Title: "extra",
										Type:  KEP_WF.TypeEnum_OBJECT,
										Properties: []*KEP_WF.OutputParam{
											{
												Title: "sex",
												Type:  KEP_WF.TypeEnum_STRING,
											},
											{
												Title: "school",
												Type:  KEP_WF.TypeEnum_ARRAY_STRING,
											},
										},
									},
								},
							},
						},
					},
				},
				nodeResultQueue: make(chan entity.NodeResult, 100),
			},
			wantStatus: entity.NodeStatusSuccess,
			wantOutput: `{"age":18,"extra":{"school":["a1","a2"],"sex":"male"},"name":"dd"}`,
		}
		runTest(t, testCase)
	})
}

func Test_deepMergeOutput(t *testing.T) {
	t.Run("When both inputs are nil", func(t *testing.T) {
		result := deepMergeOutput(nil, nil, nil)
		assert.Nil(t, result)
	})

	t.Run("When lastOutput is nil", func(t *testing.T) {
		newOutput := map[string]any{"key": "value"}
		result := deepMergeOutput(nil, newOutput, nil)
		assert.Equal(t, newOutput, result)
	})

	t.Run("When newOutput is nil", func(t *testing.T) {
		lastOutput := map[string]any{"key": "value"}
		result := deepMergeOutput(lastOutput, nil, nil)
		assert.Equal(t, lastOutput, result)
	})

	t.Run("Basic merge with no overlapping keys", func(t *testing.T) {
		lastOutput := map[string]any{"key1": "value1"}
		newOutput := map[string]any{"key2": "value2"}
		result := deepMergeOutput(lastOutput, newOutput, nil)
		expected := map[string]any{
			"key1": "value1",
			"key2": "value2",
		}
		assert.Equal(t, expected, result)
	})

	t.Run("Merge with overlapping keys, default behavior (COVER)", func(t *testing.T) {
		lastOutput := map[string]any{"key": "old_value"}
		newOutput := map[string]any{"key": "new_value"}
		result := deepMergeOutput(lastOutput, newOutput, []*KEP_WF.OutputParam{
			{
				Title:          "key",
				AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_COVER,
			},
		})
		expected := map[string]any{"key": "new_value"}
		assert.Equal(t, expected, result)
	})

	t.Run("Merge with string concatenation (INCREMENT)", func(t *testing.T) {
		lastOutput := map[string]any{"key": "Hello "}
		newOutput := map[string]any{"key": "World"}
		result := deepMergeOutput(lastOutput, newOutput, []*KEP_WF.OutputParam{
			{
				Title:          "key",
				AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_INCREMENT,
			},
		})
		expected := map[string]any{"key": "Hello World"}
		assert.Equal(t, expected, result)
	})

	t.Run("Merge nested maps", func(t *testing.T) {
		lastOutput := map[string]any{
			"nested": map[string]any{
				"key1": "value1",
				"key2": "old_value",
			},
		}
		newOutput := map[string]any{
			"nested": map[string]any{
				"key2": "new_value",
				"key3": "value3",
			},
		}
		result := deepMergeOutput(lastOutput, newOutput, []*KEP_WF.OutputParam{
			{
				Title:          "nested",
				AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_COVER,
				Properties: []*KEP_WF.OutputParam{
					{
						Title:          "key2",
						AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_COVER,
					},
				},
			},
		})
		expected := map[string]any{
			"nested": map[string]any{
				"key1": "value1",
				"key2": "new_value",
				"key3": "value3",
			},
		}
		assert.Equal(t, expected, result)
	})

	t.Run("Test OpenAI stream response format", func(t *testing.T) {
		lastOutput := map[string]any{
			"model":        "gpt-3.5-turbo-0125",
			"service_tier": "drop",
			"choices": []interface{}{
				map[string]any{
					"index": float64(0),
					"delta": map[string]any{
						"content": "快",
					},
				},
			},
		}
		newOutput := map[string]any{
			"model": "gpt-3.5-turbo-new",
			"choices": []interface{}{
				map[string]any{
					"index": float64(0),
					"delta": map[string]any{
						"content": "乐",
					},
				},
			},
		}

		// 创建OutputParam来模拟OpenAI响应的结构
		outputParams := []*KEP_WF.OutputParam{
			{
				Title:          "model",
				AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_COVER,
			},
			{
				Title:          "service_tier",
				AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_COVER,
			},
			{
				Title:          "choices",
				Type:           KEP_WF.TypeEnum_ARRAY_OBJECT,
				AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_COVER,
				Properties: []*KEP_WF.OutputParam{
					{
						Title:          "delta",
						AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_COVER,
						Properties: []*KEP_WF.OutputParam{
							{
								Title:          "content",
								AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_INCREMENT,
							},
						},
					},
				},
			},
		}

		result := deepMergeOutput(lastOutput, newOutput, outputParams)

		// 验证顶层属性是否正确合并
		assert.Equal(t, "gpt-3.5-turbo-new", result["model"])
		assert.Equal(t, "drop", result["service_tier"])

		// 验证choices数组是否正确合并
		choices, ok := result["choices"].([]interface{})
		assert.True(t, ok)
		assert.Equal(t, 1, len(choices))

		// 验证delta内容是否正确合并
		choice0, ok := choices[0].(map[string]any)
		assert.True(t, ok)
		delta, ok := choice0["delta"].(map[string]any)
		assert.True(t, ok)
		content, ok := delta["content"].(string)
		assert.True(t, ok)
		assert.Equal(t, "快乐", content)
	})

	t.Run("array obj", func(t *testing.T) {
		lastOutput := map[string]any{
			"data": map[string]any{
				"objs": []map[string]any{
					map[string]any{
						"text": "快",
					},
				},
			},
		}
		newOutput := map[string]any{
			"data": map[string]any{
				"objs": []map[string]any{
					map[string]any{
						"text": "乐",
					},
				},
			},
		}

		// 创建OutputParam来模拟OpenAI响应的结构
		outputParams := []*KEP_WF.OutputParam{

			{
				Title:          "data",
				Type:           KEP_WF.TypeEnum_OBJECT,
				AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_COVER,
				Properties: []*KEP_WF.OutputParam{
					{
						Title:          "objs",
						Type:           KEP_WF.TypeEnum_ARRAY_OBJECT,
						AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_COVER,
						Properties: []*KEP_WF.OutputParam{
							{
								Title:          "text",
								AnalysisMethod: KEP_WF.AnalysisMethodTypeEnum_INCREMENT,
							},
						},
					},
				},
			},
		}

		result := deepMergeOutput(lastOutput, newOutput, outputParams)

		// 验证顶层属性是否正确合并
		t.Log(result)
		data, ok := result["data"].(map[string]any)
		assert.True(t, ok)
		objs, ok := data["objs"].([]map[string]any)
		assert.True(t, ok)
		obj := objs[0]
		assert.Equal(t, "快乐", obj["text"])
	})
}

func Test_processSSEEvent(t *testing.T) {
	t.Run("基本SSE事件解析", func(t *testing.T) {
		input := `event: notification
data: {"text": "alert 2", "content": "今天"}`

		result := processSSEEvent(input)

		assert.Equal(t, "notification", result["event"])
		dataMap, ok := result["data"].(map[string]any)
		assert.True(t, ok)
		assert.Equal(t, "alert 2", dataMap["text"])
		assert.Equal(t, "今天", dataMap["content"])
	})

	t.Run("空SSE事件", func(t *testing.T) {
		result := processSSEEvent("")
		assert.Empty(t, result)
	})

	t.Run("只有空行的SSE事件", func(t *testing.T) {
		input := "\n\n\n"
		result := processSSEEvent(input)
		assert.Empty(t, result)
	})

	t.Run("没有冒号的行", func(t *testing.T) {
		input := "event notification\ndata"
		result := processSSEEvent(input)
		assert.Empty(t, result)
	})

	t.Run("非JSON数据字段", func(t *testing.T) {
		input := "event: notification\ndata: 这是纯文本数据"
		result := processSSEEvent(input)

		assert.Equal(t, "notification", result["event"])
		assert.Equal(t, "这是纯文本数据", result["data"])
	})

	t.Run("多个字段", func(t *testing.T) {
		input := `event: notification
data: {"text": "alert 2", "content": "今天"}
id: 12345
retry: 3000`

		result := processSSEEvent(input)

		assert.Equal(t, "notification", result["event"])
		dataMap, ok := result["data"].(map[string]any)
		assert.True(t, ok)
		assert.Equal(t, "alert 2", dataMap["text"])
		assert.Equal(t, "今天", dataMap["content"])
		assert.Equal(t, float64(12345), result["id"])
		assert.Equal(t, float64(3000), result["retry"])
	})

	t.Run("数组类型JSON数据", func(t *testing.T) {
		input := `event: list
data: [1, 2, 3, "four"]`

		result := processSSEEvent(input)

		assert.Equal(t, "list", result["event"])
		dataArray, ok := result["data"].([]any)
		assert.True(t, ok)
		assert.Equal(t, float64(1), dataArray[0])
		assert.Equal(t, float64(2), dataArray[1])
		assert.Equal(t, float64(3), dataArray[2])
		assert.Equal(t, "four", dataArray[3])
	})

	t.Run("嵌套JSON数据", func(t *testing.T) {
		input := `event: complex
data: {"user": {"name": "张三", "age": 30}, "items": [{"id": 1}, {"id": 2}]}`

		result := processSSEEvent(input)

		assert.Equal(t, "complex", result["event"])
		dataMap, ok := result["data"].(map[string]any)
		assert.True(t, ok)

		user, ok := dataMap["user"].(map[string]any)
		assert.True(t, ok)
		assert.Equal(t, "张三", user["name"])
		assert.Equal(t, float64(30), user["age"])

		items, ok := dataMap["items"].([]any)
		assert.True(t, ok)
		assert.Equal(t, float64(1), items[0].(map[string]any)["id"])
		assert.Equal(t, float64(2), items[1].(map[string]any)["id"])
	})
}

func Test_streamAPIResponse(t *testing.T) {
	// 设置测试环境
	ctx := context.Background()

	// 创建一个模拟的 ToolNodeData
	nodeData := &KEP_WF.ToolNodeData{}

	t.Run("Mock HTTP client for successful response", func(t *testing.T) {
		// 使用 gomonkey 模拟 HTTP 请求
		patches := gomonkey.ApplyFunc(createHTTPRequest, func(ctx context.Context, node *KEP_WF.ToolNodeData,
			headerValues, queryValues, bodyValues map[string]any) (*http.Request, error) {
			return http.NewRequest("GET", "https://example.com/stream", nil)
		})
		defer patches.Reset()

		// 模拟 secapi.NewSafeClient 和 client.Do
		mockClient := &mockHTTPClient{
			doFunc: func(req *http.Request) (*http.Response, error) {
				// 创建一个模拟的 SSE 响应
				respBody := `data: {"message":"Hello"}

data: {"message":"World"}

event: chat
data: {"content":"Test"}

`
				return &http.Response{
					StatusCode: 200,
					Body:       io.NopCloser(strings.NewReader(respBody)),
				}, nil
			},
		}

		patches2 := gomonkey.ApplyFunc(secapi.NewSafeClient, func(...secapi.SecOptions) *http.Client {
			return &http.Client{
				Transport: mockClient,
			}
		})
		defer patches2.Reset()

		// 调用函数
		chunks, errCh, err := streamAPIResponse(ctx, nodeData, make(map[string]any), make(map[string]any), make(map[string]any), 0)

		// 验证结果
		require.NoError(t, err)
		assert.NotNil(t, chunks)
		assert.NotNil(t, errCh)

		// 读取第一个原始 SSE 事件
		chunk1 := <-chunks
		assert.Equal(t, "data: {\"message\":\"Hello\"}\n\n", chunk1)

		// 使用 processSSEEvent 解析第一个事件
		parsedEvent1 := processSSEEvent(chunk1)
		dataMap1, ok := parsedEvent1["data"].(map[string]any)
		assert.True(t, ok)
		assert.Equal(t, "Hello", dataMap1["message"])

		// 读取第二个原始 SSE 事件
		chunk2 := <-chunks
		assert.Equal(t, "data: {\"message\":\"World\"}\n\n", chunk2)

		// 使用 processSSEEvent 解析第二个事件
		parsedEvent2 := processSSEEvent(chunk2)
		dataMap2, ok := parsedEvent2["data"].(map[string]any)
		assert.True(t, ok)
		assert.Equal(t, "World", dataMap2["message"])

		// 读取第三个原始 SSE 事件
		chunk3 := <-chunks
		assert.Equal(t, "event: chat\ndata: {\"content\":\"Test\"}\n\n", chunk3)

		// 使用 processSSEEvent 解析第三个事件
		parsedEvent3 := processSSEEvent(chunk3)
		assert.Equal(t, "chat", parsedEvent3["event"])
		dataMap3, ok := parsedEvent3["data"].(map[string]any)
		assert.True(t, ok)
		assert.Equal(t, "Test", dataMap3["content"])

		// 验证通道关闭
		_, ok = <-chunks
		assert.False(t, ok)

		// 验证没有错误
		select {
		case err := <-errCh:
			assert.Nil(t, err)
		default:
			// 没有错误，通过测试
		}
	})

	t.Run("Test error handling", func(t *testing.T) {
		// 模拟 createHTTPRequest 失败
		patches := gomonkey.ApplyFunc(createHTTPRequest, func(ctx context.Context, node *KEP_WF.ToolNodeData,
			headerValues, queryValues, bodyValues map[string]any) (*http.Request, error) {
			return nil, fmt.Errorf("request creation failed")
		})
		defer patches.Reset()

		// 调用函数
		_, _, err := streamAPIResponse(ctx, nodeData, make(map[string]any), make(map[string]any), make(map[string]any), 0)

		// 验证结果
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to create HTTP request")
	})

	t.Run("Test HTTP error response", func(t *testing.T) {
		// 模拟 createHTTPRequest 成功
		patches := gomonkey.ApplyFunc(createHTTPRequest, func(ctx context.Context, node *KEP_WF.ToolNodeData,
			headerValues, queryValues, bodyValues map[string]any) (*http.Request, error) {
			return http.NewRequest("GET", "https://example.com/stream", nil)
		})
		defer patches.Reset()

		// 模拟 HTTP 客户端返回错误状态码
		mockClient := &mockHTTPClient{
			doFunc: func(req *http.Request) (*http.Response, error) {
				return &http.Response{
					StatusCode: 404,
					Body:       io.NopCloser(strings.NewReader("Not Found")),
				}, nil
			},
		}

		patches2 := gomonkey.ApplyFunc(secapi.NewSafeClient, func(...secapi.SecOptions) *http.Client {
			return &http.Client{
				Transport: mockClient,
			}
		})
		defer patches2.Reset()

		// 调用函数
		_, _, err := streamAPIResponse(ctx, nodeData, make(map[string]any), make(map[string]any), make(map[string]any), 0)

		// 验证结果
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "HTTP status 404")
	})
}

// mockHTTPClient 是一个模拟的 HTTP 客户端
type mockHTTPClient struct {
	doFunc func(req *http.Request) (*http.Response, error)
}

func (m *mockHTTPClient) RoundTrip(req *http.Request) (*http.Response, error) {
	return m.doFunc(req)
}
