package workflow

import (
	"context"
	"fmt"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity/tconst"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// DebugNotDialogNode 调试非对话节点
func DebugNotDialogNode(ctx context.Context, req *KEP_WF_DM.DebugWorkflowNodeRequest, node *KEP_WF.WorkflowNode) (
	*KEP_WF_DM.DebugWorkflowNodeReply, error) {

	// 构造session
	session := &entity.Session{}
	session.InitVar()
	session.AppID = req.AppID
	session.DebuggingNode = &entity.DebugNode{
		NodeID:    node.NodeID,
		Inputs:    req.Inputs,
		ToolInput: req.ToolInput,
	}
	session.InitEmptyWorkflowRun()
	// 计费需要的信息，需要从admin处获取
	sessionScene := dao.SceneSandbox
	appInfo, err := dao.Default().GetAppInfo(ctx, util.ConvertStringToUint64(req.AppID), uint32(sessionScene))
	if err != nil {
		LogWorkflow(ctx).Errorf("get app info failed, error: %v", err)
		return nil, fmt.Errorf("get app info failed, error: %v", err)
	}
	session.AppType = appInfo.AppType
	uin, sid, err := dao.Default().GetCorpInfo(ctx, appInfo.CorpId)
	if err != nil {
		LogWorkflow(ctx).Errorf("get corp info failed, error: %v", err)
		return nil, fmt.Errorf("get corp info failed, error: %v", err)
	}
	session.Uin = uin
	session.SID = sid
	session.FinanceSubBizType = tconst.FinanceSubBizTypeKnowledgeQADialogTest

	executor := &LocalExecutor{
		ctx:            ctx,
		session:        session,
		nodeResulQueue: make(chan entity.NodeResult, 1),
	}

	nodeTask := entity.NodeTask{
		BelongNodeID: "",
		Node:         node,
	}
	i, retry := 0, 0
	if node.GetExceptionHandling().GetSwitch() == KEP_WF.ExceptionHandling_ON {
		retry = int(node.GetExceptionHandling().MaxRetries)
	}

RETRY:
	go executor.doNodeTask(&nodeTask)
	for {
		select {
		case nodeResult := <-executor.GetNodeResulQueue():
			LogWorkflow(ctx).Infof("dealNodeResult, nodeResult: %v", util.ToJsonString(nodeResult))
			if !nodeResult.Status.IsFinished() {
				continue
			}
			if nodeResult.Status == entity.NodeStatusFailed {
				if i < retry {
					i++
					time.Sleep(time.Duration(node.GetExceptionHandling().GetRetryInterval()) * time.Second)
					goto RETRY
				} else if node.GetExceptionHandling().GetSwitch() == KEP_WF.ExceptionHandling_ON {
					nodeResult.Status = entity.NodeStatusSuccess
					nodeResult.Output = node.ExceptionHandling.AbnormalOutputResult
				}
			}
			return &KEP_WF_DM.DebugWorkflowNodeReply{
				Code:    0,
				Message: "",
				NodeData: &KEP_WF_DM.RunNodeInfo{
					NodeID:           node.NodeID,
					NodeType:         node.NodeType,
					NodeName:         node.NodeName,
					Status:           entity.GetApiNodeStatus(nodeResult.Status),
					Input:            nodeResult.Input,
					Output:           nodeResult.Output,
					TaskOutput:       nodeResult.TaskOutput,
					Log:              nodeResult.Log,
					FailMessage:      nodeResult.FailMessage,
					CostMilliSeconds: uint32(nodeResult.CostMilliSeconds),
					Reply:            "",
					BelongNodeID:     "",
					IsCurrent:        false,
					FailCode:         nodeResult.ErrorCode,
					StatisticInfos:   nodeResult.StatisticInfo,
				},
			}, nil
		case <-time.After(time.Minute * 10):
			return nil, fmt.Errorf("DebugNotDialogNode timeout")
		}
	}
}
