package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/dao"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
)

func executeCodeNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	LogWorkflow(ctx).Infof("executeCodeNode start, nodeID: %v", node.NodeID)
	start := time.Now()
	// 节点状态初始化
	nodeResult := entity.NodeResult{
		BelongNodeID: nodeTask.BelongNodeID,
		NodeID:       node.GetNodeID(),
		Status:       entity.NodeStatusRunning,
	}
	nodeResultQueue <- nodeResult

	// err需要使用entity中的NodeErr，避免错误码、错误信息为空
	log, codeResult, input, output, err := runCode(ctx, session, nodeTask.BelongNodeID, node)
	if err != nil {
		nodeResult.Status = entity.NodeStatusFailed
		nodeResult.FailMessage = entity.GetNodeErrMsg(err)
		nodeResult.ErrorCode = entity.GetNodeErrCode(err)
	} else {
		nodeResult.Input = util.ToJsonString(input)
		nodeResult.Status = entity.NodeStatusSuccess
		nodeResult.TaskOutput = codeResult
		nodeResult.Output = util.ToJsonString(output)
		nodeResult.Log = log
	}
	nodeResult.CostMilliSeconds = time.Since(start).Milliseconds()
	LogWorkflow(ctx).Infof("executeCodeNode done, nodeID: %v,cost:%d", node.NodeID,
		nodeResult.CostMilliSeconds)
	nodeResultQueue <- nodeResult

}

func runCode(ctx context.Context, session *entity.Session, belongID string, node *KEP_WF.WorkflowNode) (
	string, string, map[string]any, map[string]any, error) {
	codeData := node.GetCodeExecutorNodeData()
	if codeData == nil {
		LogWorkflow(ctx).Errorf("trans code data failed:%v", node.GetNodeData())
		return "", "", nil, nil, entity.ErrMissingParam
	}
	input, err := parseCodeParams(session, belongID, node.GetInputs())
	if err != nil {
		LogWorkflow(ctx).Errorf("parseCodeParams failed:%v", err)
		return "", "", nil, nil, entity.ErrInvalidParam
	}
	codeSessionID := getCodeSessionID(session.AppID, config.GetMainConfig().RunCode.MaxAppParallel)
	daoClient := dao.Default()
	if session.IsAsync {
		daoClient = dao.InAsyncDao()
	}
	log, result, err := daoClient.RunCode(ctx, codeSessionID, codeData.GetCode(), util.ToJsonString(input))
	if err != nil {
		LogWorkflow(ctx).Warnf("RunCode failed: %v", err)
		return "", "", input, nil, entity.WrapNodeErr(entity.ErrExecuteCodeFailed, err.Error())
	}
	rspValue := make(map[string]any)
	_ = json.Unmarshal([]byte(result), &rspValue)
	outputs := node.GetOutputs()
	// 过滤掉第一层的Output
	if len(outputs) == 1 && outputs[0].GetTitle() == entity.OutputField &&
		outputs[0].GetType() == KEP_WF.TypeEnum_OBJECT {
		outputs = outputs[0].GetProperties()
	}
	return log, result, input, util.ParseOutputValue(rspValue, outputs), nil
}

func getCodeSessionID(appID string, maxNum int) string {
	// 返回appID+的随机数字
	if maxNum <= 0 {
		maxNum = 1
	}
	codeSessionID := fmt.Sprintf("%s_%d", appID, rand.Intn(maxNum))
	return codeSessionID
}

// parseCodeParams 代码节点，输入参数的类型跟随引用的类型，直接跟传入的类型一致即可,  自定义类型默认为string
func parseCodeParams(session *entity.Session, belongID string, params []*KEP_WF.InputParam) (map[string]any, error) {
	res := make(map[string]any)
	for _, param := range params {
		if param.GetInput().GetInputType() == KEP_WF.InputSourceEnum_USER_INPUT {
			if len(param.GetInput().GetUserInputValue().GetValues()) > 0 {
				res[param.GetName()] = param.GetInput().GetUserInputValue().GetValues()[0]
			}
		} else {
			if session.IsDebuggingNode() && belongID == "" {
				res[param.GetName()] = session.DebuggingNode.Inputs[param.GetName()]
			} else {
				res[param.GetName()] = getInputValue(session, belongID, param.GetInput(), nil)
			}
		}
	}
	return res, nil
}
