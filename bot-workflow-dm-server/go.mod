// KEP.bot-workflow-dm-server
//
// @(#)go.mod  December 07, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.

module git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server

go 1.23.0

toolchain go1.23.8

// replace git.woa.com/raven/pb-stub => /Users/<USER>/gosrc0/raven/pb-stub
//replace git.woa.com/dialogue-platform/lke_proto => /Users/<USER>/go/src/git.woa.com/qbot/lke_proto
//replace git.woa.com/dialogue-platform/lke_proto => /Users/<USER>/go/src/git.woa.com/
//replace git.woa.com/dialogue-platform/go-comm => /Users/<USER>/go/src/git.woa.com/qbot/go-comm

// 私有化上报使用, 不了解请勿动, 感谢~
replace git.code.oa.com/trpc-go/trpc-metrics-prometheus => git.woa.com/dialogue-platform/tools/trpc-metrics-prometheus v0.1.13

// 服务目前由于依赖common，引入redigo的依赖
replace (
	git.code.oa.com/trpc-go/trpc-database/redis => git.code.oa.com/trpc-go/trpc-database/redis v0.1.15
	github.com/gomodule/redigo => github.com/gomodule/redigo v1.8.5
)

require (
	git.code.oa.com/trpc-go/trpc-database/goredis v0.3.1
	git.code.oa.com/trpc-go/trpc-database/gorm v0.2.9
	git.code.oa.com/trpc-go/trpc-database/redis v0.2.3
	git.code.oa.com/trpc-go/trpc-go v0.19.3
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.5.12
	git.code.oa.com/trpc-go/trpc-selector-dsn v0.2.1
	git.woa.com/dialogue-platform/common/v3 v3.0.5
	git.woa.com/dialogue-platform/go-comm v0.2.28-0.20250607064954-cdfede03f74d
	git.woa.com/dialogue-platform/lke_proto v1.0.4-woa.0.20250607112706-5e12f9d52243
	git.woa.com/dialogue-platform/proto v0.1.82-0.20250205113345-1445f2d72689
	git.woa.com/galileo/trpc-go-galileo v0.19.1-rc.0
	git.woa.com/ivy/protobuf/trpc-go/qbot/finance/finance v0.0.0-20250513081647-38406c434b23
	git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat v0.0.0-20250528090035-6b6a31c9b7c5
	git.woa.com/sec-api/go/scurl v0.3.0
	git.woa.com/sec-api/go/trpc_scurl v0.1.4
	git.woa.com/trpc-go/mcp-go/client v0.0.8-0.20250519074744-46a1c1de91a0
	git.woa.com/trpc-go/mcp-go/mcp v0.0.3
	git.woa.com/trpc-go/tnet v0.1.0
	github.com/IBM/sarama v1.45.1
	github.com/PaesslerAG/jsonpath v0.1.1
	github.com/agiledragon/gomonkey/v2 v2.12.0
	github.com/alicebob/miniredis/v2 v2.23.1
	github.com/apache/rocketmq-client-go/v2 v2.1.2
	github.com/apache/rocketmq-clients/golang/v5 v5.1.2
	github.com/asaskevich/govalidator v0.0.0-20210307081110-f21760c49a8d
	github.com/go-redis/redis/v8 v8.11.5
	github.com/golang/mock v1.6.0
	github.com/google/uuid v1.6.0
	github.com/jinzhu/copier v0.4.0
	github.com/prashantv/gostub v1.1.0
	github.com/smartystreets/goconvey v1.7.2
	github.com/stretchr/testify v1.10.0
	github.com/tidwall/gjson v1.17.0
	go.opentelemetry.io/otel/trace v1.34.0
	google.golang.org/protobuf v1.36.5
	gopkg.in/yaml.v3 v3.0.1
	gorm.io/gorm v1.25.1
)

require (
	contrib.go.opencensus.io/exporter/ocagent v0.6.0 // indirect
	git.code.oa.com/trpc-go/trpc-utils v0.2.2 // indirect
	git.woa.com/trpc/trpc-robust/go-sdk v0.0.1 // indirect
	git.woa.com/trpc/trpc-robust/proto/pb/go/trpc-robust v0.0.0-20240820014626-************ // indirect
	github.com/ClickHouse/ch-go v0.50.0 // indirect
	github.com/ClickHouse/clickhouse-go/v2 v2.4.3 // indirect
	github.com/alicebob/gopher-json v0.0.0-20200520072559-a9ecdc9d1d3a // indirect
	github.com/census-instrumentation/opencensus-proto v0.4.1 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dchest/siphash v1.2.3 // indirect
	github.com/eapache/go-resiliency v1.7.0 // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3 // indirect
	github.com/eapache/queue v1.1.0 // indirect
	github.com/emirpasic/gods v1.12.0 // indirect
	github.com/getkin/kin-openapi v0.128.0 // indirect
	github.com/go-faster/city v1.0.1 // indirect
	github.com/go-faster/errors v0.6.1 // indirect
	github.com/go-openapi/jsonpointer v0.21.0 // indirect
	github.com/go-openapi/swag v0.23.0 // indirect
	github.com/go-sql-driver/mysql v1.6.0 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/hashicorp/go-uuid v1.0.3 // indirect
	github.com/invopop/yaml v0.3.1 // indirect
	github.com/jackc/chunkreader/v2 v2.0.1 // indirect
	github.com/jackc/pgconn v1.12.1 // indirect
	github.com/jackc/pgio v1.0.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgproto3/v2 v2.3.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20200714003250-2b9c44734f2b // indirect
	github.com/jackc/pgtype v1.11.0 // indirect
	github.com/jackc/pgx/v4 v4.16.1 // indirect
	github.com/jcmturner/aescts/v2 v2.0.0 // indirect
	github.com/jcmturner/dnsutils/v2 v2.0.0 // indirect
	github.com/jcmturner/gofork v1.7.6 // indirect
	github.com/jcmturner/gokrb5/v8 v8.4.4 // indirect
	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/paulmach/orb v0.7.1 // indirect
	github.com/perimeterx/marshmallow v1.1.5 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/r3labs/sse/v2 v2.10.0 // indirect
	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
	github.com/segmentio/asm v1.2.0 // indirect
	github.com/shopspring/decimal v1.3.1 // indirect
	github.com/sirupsen/logrus v1.9.0 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/valyala/fastrand v1.1.0 // indirect
	github.com/yuin/gopher-lua v0.0.0-20220504180219-658193537a64 // indirect
	go.opencensus.io v0.24.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetrichttp v1.28.0 // indirect
	go.opentelemetry.io/otel/sdk/metric v1.32.0 // indirect
	google.golang.org/api v0.215.0 // indirect
	gopkg.in/cenkalti/backoff.v1 v1.1.0 // indirect
	gorm.io/driver/mysql v1.3.4 // indirect
	gorm.io/driver/postgres v1.3.7 // indirect
	stathat.com/c/consistent v1.0.0 // indirect
)

require (
	git.code.oa.com/atta/attaapi-go v1.0.8 // indirect
	git.code.oa.com/devsec/protoc-gen-secv v0.3.4 // indirect
	git.code.oa.com/pcgmonitor/trpc_report_api_go v0.3.13 // indirect
	git.code.oa.com/polaris/polaris-go v0.12.12 // indirect
	git.code.oa.com/rainbow/golang-sdk v0.5.5 // indirect
	git.code.oa.com/rainbow/proto v1.94.0 // indirect
	git.code.oa.com/sec-api/go/checkurl v0.1.0 // indirect
	git.code.oa.com/trpc-go/trpc v0.1.2 // indirect
	git.code.oa.com/trpc-go/trpc-config-rainbow v0.2.7 // indirect
	git.code.oa.com/trpc-go/trpc-filter/recovery v0.1.5-0.20231229105235-90b66adeb80d // indirect
	git.code.oa.com/trpc-go/trpc-log-atta v0.2.0 // indirect
	git.code.oa.com/trpc-go/trpc-metrics-m007 v0.5.1 // indirect
	git.code.oa.com/trpc-go/trpc-metrics-prometheus v0.1.9
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.5.20 // indirect
	git.woa.com/baicaoyuan/apex/proto v0.0.1 // indirect
	git.woa.com/galileo/eco/go/sdk/base v0.19.1 // indirect
	git.woa.com/jce/jce v1.2.0 // indirect
	git.woa.com/polaris/polaris-go/v2 v2.5.12 // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.1.4 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.3 // indirect
	git.woa.com/trpc-go/go_reuseport v1.7.0 // indirect
	github.com/BurntSushi/toml v1.4.1-0.20240526193622-a339e1f7089c // indirect
	github.com/PaesslerAG/gval v1.0.0 // indirect
	github.com/alphadose/haxmap v1.4.1 // indirect
	github.com/andybalholm/brotli v1.1.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bytedance/sonic v1.12.6 // indirect
	github.com/bytedance/sonic/loader v0.2.1 // indirect
	github.com/cenkalti/backoff/v4 v4.3.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cloudwego/base64x v0.1.4 // indirect
	github.com/cloudwego/iasm v0.2.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/gin-gonic/gin v1.10.0 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-playground/form/v4 v4.2.1 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.20.0 // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/gomodule/redigo v2.0.0+incompatible // indirect
	github.com/google/flatbuffers v24.3.25+incompatible // indirect
	github.com/google/go-cmp v0.7.0 // indirect
	github.com/google/pprof v0.0.0-20241210010833-40e02aabc2ad // indirect
	github.com/gopherjs/gopherjs v0.0.0-20200217142428-fce0ec30dd00 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.22.0 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/jxskiss/base62 v1.1.0 // indirect
	github.com/kelindar/bitmap v1.5.2 // indirect
	github.com/kelindar/simd v1.1.2 // indirect
	github.com/klauspost/compress v1.17.11 // indirect
	github.com/klauspost/cpuid/v2 v2.2.7 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/lestrrat-go/strftime v1.0.6 // indirect
	github.com/magiconair/properties v1.8.6 // indirect
	github.com/mcuadros/go-defaults v1.2.0 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/nanmu42/limitio v1.0.0 // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/onsi/gomega v1.36.2 // indirect
	github.com/panjf2000/ants/v2 v2.10.0 // indirect
	github.com/pelletier/go-toml v1.9.5 // indirect
	github.com/pelletier/go-toml/v2 v2.2.3 // indirect
	github.com/pierrec/lz4/v4 v4.1.22 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/prometheus/client_golang v1.20.5 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.55.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/qianbin/directcache v0.9.7 // indirect
	github.com/remeh/sizedwaitgroup v1.0.0 // indirect
	github.com/robfig/cron v1.2.0 // indirect
	github.com/rogpeppe/go-internal v1.14.1 // indirect
	github.com/smartystreets/assertions v1.2.0 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/afero v1.12.0 // indirect
	github.com/spf13/cast v1.6.0 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
	github.com/spf13/viper v1.12.0 // indirect
	github.com/subosito/gotenv v1.4.1 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.0 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.52.0 // indirect
	github.com/yosida95/uritemplate/v3 v3.0.2 // indirect
	go.opentelemetry.io/auto/sdk v1.1.0 // indirect
	go.opentelemetry.io/otel v1.34.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.31.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.31.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracehttp v1.31.0 // indirect
	go.opentelemetry.io/otel/metric v1.34.0 // indirect
	go.opentelemetry.io/otel/sdk v1.32.0 // indirect
	go.opentelemetry.io/proto/otlp v1.3.1 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/automaxprocs v1.6.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/arch v0.8.0 // indirect
	golang.org/x/crypto v0.36.0 // indirect
	golang.org/x/exp v0.0.0-20240909161429-701f63a606c0 // indirect
	golang.org/x/net v0.37.0 // indirect
	golang.org/x/sync v0.12.0 // indirect
	golang.org/x/sys v0.31.0 // indirect
	golang.org/x/text v0.23.0 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20241209162323-e6fa225c2576 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250219182151-9fdb1cabc7b2 // indirect
	google.golang.org/grpc v1.70.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
)
