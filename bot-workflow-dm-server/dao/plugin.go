package dao

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util/config"
	"git.woa.com/dialogue-platform/go-comm/panicprinter"
	scurlcomm "git.woa.com/dialogue-platform/go-comm/rainbow-comm/scurl"
	plugin "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	pluginRun "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server"
	scurl "git.woa.com/sec-api/go/trpc_scurl"
	tmclient "git.woa.com/trpc-go/mcp-go/client"
	tmcp "git.woa.com/trpc-go/mcp-go/mcp"
)

// DescribeTool 查看工具详情
func (d *dao) DescribeTool(ctx context.Context, req *plugin.DescribeToolReq, opts ...client.Option) (
	rsp *plugin.DescribeToolRsp, err error) {
	step := trace.StartStep(ctx, trace.StepKeyDescribeTool, req)
	rsp, err = d.pluginCli.DescribeTool(ctx, req, opts...)
	defer func() { step.RecordEnd(rsp, err) }()
	if err != nil {
		LogAPI(ctx).Errorf("New chat stream client error: %+v", err)
		return nil, err
	}
	return rsp, nil
}

// RunTool 调用工具
func (d *dao) RunTool(ctx context.Context, req *pluginRun.RunToolReq, opts ...client.Option) (rsp *pluginRun.RunToolRsp,
	err error) {
	step := trace.StartStep(ctx, trace.StepKeyRunTool, req)
	if d.inAsync {
		opts = append(opts, client.WithTimeout(0))
	}
	rsp, err = d.pluginRunCli.RunTool(ctx, req, opts...)
	defer func() { step.RecordEnd(rsp, err) }()
	if err != nil {
		LogAPI(ctx).Errorf("RunTool failed, error: %+v", err)
		return nil, err
	}
	return rsp, nil
}

// StreamRunTool 工具运行（流式）
func (d *dao) StreamRunTool(ctx context.Context, req *pluginRun.StreamRunToolReq, opts ...client.Option) (
	chan *pluginRun.StreamRunToolRsp, error) {
	step := trace.StartStep(ctx, trace.StepKeyStreamRunTool, req)
	if d.inAsync {
		opts = append(opts, client.WithTimeout(0))
	}
	cli, err := d.pluginRunCli.StreamRunTool(ctx, opts...)
	if err != nil {
		LogAPI(ctx).Errorf("StreamRunTool failed, error: %+v", err)
		step.RecordEnd(nil, err)
		return nil, err
	}

	err = cli.Send(req)
	if err != nil {
		LogAPI(ctx).Errorf("Send StreamRunTool request error: %+v", err)
		step.RecordEnd(nil, err)
		return nil, err
	}

	ch := make(chan *pluginRun.StreamRunToolRsp)
	go func() {
		defer panicprinter.PrintPanic()
		var rsp *pluginRun.StreamRunToolRsp
		defer func() {
			step.RecordEnd(rsp, err)
			_ = cli.CloseSend()
			close(ch)
		}()
		for {
			select {
			case <-ctx.Done():
				return
			default:
				rsp, err = cli.Recv()
				if err != nil {
					if errors.Is(err, context.Canceled) || strings.Contains(errs.Msg(err), "context canceled") {
						return
					}
					LogAPI(ctx).Errorf("Read StreamRunToolRsp error: %+v, req: %+v", err, req)
					rsp = &pluginRun.StreamRunToolRsp{Code: -1, ErrMsg: err.Error(), IsFinal: true}
					ch <- rsp
					return
				}

				if rsp.Code != 0 || rsp.Rsp == nil {
					LogAPI(ctx).Warnf("Read StreamRunToolRsp failed, error: %+v", rsp)
					rsp.IsFinal = true
					ch <- rsp
					return
				}
				ch <- rsp

				if rsp.IsFinal {
					return
				}
			}
		}
	}()
	return ch, nil
}

// // initMCPClient 初始化MCP客户端
// func initMCPClient(ctx context.Context, mcpServer *plugin.MCPServerInfo, headers map[string]any) (
//	*mcpClient.SSEMCPClient, error) {
//	options := make([]mcpClient.ClientOption, 0)
//	reqHeaders := make(map[string]string)
//	for headerK, headerV := range mcpServer.GetHeaders() {
//		if headerK == "" {
//			continue
//		}
//		reqHeaders[headerK] = headerV
//	}
//	for headerK, headerV := range headers {
//		if headerK == "" {
//			continue
//		}
//		reqHeaders[headerK] = util.ToJsonString(headerV)
//	}
//	connectTimeout := time.Duration(mcpServer.Timeout) * time.Second
//	sseReadTimeout := time.Duration(mcpServer.SseReadTimeout) * time.Second
//	options = append(options,
//		mcpClient.WithHeaders(reqHeaders),
//		mcpClient.WithUnsafeDomain(config.GetMainConfig().MCP.WhiteURLs),
//		mcpClient.WithAllowPorts(config.GetMainConfig().MCP.Ports),
//		mcpClient.WithTimeout(connectTimeout),
//		mcpClient.WithSSEReadTimeout(sseReadTimeout),
//	)
//
//	mClient, err := mcpClient.NewSSEMCPClient(mcpServer.McpServerUrl, options...)
//	if err != nil {
//		LogAPI(ctx).Warnf("Failed to create client: %v", err)
//		return mClient, err
//	}
//	defer func() {
//		if err != nil {
//			_ = mClient.Close()
//		}
//	}()
//
//	// Start the client
//	if err := mClient.Start(ctx); err != nil {
//		LogAPI(ctx).Warnf("Failed to start client: %v", err)
//		return mClient, err
//	}
//
//	// Initialize
//	initRequest := mcp.InitializeRequest{}
//	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
//	initRequest.Params.ClientInfo = mcp.Implementation{
//		Name:    "workflow-dm-server",
//		Version: "1.0.0",
//	}
//
//	_, err = mClient.Initialize(ctx, initRequest)
//	if err != nil {
//		LogAPI(ctx).Warnf("Failed to initialize: %v", err)
//		return mClient, err
//	}
//	return mClient, nil
// }

// MCPCallTool 调用MCP工具
func (d *dao) MCPCallTool(ctx context.Context, name string, mcpServer *plugin.MCPServerInfo,
	headerValues map[string]any, bodyValues map[string]any, uin uint64) (result *tmcp.CallToolResult, err error) {
	if mcpServer == nil {
		LogAPI(ctx).Errorf("mcpServer is nil")
		return nil, errors.New("mcpServer is nil")
	}
	mcpReq := map[string]any{
		"ToolName":     name,
		"MCPServer":    mcpServer,
		"HeaderValues": headerValues,
		"BodyValues":   bodyValues,
	}
	step := trace.StartStep(ctx, trace.StepKeyMCPCallTool, mcpReq)
	defer func() { step.RecordEnd(result, err) }()

	uinStr := fmt.Sprintf("%d", uin)
	whiteURLs := append(config.GetMainConfig().MCP.WhiteURLs, scurlcomm.GetScurlDomainWLByUin(ctx, uinStr)...)
	whitePorts := append(config.GetMainConfig().MCP.Ports, scurlcomm.GetScurlPortWLByUin(ctx, uinStr)...)
	var secOptions []scurl.SecOptions
	if len(whiteURLs) > 0 {
		secOptions = append(secOptions, scurl.WithUnsafeDomain(whiteURLs))
	}
	if len(whitePorts) > 0 {
		secOptions = append(secOptions, scurl.WithAllowPorts(whitePorts))
	}
	var secTr = scurl.NewTrpcHTTPTransport(secOptions...)
	// 1. 创建并初始化 MCP 客户端
	httpHeaders := make(http.Header)
	for key, value := range headerValues {
		httpHeaders.Set(key, util.ToJsonString(value))
	}
	mcpClient, _, err := tmclient.NewSSEMCPTRPCClientWithOptions(
		ctx,
		"lke-mcp",
		tmcp.InitializeRequest{
			Params: tmcp.InitializeParams{
				ProtocolVersion: "1.0.0",
				ClientInfo: tmcp.Implementation{
					Name:    "lke-mcp-client",
					Version: "1.0.0",
				},
			},
		},
		tmclient.WithURL(mcpServer.McpServerUrl),
		tmclient.WithTRPCOptions(client.WithTransport(secTr)),
		tmclient.WithHTTPHeader(httpHeaders),
		tmclient.WithInitializeConnectionTimeout(time.Duration(mcpServer.Timeout)*time.Second),
		tmclient.WithSSEConnectionReadTimeout(time.Duration(mcpServer.SseReadTimeout)*time.Second),
	)
	if err != nil {
		LogAPI(ctx).Warnf("initMCPClient failed, error: %v", err)
		return nil, err
	}
	defer func() { _ = mcpClient.Close() }()

	request := tmcp.CallToolRequest{}
	request.Params.Name = name
	request.Params.Arguments = bodyValues
	result, err = mcpClient.CallTool(ctx, request)
	if err != nil {
		LogAPI(ctx).Warnf("mcp CallTool failed: %v", err)
		return nil, err
	}
	//
	// mClient, err := initMCPClient(ctx, mcpServer, headerValues)
	// if err != nil {
	//	LogAPI(ctx).Warnf("initMCPClient failed, error: %v", err)
	//	return nil, err
	// }
	// defer func() { _ = mClient.Close() }()
	// request := mcp.CallToolRequest{}
	// request.Params.Name = name
	// request.Params.Arguments = bodyValues
	//
	// result, err = mClient.CallTool(ctx, request)
	// if err != nil {
	//	LogAPI(ctx).Warnf("mcp CallTool failed: %v", err)
	//	return nil, err
	// }
	return result, nil
}
