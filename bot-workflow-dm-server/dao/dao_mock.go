// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package dao is a generated GoMock package.
package dao

import (
	context "context"
	reflect "reflect"

	client "git.code.oa.com/trpc-go/trpc-go/client"
	entity "git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	trace "git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/logic/trace"
	bot_admin_config_server "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_admin_config_server"
	bot_knowledge_config_server "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_knowledge_config_server"
	knowledge "git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	plugin_config_server "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_config_server"
	plugin_exec_server "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server"
	entity_extractor "git.woa.com/dialogue-platform/proto/pb-stub/entity-extractor"
	llm_manager_server "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	chat "git.woa.com/ivy/protobuf/trpc-go/qbot/qbot/chat"
	mcp "git.woa.com/trpc-go/mcp-go/mcp"
	gomock "github.com/golang/mock/gomock"
)

// MockDao is a mock of Dao interface.
type MockDao struct {
	ctrl     *gomock.Controller
	recorder *MockDaoMockRecorder
}

// MockDaoMockRecorder is the mock recorder for MockDao.
type MockDaoMockRecorder struct {
	mock *MockDao
}

// NewMockDao creates a new mock instance.
func NewMockDao(ctrl *gomock.Controller) *MockDao {
	mock := &MockDao{ctrl: ctrl}
	mock.recorder = &MockDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDao) EXPECT() *MockDaoMockRecorder {
	return m.recorder
}

// Chat mocks base method.
func (m *MockDao) Chat(ctx context.Context, session *entity.Session, req *llm_manager_server.Request, stepKey trace.StepKey) (chan *llm_manager_server.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Chat", ctx, session, req, stepKey)
	ret0, _ := ret[0].(chan *llm_manager_server.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Chat indicates an expected call of Chat.
func (mr *MockDaoMockRecorder) Chat(ctx, session, req, stepKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Chat", reflect.TypeOf((*MockDao)(nil).Chat), ctx, session, req, stepKey)
}

// DescribeTool mocks base method.
func (m *MockDao) DescribeTool(ctx context.Context, req *plugin_config_server.DescribeToolReq, opts ...client.Option) (*plugin_config_server.DescribeToolRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeTool", varargs...)
	ret0, _ := ret[0].(*plugin_config_server.DescribeToolRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTool indicates an expected call of DescribeTool.
func (mr *MockDaoMockRecorder) DescribeTool(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTool", reflect.TypeOf((*MockDao)(nil).DescribeTool), varargs...)
}

// Extractor mocks base method.
func (m *MockDao) Extractor(ctx context.Context, serverName string, req *entity_extractor.EntityExtractorReq) (*entity_extractor.EntityExtractorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Extractor", ctx, serverName, req)
	ret0, _ := ret[0].(*entity_extractor.EntityExtractorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Extractor indicates an expected call of Extractor.
func (mr *MockDaoMockRecorder) Extractor(ctx, serverName, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Extractor", reflect.TypeOf((*MockDao)(nil).Extractor), ctx, serverName, req)
}

// GetAnswerFromBatchKnowledge mocks base method.
func (m *MockDao) GetAnswerFromBatchKnowledge(ctx context.Context, session *entity.Session, req *chat.GetAnswerFromBatchKnowledgeRequest, ch chan *chat.GetAnswerFromBatchKnowledgeReply) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnswerFromBatchKnowledge", ctx, session, req, ch)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAnswerFromBatchKnowledge indicates an expected call of GetAnswerFromBatchKnowledge.
func (mr *MockDaoMockRecorder) GetAnswerFromBatchKnowledge(ctx, session, req, ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnswerFromBatchKnowledge", reflect.TypeOf((*MockDao)(nil).GetAnswerFromBatchKnowledge), ctx, session, req, ch)
}

// GetAnswerFromKnowledge mocks base method.
func (m *MockDao) GetAnswerFromKnowledge(ctx context.Context, session *entity.Session, req *chat.GetAnswerFromKnowledgeRequest, ch chan *chat.GetAnswerFromKnowledgeReply) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnswerFromKnowledge", ctx, session, req, ch)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAnswerFromKnowledge indicates an expected call of GetAnswerFromKnowledge.
func (mr *MockDaoMockRecorder) GetAnswerFromKnowledge(ctx, session, req, ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnswerFromKnowledge", reflect.TypeOf((*MockDao)(nil).GetAnswerFromKnowledge), ctx, session, req, ch)
}

// GetAppInfo mocks base method.
func (m *MockDao) GetAppInfo(ctx context.Context, appID uint64, scene uint32) (*bot_admin_config_server.GetAppInfoRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppInfo", ctx, appID, scene)
	ret0, _ := ret[0].(*bot_admin_config_server.GetAppInfoRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppInfo indicates an expected call of GetAppInfo.
func (mr *MockDaoMockRecorder) GetAppInfo(ctx, appID, scene interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppInfo", reflect.TypeOf((*MockDao)(nil).GetAppInfo), ctx, appID, scene)
}

// GetAttributeInfo mocks base method.
func (m *MockDao) GetAttributeInfo(ctx context.Context, req *bot_knowledge_config_server.GetAttributeInfoReq) ([]*bot_knowledge_config_server.GetAttributeInfoRsp_AttrLabelInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAttributeInfo", ctx, req)
	ret0, _ := ret[0].([]*bot_knowledge_config_server.GetAttributeInfoRsp_AttrLabelInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAttributeInfo indicates an expected call of GetAttributeInfo.
func (mr *MockDaoMockRecorder) GetAttributeInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAttributeInfo", reflect.TypeOf((*MockDao)(nil).GetAttributeInfo), ctx, req)
}

// GetCOTSwitch mocks base method.
func (m *MockDao) GetCOTSwitch(ctx context.Context, appID uint64, scene uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCOTSwitch", ctx, appID, scene)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCOTSwitch indicates an expected call of GetCOTSwitch.
func (mr *MockDaoMockRecorder) GetCOTSwitch(ctx, appID, scene interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCOTSwitch", reflect.TypeOf((*MockDao)(nil).GetCOTSwitch), ctx, appID, scene)
}

// GetCorpInfo mocks base method.
func (m *MockDao) GetCorpInfo(ctx context.Context, corpID uint64) (uint64, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCorpInfo", ctx, corpID)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCorpInfo indicates an expected call of GetCorpInfo.
func (mr *MockDaoMockRecorder) GetCorpInfo(ctx, corpID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCorpInfo", reflect.TypeOf((*MockDao)(nil).GetCorpInfo), ctx, corpID)
}

// GetWorkflowCustomConfig mocks base method.
func (m *MockDao) GetWorkflowCustomConfig(ctx context.Context, appID uint64, scene uint32) (*bot_admin_config_server.AppWorkflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowCustomConfig", ctx, appID, scene)
	ret0, _ := ret[0].(*bot_admin_config_server.AppWorkflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowCustomConfig indicates an expected call of GetWorkflowCustomConfig.
func (mr *MockDaoMockRecorder) GetWorkflowCustomConfig(ctx, appID, scene interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowCustomConfig", reflect.TypeOf((*MockDao)(nil).GetWorkflowCustomConfig), ctx, appID, scene)
}

// InnerDescribeDocs mocks base method.
func (m *MockDao) InnerDescribeDocs(ctx context.Context, req *bot_knowledge_config_server.InnerDescribeDocsReq) (*bot_knowledge_config_server.InnerDescribeDocsRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InnerDescribeDocs", ctx, req)
	ret0, _ := ret[0].(*bot_knowledge_config_server.InnerDescribeDocsRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InnerDescribeDocs indicates an expected call of InnerDescribeDocs.
func (mr *MockDaoMockRecorder) InnerDescribeDocs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InnerDescribeDocs", reflect.TypeOf((*MockDao)(nil).InnerDescribeDocs), ctx, req)
}

// IsModelInWhiteList mocks base method.
func (m *MockDao) IsModelInWhiteList(appID, modelName string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsModelInWhiteList", appID, modelName)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsModelInWhiteList indicates an expected call of IsModelInWhiteList.
func (mr *MockDaoMockRecorder) IsModelInWhiteList(appID, modelName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsModelInWhiteList", reflect.TypeOf((*MockDao)(nil).IsModelInWhiteList), appID, modelName)
}

// MCPCallTool mocks base method.
func (m *MockDao) MCPCallTool(ctx context.Context, name string, server *plugin_config_server.MCPServerInfo, headers, bodies map[string]any, uin uint64) (*mcp.CallToolResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MCPCallTool", ctx, name, server, headers, bodies, uin)
	ret0, _ := ret[0].(*mcp.CallToolResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MCPCallTool indicates an expected call of MCPCallTool.
func (mr *MockDaoMockRecorder) MCPCallTool(ctx, name, server, headers, bodies, uin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MCPCallTool", reflect.TypeOf((*MockDao)(nil).MCPCallTool), ctx, name, server, headers, bodies, uin)
}

// MatchRefer mocks base method.
func (m *MockDao) MatchRefer(ctx context.Context, req *bot_knowledge_config_server.MatchReferReq) ([]*bot_knowledge_config_server.MatchReferRsp_Refer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MatchRefer", ctx, req)
	ret0, _ := ret[0].([]*bot_knowledge_config_server.MatchReferRsp_Refer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MatchRefer indicates an expected call of MatchRefer.
func (mr *MockDaoMockRecorder) MatchRefer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MatchRefer", reflect.TypeOf((*MockDao)(nil).MatchRefer), ctx, req)
}

// ReportConcurrencyDosage mocks base method.
func (m *MockDao) ReportConcurrencyDosage(ctx context.Context, session *entity.Session, dosage ConcurrencyDosage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportConcurrencyDosage", ctx, session, dosage)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportConcurrencyDosage indicates an expected call of ReportConcurrencyDosage.
func (mr *MockDaoMockRecorder) ReportConcurrencyDosage(ctx, session, dosage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportConcurrencyDosage", reflect.TypeOf((*MockDao)(nil).ReportConcurrencyDosage), ctx, session, dosage)
}

// ReportOverConcurrencyDosage mocks base method.
func (m *MockDao) ReportOverConcurrencyDosage(ctx context.Context, session *entity.Session, dosage OverloadDosage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportOverConcurrencyDosage", ctx, session, dosage)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportOverConcurrencyDosage indicates an expected call of ReportOverConcurrencyDosage.
func (mr *MockDaoMockRecorder) ReportOverConcurrencyDosage(ctx, session, dosage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportOverConcurrencyDosage", reflect.TypeOf((*MockDao)(nil).ReportOverConcurrencyDosage), ctx, session, dosage)
}

// ReportOverMinuteDosage mocks base method.
func (m *MockDao) ReportOverMinuteDosage(ctx context.Context, session *entity.Session, dosage OverloadDosage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportOverMinuteDosage", ctx, session, dosage)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportOverMinuteDosage indicates an expected call of ReportOverMinuteDosage.
func (mr *MockDaoMockRecorder) ReportOverMinuteDosage(ctx, session, dosage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportOverMinuteDosage", reflect.TypeOf((*MockDao)(nil).ReportOverMinuteDosage), ctx, session, dosage)
}

// ReportTokenDosage mocks base method.
func (m *MockDao) ReportTokenDosage(ctx context.Context, session *entity.Session, dosage TokenDosage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportTokenDosage", ctx, session, dosage)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportTokenDosage indicates an expected call of ReportTokenDosage.
func (mr *MockDaoMockRecorder) ReportTokenDosage(ctx, session, dosage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportTokenDosage", reflect.TypeOf((*MockDao)(nil).ReportTokenDosage), ctx, session, dosage)
}

// RunCode mocks base method.
func (m *MockDao) RunCode(ctx context.Context, runSessionID, codeContent, arguments string) (string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunCode", ctx, runSessionID, codeContent, arguments)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// RunCode indicates an expected call of RunCode.
func (mr *MockDaoMockRecorder) RunCode(ctx, runSessionID, codeContent, arguments interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunCode", reflect.TypeOf((*MockDao)(nil).RunCode), ctx, runSessionID, codeContent, arguments)
}

// RunTool mocks base method.
func (m *MockDao) RunTool(ctx context.Context, req *plugin_exec_server.RunToolReq, opts ...client.Option) (*plugin_exec_server.RunToolRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RunTool", varargs...)
	ret0, _ := ret[0].(*plugin_exec_server.RunToolRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RunTool indicates an expected call of RunTool.
func (mr *MockDaoMockRecorder) RunTool(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunTool", reflect.TypeOf((*MockDao)(nil).RunTool), varargs...)
}

// SearchKnowledge mocks base method.
func (m *MockDao) SearchKnowledge(ctx context.Context, req *knowledge.SearchKnowledgeReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchKnowledge", ctx, req)
	ret0, _ := ret[0].([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchKnowledge indicates an expected call of SearchKnowledge.
func (mr *MockDaoMockRecorder) SearchKnowledge(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchKnowledge", reflect.TypeOf((*MockDao)(nil).SearchKnowledge), ctx, req)
}

// SearchKnowledgeBatch mocks base method.
func (m *MockDao) SearchKnowledgeBatch(ctx context.Context, req *knowledge.SearchKnowledgeBatchReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchKnowledgeBatch", ctx, req)
	ret0, _ := ret[0].([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchKnowledgeBatch indicates an expected call of SearchKnowledgeBatch.
func (mr *MockDaoMockRecorder) SearchKnowledgeBatch(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchKnowledgeBatch", reflect.TypeOf((*MockDao)(nil).SearchKnowledgeBatch), ctx, req)
}

// SearchPreview mocks base method.
func (m *MockDao) SearchPreview(ctx context.Context, req *bot_knowledge_config_server.SearchPreviewReq) ([]*bot_knowledge_config_server.SearchPreviewRsp_Doc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchPreview", ctx, req)
	ret0, _ := ret[0].([]*bot_knowledge_config_server.SearchPreviewRsp_Doc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchPreview indicates an expected call of SearchPreview.
func (mr *MockDaoMockRecorder) SearchPreview(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchPreview", reflect.TypeOf((*MockDao)(nil).SearchPreview), ctx, req)
}

// SearchRelease mocks base method.
func (m *MockDao) SearchRelease(ctx context.Context, req *bot_knowledge_config_server.SearchReq) ([]*bot_knowledge_config_server.SearchRsp_Doc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchRelease", ctx, req)
	ret0, _ := ret[0].([]*bot_knowledge_config_server.SearchRsp_Doc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchRelease indicates an expected call of SearchRelease.
func (mr *MockDaoMockRecorder) SearchRelease(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchRelease", reflect.TypeOf((*MockDao)(nil).SearchRelease), ctx, req)
}

// SimpleChat mocks base method.
func (m *MockDao) SimpleChat(ctx context.Context, session *entity.Session, req *llm_manager_server.Request, stepKey trace.StepKey) (*llm_manager_server.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleChat", ctx, session, req, stepKey)
	ret0, _ := ret[0].(*llm_manager_server.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimpleChat indicates an expected call of SimpleChat.
func (mr *MockDaoMockRecorder) SimpleChat(ctx, session, req, stepKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleChat", reflect.TypeOf((*MockDao)(nil).SimpleChat), ctx, session, req, stepKey)
}

// StreamRunTool mocks base method.
func (m *MockDao) StreamRunTool(ctx context.Context, req *plugin_exec_server.StreamRunToolReq, opts ...client.Option) (chan *plugin_exec_server.StreamRunToolRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StreamRunTool", varargs...)
	ret0, _ := ret[0].(chan *plugin_exec_server.StreamRunToolRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StreamRunTool indicates an expected call of StreamRunTool.
func (mr *MockDaoMockRecorder) StreamRunTool(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamRunTool", reflect.TypeOf((*MockDao)(nil).StreamRunTool), varargs...)
}

// MockIKnowledge is a mock of IKnowledge interface.
type MockIKnowledge struct {
	ctrl     *gomock.Controller
	recorder *MockIKnowledgeMockRecorder
}

// MockIKnowledgeMockRecorder is the mock recorder for MockIKnowledge.
type MockIKnowledgeMockRecorder struct {
	mock *MockIKnowledge
}

// NewMockIKnowledge creates a new mock instance.
func NewMockIKnowledge(ctrl *gomock.Controller) *MockIKnowledge {
	mock := &MockIKnowledge{ctrl: ctrl}
	mock.recorder = &MockIKnowledgeMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIKnowledge) EXPECT() *MockIKnowledgeMockRecorder {
	return m.recorder
}

// GetAttributeInfo mocks base method.
func (m *MockIKnowledge) GetAttributeInfo(ctx context.Context, req *bot_knowledge_config_server.GetAttributeInfoReq) ([]*bot_knowledge_config_server.GetAttributeInfoRsp_AttrLabelInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAttributeInfo", ctx, req)
	ret0, _ := ret[0].([]*bot_knowledge_config_server.GetAttributeInfoRsp_AttrLabelInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAttributeInfo indicates an expected call of GetAttributeInfo.
func (mr *MockIKnowledgeMockRecorder) GetAttributeInfo(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAttributeInfo", reflect.TypeOf((*MockIKnowledge)(nil).GetAttributeInfo), ctx, req)
}

// InnerDescribeDocs mocks base method.
func (m *MockIKnowledge) InnerDescribeDocs(ctx context.Context, req *bot_knowledge_config_server.InnerDescribeDocsReq) (*bot_knowledge_config_server.InnerDescribeDocsRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InnerDescribeDocs", ctx, req)
	ret0, _ := ret[0].(*bot_knowledge_config_server.InnerDescribeDocsRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InnerDescribeDocs indicates an expected call of InnerDescribeDocs.
func (mr *MockIKnowledgeMockRecorder) InnerDescribeDocs(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InnerDescribeDocs", reflect.TypeOf((*MockIKnowledge)(nil).InnerDescribeDocs), ctx, req)
}

// MatchRefer mocks base method.
func (m *MockIKnowledge) MatchRefer(ctx context.Context, req *bot_knowledge_config_server.MatchReferReq) ([]*bot_knowledge_config_server.MatchReferRsp_Refer, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MatchRefer", ctx, req)
	ret0, _ := ret[0].([]*bot_knowledge_config_server.MatchReferRsp_Refer)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MatchRefer indicates an expected call of MatchRefer.
func (mr *MockIKnowledgeMockRecorder) MatchRefer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MatchRefer", reflect.TypeOf((*MockIKnowledge)(nil).MatchRefer), ctx, req)
}

// SearchKnowledge mocks base method.
func (m *MockIKnowledge) SearchKnowledge(ctx context.Context, req *knowledge.SearchKnowledgeReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchKnowledge", ctx, req)
	ret0, _ := ret[0].([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchKnowledge indicates an expected call of SearchKnowledge.
func (mr *MockIKnowledgeMockRecorder) SearchKnowledge(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchKnowledge", reflect.TypeOf((*MockIKnowledge)(nil).SearchKnowledge), ctx, req)
}

// SearchKnowledgeBatch mocks base method.
func (m *MockIKnowledge) SearchKnowledgeBatch(ctx context.Context, req *knowledge.SearchKnowledgeBatchReq) ([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchKnowledgeBatch", ctx, req)
	ret0, _ := ret[0].([]*knowledge.SearchKnowledgeRsp_SearchRsp_Doc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchKnowledgeBatch indicates an expected call of SearchKnowledgeBatch.
func (mr *MockIKnowledgeMockRecorder) SearchKnowledgeBatch(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchKnowledgeBatch", reflect.TypeOf((*MockIKnowledge)(nil).SearchKnowledgeBatch), ctx, req)
}

// SearchPreview mocks base method.
func (m *MockIKnowledge) SearchPreview(ctx context.Context, req *bot_knowledge_config_server.SearchPreviewReq) ([]*bot_knowledge_config_server.SearchPreviewRsp_Doc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchPreview", ctx, req)
	ret0, _ := ret[0].([]*bot_knowledge_config_server.SearchPreviewRsp_Doc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchPreview indicates an expected call of SearchPreview.
func (mr *MockIKnowledgeMockRecorder) SearchPreview(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchPreview", reflect.TypeOf((*MockIKnowledge)(nil).SearchPreview), ctx, req)
}

// SearchRelease mocks base method.
func (m *MockIKnowledge) SearchRelease(ctx context.Context, req *bot_knowledge_config_server.SearchReq) ([]*bot_knowledge_config_server.SearchRsp_Doc, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchRelease", ctx, req)
	ret0, _ := ret[0].([]*bot_knowledge_config_server.SearchRsp_Doc)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchRelease indicates an expected call of SearchRelease.
func (mr *MockIKnowledgeMockRecorder) SearchRelease(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchRelease", reflect.TypeOf((*MockIKnowledge)(nil).SearchRelease), ctx, req)
}

// MockILLM is a mock of ILLM interface.
type MockILLM struct {
	ctrl     *gomock.Controller
	recorder *MockILLMMockRecorder
}

// MockILLMMockRecorder is the mock recorder for MockILLM.
type MockILLMMockRecorder struct {
	mock *MockILLM
}

// NewMockILLM creates a new mock instance.
func NewMockILLM(ctrl *gomock.Controller) *MockILLM {
	mock := &MockILLM{ctrl: ctrl}
	mock.recorder = &MockILLMMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockILLM) EXPECT() *MockILLMMockRecorder {
	return m.recorder
}

// Chat mocks base method.
func (m *MockILLM) Chat(ctx context.Context, session *entity.Session, req *llm_manager_server.Request, stepKey trace.StepKey) (chan *llm_manager_server.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Chat", ctx, session, req, stepKey)
	ret0, _ := ret[0].(chan *llm_manager_server.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Chat indicates an expected call of Chat.
func (mr *MockILLMMockRecorder) Chat(ctx, session, req, stepKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Chat", reflect.TypeOf((*MockILLM)(nil).Chat), ctx, session, req, stepKey)
}

// IsModelInWhiteList mocks base method.
func (m *MockILLM) IsModelInWhiteList(appID, modelName string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsModelInWhiteList", appID, modelName)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsModelInWhiteList indicates an expected call of IsModelInWhiteList.
func (mr *MockILLMMockRecorder) IsModelInWhiteList(appID, modelName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsModelInWhiteList", reflect.TypeOf((*MockILLM)(nil).IsModelInWhiteList), appID, modelName)
}

// SimpleChat mocks base method.
func (m *MockILLM) SimpleChat(ctx context.Context, session *entity.Session, req *llm_manager_server.Request, stepKey trace.StepKey) (*llm_manager_server.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimpleChat", ctx, session, req, stepKey)
	ret0, _ := ret[0].(*llm_manager_server.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimpleChat indicates an expected call of SimpleChat.
func (mr *MockILLMMockRecorder) SimpleChat(ctx, session, req, stepKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimpleChat", reflect.TypeOf((*MockILLM)(nil).SimpleChat), ctx, session, req, stepKey)
}

// MockIAdmin is a mock of IAdmin interface.
type MockIAdmin struct {
	ctrl     *gomock.Controller
	recorder *MockIAdminMockRecorder
}

// MockIAdminMockRecorder is the mock recorder for MockIAdmin.
type MockIAdminMockRecorder struct {
	mock *MockIAdmin
}

// NewMockIAdmin creates a new mock instance.
func NewMockIAdmin(ctrl *gomock.Controller) *MockIAdmin {
	mock := &MockIAdmin{ctrl: ctrl}
	mock.recorder = &MockIAdminMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAdmin) EXPECT() *MockIAdminMockRecorder {
	return m.recorder
}

// GetAppInfo mocks base method.
func (m *MockIAdmin) GetAppInfo(ctx context.Context, appID uint64, scene uint32) (*bot_admin_config_server.GetAppInfoRsp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppInfo", ctx, appID, scene)
	ret0, _ := ret[0].(*bot_admin_config_server.GetAppInfoRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppInfo indicates an expected call of GetAppInfo.
func (mr *MockIAdminMockRecorder) GetAppInfo(ctx, appID, scene interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppInfo", reflect.TypeOf((*MockIAdmin)(nil).GetAppInfo), ctx, appID, scene)
}

// GetCOTSwitch mocks base method.
func (m *MockIAdmin) GetCOTSwitch(ctx context.Context, appID uint64, scene uint32) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCOTSwitch", ctx, appID, scene)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCOTSwitch indicates an expected call of GetCOTSwitch.
func (mr *MockIAdminMockRecorder) GetCOTSwitch(ctx, appID, scene interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCOTSwitch", reflect.TypeOf((*MockIAdmin)(nil).GetCOTSwitch), ctx, appID, scene)
}

// GetCorpInfo mocks base method.
func (m *MockIAdmin) GetCorpInfo(ctx context.Context, corpID uint64) (uint64, uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCorpInfo", ctx, corpID)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(uint32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCorpInfo indicates an expected call of GetCorpInfo.
func (mr *MockIAdminMockRecorder) GetCorpInfo(ctx, corpID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCorpInfo", reflect.TypeOf((*MockIAdmin)(nil).GetCorpInfo), ctx, corpID)
}

// GetWorkflowCustomConfig mocks base method.
func (m *MockIAdmin) GetWorkflowCustomConfig(ctx context.Context, appID uint64, scene uint32) (*bot_admin_config_server.AppWorkflow, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWorkflowCustomConfig", ctx, appID, scene)
	ret0, _ := ret[0].(*bot_admin_config_server.AppWorkflow)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWorkflowCustomConfig indicates an expected call of GetWorkflowCustomConfig.
func (mr *MockIAdminMockRecorder) GetWorkflowCustomConfig(ctx, appID, scene interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWorkflowCustomConfig", reflect.TypeOf((*MockIAdmin)(nil).GetWorkflowCustomConfig), ctx, appID, scene)
}

// MockIChat is a mock of IChat interface.
type MockIChat struct {
	ctrl     *gomock.Controller
	recorder *MockIChatMockRecorder
}

// MockIChatMockRecorder is the mock recorder for MockIChat.
type MockIChatMockRecorder struct {
	mock *MockIChat
}

// NewMockIChat creates a new mock instance.
func NewMockIChat(ctrl *gomock.Controller) *MockIChat {
	mock := &MockIChat{ctrl: ctrl}
	mock.recorder = &MockIChatMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIChat) EXPECT() *MockIChatMockRecorder {
	return m.recorder
}

// GetAnswerFromBatchKnowledge mocks base method.
func (m *MockIChat) GetAnswerFromBatchKnowledge(ctx context.Context, session *entity.Session, req *chat.GetAnswerFromBatchKnowledgeRequest, ch chan *chat.GetAnswerFromBatchKnowledgeReply) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnswerFromBatchKnowledge", ctx, session, req, ch)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAnswerFromBatchKnowledge indicates an expected call of GetAnswerFromBatchKnowledge.
func (mr *MockIChatMockRecorder) GetAnswerFromBatchKnowledge(ctx, session, req, ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnswerFromBatchKnowledge", reflect.TypeOf((*MockIChat)(nil).GetAnswerFromBatchKnowledge), ctx, session, req, ch)
}

// GetAnswerFromKnowledge mocks base method.
func (m *MockIChat) GetAnswerFromKnowledge(ctx context.Context, session *entity.Session, req *chat.GetAnswerFromKnowledgeRequest, ch chan *chat.GetAnswerFromKnowledgeReply) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnswerFromKnowledge", ctx, session, req, ch)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetAnswerFromKnowledge indicates an expected call of GetAnswerFromKnowledge.
func (mr *MockIChatMockRecorder) GetAnswerFromKnowledge(ctx, session, req, ch interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnswerFromKnowledge", reflect.TypeOf((*MockIChat)(nil).GetAnswerFromKnowledge), ctx, session, req, ch)
}

// MockIExtractor is a mock of IExtractor interface.
type MockIExtractor struct {
	ctrl     *gomock.Controller
	recorder *MockIExtractorMockRecorder
}

// MockIExtractorMockRecorder is the mock recorder for MockIExtractor.
type MockIExtractorMockRecorder struct {
	mock *MockIExtractor
}

// NewMockIExtractor creates a new mock instance.
func NewMockIExtractor(ctrl *gomock.Controller) *MockIExtractor {
	mock := &MockIExtractor{ctrl: ctrl}
	mock.recorder = &MockIExtractorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIExtractor) EXPECT() *MockIExtractorMockRecorder {
	return m.recorder
}

// Extractor mocks base method.
func (m *MockIExtractor) Extractor(ctx context.Context, serverName string, req *entity_extractor.EntityExtractorReq) (*entity_extractor.EntityExtractorResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Extractor", ctx, serverName, req)
	ret0, _ := ret[0].(*entity_extractor.EntityExtractorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Extractor indicates an expected call of Extractor.
func (mr *MockIExtractorMockRecorder) Extractor(ctx, serverName, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Extractor", reflect.TypeOf((*MockIExtractor)(nil).Extractor), ctx, serverName, req)
}

// MockIPlugin is a mock of IPlugin interface.
type MockIPlugin struct {
	ctrl     *gomock.Controller
	recorder *MockIPluginMockRecorder
}

// MockIPluginMockRecorder is the mock recorder for MockIPlugin.
type MockIPluginMockRecorder struct {
	mock *MockIPlugin
}

// NewMockIPlugin creates a new mock instance.
func NewMockIPlugin(ctrl *gomock.Controller) *MockIPlugin {
	mock := &MockIPlugin{ctrl: ctrl}
	mock.recorder = &MockIPluginMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPlugin) EXPECT() *MockIPluginMockRecorder {
	return m.recorder
}

// DescribeTool mocks base method.
func (m *MockIPlugin) DescribeTool(ctx context.Context, req *plugin_config_server.DescribeToolReq, opts ...client.Option) (*plugin_config_server.DescribeToolRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DescribeTool", varargs...)
	ret0, _ := ret[0].(*plugin_config_server.DescribeToolRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeTool indicates an expected call of DescribeTool.
func (mr *MockIPluginMockRecorder) DescribeTool(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeTool", reflect.TypeOf((*MockIPlugin)(nil).DescribeTool), varargs...)
}

// MCPCallTool mocks base method.
func (m *MockIPlugin) MCPCallTool(ctx context.Context, name string, server *plugin_config_server.MCPServerInfo, headers, bodies map[string]any, uin uint64) (*mcp.CallToolResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MCPCallTool", ctx, name, server, headers, bodies, uin)
	ret0, _ := ret[0].(*mcp.CallToolResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MCPCallTool indicates an expected call of MCPCallTool.
func (mr *MockIPluginMockRecorder) MCPCallTool(ctx, name, server, headers, bodies, uin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MCPCallTool", reflect.TypeOf((*MockIPlugin)(nil).MCPCallTool), ctx, name, server, headers, bodies, uin)
}

// RunTool mocks base method.
func (m *MockIPlugin) RunTool(ctx context.Context, req *plugin_exec_server.RunToolReq, opts ...client.Option) (*plugin_exec_server.RunToolRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RunTool", varargs...)
	ret0, _ := ret[0].(*plugin_exec_server.RunToolRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RunTool indicates an expected call of RunTool.
func (mr *MockIPluginMockRecorder) RunTool(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunTool", reflect.TypeOf((*MockIPlugin)(nil).RunTool), varargs...)
}

// StreamRunTool mocks base method.
func (m *MockIPlugin) StreamRunTool(ctx context.Context, req *plugin_exec_server.StreamRunToolReq, opts ...client.Option) (chan *plugin_exec_server.StreamRunToolRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StreamRunTool", varargs...)
	ret0, _ := ret[0].(chan *plugin_exec_server.StreamRunToolRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StreamRunTool indicates an expected call of StreamRunTool.
func (mr *MockIPluginMockRecorder) StreamRunTool(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StreamRunTool", reflect.TypeOf((*MockIPlugin)(nil).StreamRunTool), varargs...)
}

// MockICode is a mock of ICode interface.
type MockICode struct {
	ctrl     *gomock.Controller
	recorder *MockICodeMockRecorder
}

// MockICodeMockRecorder is the mock recorder for MockICode.
type MockICodeMockRecorder struct {
	mock *MockICode
}

// NewMockICode creates a new mock instance.
func NewMockICode(ctrl *gomock.Controller) *MockICode {
	mock := &MockICode{ctrl: ctrl}
	mock.recorder = &MockICodeMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICode) EXPECT() *MockICodeMockRecorder {
	return m.recorder
}

// RunCode mocks base method.
func (m *MockICode) RunCode(ctx context.Context, runSessionID, codeContent, arguments string) (string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunCode", ctx, runSessionID, codeContent, arguments)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// RunCode indicates an expected call of RunCode.
func (mr *MockICodeMockRecorder) RunCode(ctx, runSessionID, codeContent, arguments interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunCode", reflect.TypeOf((*MockICode)(nil).RunCode), ctx, runSessionID, codeContent, arguments)
}

// MockIFinance is a mock of IFinance interface.
type MockIFinance struct {
	ctrl     *gomock.Controller
	recorder *MockIFinanceMockRecorder
}

// MockIFinanceMockRecorder is the mock recorder for MockIFinance.
type MockIFinanceMockRecorder struct {
	mock *MockIFinance
}

// NewMockIFinance creates a new mock instance.
func NewMockIFinance(ctrl *gomock.Controller) *MockIFinance {
	mock := &MockIFinance{ctrl: ctrl}
	mock.recorder = &MockIFinanceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIFinance) EXPECT() *MockIFinanceMockRecorder {
	return m.recorder
}

// ReportConcurrencyDosage mocks base method.
func (m *MockIFinance) ReportConcurrencyDosage(ctx context.Context, session *entity.Session, dosage ConcurrencyDosage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportConcurrencyDosage", ctx, session, dosage)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportConcurrencyDosage indicates an expected call of ReportConcurrencyDosage.
func (mr *MockIFinanceMockRecorder) ReportConcurrencyDosage(ctx, session, dosage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportConcurrencyDosage", reflect.TypeOf((*MockIFinance)(nil).ReportConcurrencyDosage), ctx, session, dosage)
}

// ReportOverConcurrencyDosage mocks base method.
func (m *MockIFinance) ReportOverConcurrencyDosage(ctx context.Context, session *entity.Session, dosage OverloadDosage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportOverConcurrencyDosage", ctx, session, dosage)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportOverConcurrencyDosage indicates an expected call of ReportOverConcurrencyDosage.
func (mr *MockIFinanceMockRecorder) ReportOverConcurrencyDosage(ctx, session, dosage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportOverConcurrencyDosage", reflect.TypeOf((*MockIFinance)(nil).ReportOverConcurrencyDosage), ctx, session, dosage)
}

// ReportOverMinuteDosage mocks base method.
func (m *MockIFinance) ReportOverMinuteDosage(ctx context.Context, session *entity.Session, dosage OverloadDosage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportOverMinuteDosage", ctx, session, dosage)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportOverMinuteDosage indicates an expected call of ReportOverMinuteDosage.
func (mr *MockIFinanceMockRecorder) ReportOverMinuteDosage(ctx, session, dosage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportOverMinuteDosage", reflect.TypeOf((*MockIFinance)(nil).ReportOverMinuteDosage), ctx, session, dosage)
}

// ReportTokenDosage mocks base method.
func (m *MockIFinance) ReportTokenDosage(ctx context.Context, session *entity.Session, dosage TokenDosage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReportTokenDosage", ctx, session, dosage)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReportTokenDosage indicates an expected call of ReportTokenDosage.
func (mr *MockIFinanceMockRecorder) ReportTokenDosage(ctx, session, dosage interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportTokenDosage", reflect.TypeOf((*MockIFinance)(nil).ReportTokenDosage), ctx, session, dosage)
}
