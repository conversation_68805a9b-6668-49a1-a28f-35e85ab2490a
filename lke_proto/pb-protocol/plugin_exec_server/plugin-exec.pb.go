// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.19.1
// source: plugin-exec.proto

package plugin_exec_server

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	_ "git.code.oa.com/trpc-go/trpc"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type KnowledgeFilterEnum int32

const (
	KnowledgeFilterEnum_KNOWLEDGE_FILTER_TYPE_ALL        KnowledgeFilterEnum = 0 // 全部知识
	KnowledgeFilterEnum_KNOWLEDGE_FILTER_TYPE_DOC_AND_QA KnowledgeFilterEnum = 1 // 按文档和问答
	KnowledgeFilterEnum_KNOWLEDGE_FILTER_TYPE_TAG        KnowledgeFilterEnum = 2 // 按标签
)

// Enum value maps for KnowledgeFilterEnum.
var (
	KnowledgeFilterEnum_name = map[int32]string{
		0: "KNOWLEDGE_FILTER_TYPE_ALL",
		1: "KNOWLEDGE_FILTER_TYPE_DOC_AND_QA",
		2: "KNOWLEDGE_FILTER_TYPE_TAG",
	}
	KnowledgeFilterEnum_value = map[string]int32{
		"KNOWLEDGE_FILTER_TYPE_ALL":        0,
		"KNOWLEDGE_FILTER_TYPE_DOC_AND_QA": 1,
		"KNOWLEDGE_FILTER_TYPE_TAG":        2,
	}
)

func (x KnowledgeFilterEnum) Enum() *KnowledgeFilterEnum {
	p := new(KnowledgeFilterEnum)
	*p = x
	return p
}

func (x KnowledgeFilterEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeFilterEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_exec_proto_enumTypes[0].Descriptor()
}

func (KnowledgeFilterEnum) Type() protoreflect.EnumType {
	return &file_plugin_exec_proto_enumTypes[0]
}

func (x KnowledgeFilterEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeFilterEnum.Descriptor instead.
func (KnowledgeFilterEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{0}
}

type KnowledgeFilter_TagOperatorEnum int32

const (
	KnowledgeFilter_AND KnowledgeFilter_TagOperatorEnum = 0 // AND
	KnowledgeFilter_OR  KnowledgeFilter_TagOperatorEnum = 1 // OR
)

// Enum value maps for KnowledgeFilter_TagOperatorEnum.
var (
	KnowledgeFilter_TagOperatorEnum_name = map[int32]string{
		0: "AND",
		1: "OR",
	}
	KnowledgeFilter_TagOperatorEnum_value = map[string]int32{
		"AND": 0,
		"OR":  1,
	}
)

func (x KnowledgeFilter_TagOperatorEnum) Enum() *KnowledgeFilter_TagOperatorEnum {
	p := new(KnowledgeFilter_TagOperatorEnum)
	*p = x
	return p
}

func (x KnowledgeFilter_TagOperatorEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeFilter_TagOperatorEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_exec_proto_enumTypes[1].Descriptor()
}

func (KnowledgeFilter_TagOperatorEnum) Type() protoreflect.EnumType {
	return &file_plugin_exec_proto_enumTypes[1]
}

func (x KnowledgeFilter_TagOperatorEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeFilter_TagOperatorEnum.Descriptor instead.
func (KnowledgeFilter_TagOperatorEnum) EnumDescriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{4, 0}
}

type History_Role int32

const (
	History_USER      History_Role = 0 // 用户说的内容
	History_ASSISTANT History_Role = 1 // 大模型回复的额内容
	History_SYSTEM    History_Role = 2 // 一般作为系统Prompt用
	History_NONE      History_Role = 3 // 无角色（与LLM交互时将不自动补齐角色名）
)

// Enum value maps for History_Role.
var (
	History_Role_name = map[int32]string{
		0: "USER",
		1: "ASSISTANT",
		2: "SYSTEM",
		3: "NONE",
	}
	History_Role_value = map[string]int32{
		"USER":      0,
		"ASSISTANT": 1,
		"SYSTEM":    2,
		"NONE":      3,
	}
)

func (x History_Role) Enum() *History_Role {
	p := new(History_Role)
	*p = x
	return p
}

func (x History_Role) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (History_Role) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_exec_proto_enumTypes[2].Descriptor()
}

func (History_Role) Type() protoreflect.EnumType {
	return &file_plugin_exec_proto_enumTypes[2]
}

func (x History_Role) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use History_Role.Descriptor instead.
func (History_Role) EnumDescriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{7, 0}
}

type StreamRunToolReq_ReqType int32

const (
	StreamRunToolReq_UNKNOWN_TYPE StreamRunToolReq_ReqType = 0 // 未知类型
	StreamRunToolReq_RUN          StreamRunToolReq_ReqType = 1 // 工具执行
	StreamRunToolReq_CANCEL       StreamRunToolReq_ReqType = 2 // 取消执行
)

// Enum value maps for StreamRunToolReq_ReqType.
var (
	StreamRunToolReq_ReqType_name = map[int32]string{
		0: "UNKNOWN_TYPE",
		1: "RUN",
		2: "CANCEL",
	}
	StreamRunToolReq_ReqType_value = map[string]int32{
		"UNKNOWN_TYPE": 0,
		"RUN":          1,
		"CANCEL":       2,
	}
)

func (x StreamRunToolReq_ReqType) Enum() *StreamRunToolReq_ReqType {
	p := new(StreamRunToolReq_ReqType)
	*p = x
	return p
}

func (x StreamRunToolReq_ReqType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StreamRunToolReq_ReqType) Descriptor() protoreflect.EnumDescriptor {
	return file_plugin_exec_proto_enumTypes[3].Descriptor()
}

func (StreamRunToolReq_ReqType) Type() protoreflect.EnumType {
	return &file_plugin_exec_proto_enumTypes[3]
}

func (x StreamRunToolReq_ReqType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StreamRunToolReq_ReqType.Descriptor instead.
func (StreamRunToolReq_ReqType) EnumDescriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{10, 0}
}

type RunToolReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppId       string         `protobuf:"bytes,1,opt,name=AppId,proto3" json:"AppId,omitempty"`             // 应用id
	AppScene    uint32         `protobuf:"varint,2,opt,name=AppScene,proto3" json:"AppScene,omitempty"`      // 应用场景 1 评测 2 线上
	PluginId    string         `protobuf:"bytes,3,opt,name=PluginId,proto3" json:"PluginId,omitempty"`       // 插件id
	ToolId      string         `protobuf:"bytes,4,opt,name=ToolId,proto3" json:"ToolId,omitempty"`           // 工具id
	HeaderValue string         `protobuf:"bytes,5,opt,name=HeaderValue,proto3" json:"HeaderValue,omitempty"` // header值，json字符串
	QueryValue  string         `protobuf:"bytes,6,opt,name=QueryValue,proto3" json:"QueryValue,omitempty"`   // query值，json字符串
	BodyValue   string         `protobuf:"bytes,7,opt,name=BodyValue,proto3" json:"BodyValue,omitempty"`     // body值，json字符串
	InputValue  string         `protobuf:"bytes,8,opt,name=InputValue,proto3" json:"InputValue,omitempty"`   // 输入值，json字符串，为Query、Header、Body的合集
	Uin         string         `protobuf:"bytes,9,opt,name=Uin,proto3" json:"Uin,omitempty"`                 // uin
	ExtraInfo   *ToolExtraInfo `protobuf:"bytes,100,opt,name=ExtraInfo,proto3" json:"ExtraInfo,omitempty"`   // 附加信息
}

func (x *RunToolReq) Reset() {
	*x = RunToolReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunToolReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunToolReq) ProtoMessage() {}

func (x *RunToolReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunToolReq.ProtoReflect.Descriptor instead.
func (*RunToolReq) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{0}
}

func (x *RunToolReq) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *RunToolReq) GetAppScene() uint32 {
	if x != nil {
		return x.AppScene
	}
	return 0
}

func (x *RunToolReq) GetPluginId() string {
	if x != nil {
		return x.PluginId
	}
	return ""
}

func (x *RunToolReq) GetToolId() string {
	if x != nil {
		return x.ToolId
	}
	return ""
}

func (x *RunToolReq) GetHeaderValue() string {
	if x != nil {
		return x.HeaderValue
	}
	return ""
}

func (x *RunToolReq) GetQueryValue() string {
	if x != nil {
		return x.QueryValue
	}
	return ""
}

func (x *RunToolReq) GetBodyValue() string {
	if x != nil {
		return x.BodyValue
	}
	return ""
}

func (x *RunToolReq) GetInputValue() string {
	if x != nil {
		return x.InputValue
	}
	return ""
}

func (x *RunToolReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *RunToolReq) GetExtraInfo() *ToolExtraInfo {
	if x != nil {
		return x.ExtraInfo
	}
	return nil
}

// 附加信息
type ToolExtraInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SystemRole string     `protobuf:"bytes,1,opt,name=SystemRole,proto3" json:"SystemRole,omitempty"` // 系统角色
	SessionId  string     `protobuf:"bytes,2,opt,name=SessionId,proto3" json:"SessionId,omitempty"`   // 上下文SessionId 代码执行工具用
	History    []*History `protobuf:"bytes,3,rep,name=History,proto3" json:"History,omitempty"`       // 历史记录
	Labels     []*Label   `protobuf:"bytes,4,rep,name=Labels,proto3" json:"Labels,omitempty"`         // 标签
	// agent 模式的知识库配置
	AgentKnowledgeQaConfig *AgentKnowledgeQAConfig `protobuf:"bytes,5,opt,name=AgentKnowledgeQaConfig,proto3" json:"AgentKnowledgeQaConfig,omitempty"`
}

func (x *ToolExtraInfo) Reset() {
	*x = ToolExtraInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolExtraInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolExtraInfo) ProtoMessage() {}

func (x *ToolExtraInfo) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolExtraInfo.ProtoReflect.Descriptor instead.
func (*ToolExtraInfo) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{1}
}

func (x *ToolExtraInfo) GetSystemRole() string {
	if x != nil {
		return x.SystemRole
	}
	return ""
}

func (x *ToolExtraInfo) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *ToolExtraInfo) GetHistory() []*History {
	if x != nil {
		return x.History
	}
	return nil
}

func (x *ToolExtraInfo) GetLabels() []*Label {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *ToolExtraInfo) GetAgentKnowledgeQaConfig() *AgentKnowledgeQAConfig {
	if x != nil {
		return x.AgentKnowledgeQaConfig
	}
	return nil
}

type AgentModelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// index 和别的地方对齐
	ModelName   string  `protobuf:"bytes,8,opt,name=ModelName,proto3" json:"ModelName,omitempty"`
	Temperature float32 `protobuf:"fixed32,14,opt,name=Temperature,proto3" json:"Temperature,omitempty"`
	// TopP
	TopP float32 `protobuf:"fixed32,15,opt,name=TopP,proto3" json:"TopP,omitempty"`
}

func (x *AgentModelInfo) Reset() {
	*x = AgentModelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentModelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentModelInfo) ProtoMessage() {}

func (x *AgentModelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentModelInfo.ProtoReflect.Descriptor instead.
func (*AgentModelInfo) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{2}
}

func (x *AgentModelInfo) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *AgentModelInfo) GetTemperature() float32 {
	if x != nil {
		return x.Temperature
	}
	return 0
}

func (x *AgentModelInfo) GetTopP() float32 {
	if x != nil {
		return x.TopP
	}
	return 0
}

type KnowledgeFilterDocAndAnswer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DocBizIds []string `protobuf:"bytes,5,rep,name=DocBizIds,proto3" json:"DocBizIds,omitempty"` // 文档ID列表
	AllQa     bool     `protobuf:"varint,6,opt,name=AllQa,proto3" json:"AllQa,omitempty"`        // 问答
}

func (x *KnowledgeFilterDocAndAnswer) Reset() {
	*x = KnowledgeFilterDocAndAnswer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeFilterDocAndAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeFilterDocAndAnswer) ProtoMessage() {}

func (x *KnowledgeFilterDocAndAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeFilterDocAndAnswer.ProtoReflect.Descriptor instead.
func (*KnowledgeFilterDocAndAnswer) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{3}
}

func (x *KnowledgeFilterDocAndAnswer) GetDocBizIds() []string {
	if x != nil {
		return x.DocBizIds
	}
	return nil
}

func (x *KnowledgeFilterDocAndAnswer) GetAllQa() bool {
	if x != nil {
		return x.AllQa
	}
	return false
}

type KnowledgeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilterType   KnowledgeFilterEnum             `protobuf:"varint,1,opt,name=FilterType,proto3,enum=trpc.KEP.plugin_exec_server.KnowledgeFilterEnum" json:"FilterType,omitempty"`               // 知识检索筛选方式: [文档和问答] or [标签]
	DocAndAnswer *KnowledgeFilterDocAndAnswer    `protobuf:"bytes,2,opt,name=DocAndAnswer,proto3" json:"DocAndAnswer,omitempty"`                                                                 // 文档和问答过滤器
	TagOperator  KnowledgeFilter_TagOperatorEnum `protobuf:"varint,3,opt,name=TagOperator,proto3,enum=trpc.KEP.plugin_exec_server.KnowledgeFilter_TagOperatorEnum" json:"TagOperator,omitempty"` // 标签之间的关系，可以是 AND OR，标签从 Labels 中取
}

func (x *KnowledgeFilter) Reset() {
	*x = KnowledgeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KnowledgeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeFilter) ProtoMessage() {}

func (x *KnowledgeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeFilter.ProtoReflect.Descriptor instead.
func (*KnowledgeFilter) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{4}
}

func (x *KnowledgeFilter) GetFilterType() KnowledgeFilterEnum {
	if x != nil {
		return x.FilterType
	}
	return KnowledgeFilterEnum_KNOWLEDGE_FILTER_TYPE_ALL
}

func (x *KnowledgeFilter) GetDocAndAnswer() *KnowledgeFilterDocAndAnswer {
	if x != nil {
		return x.DocAndAnswer
	}
	return nil
}

func (x *KnowledgeFilter) GetTagOperator() KnowledgeFilter_TagOperatorEnum {
	if x != nil {
		return x.TagOperator
	}
	return KnowledgeFilter_AND
}

type AgentKnowledgeQAConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识检索筛选范围
	Filter *KnowledgeFilter `protobuf:"bytes,1,opt,name=Filter,proto3" json:"Filter,omitempty"`
	// 插件调用LLM时使用的模型配置，一般用于指定知识库问答插件的生成模型
	AgentModel *AgentModelInfo `protobuf:"bytes,9,opt,name=AgentModel,proto3" json:"AgentModel,omitempty"`
}

func (x *AgentKnowledgeQAConfig) Reset() {
	*x = AgentKnowledgeQAConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AgentKnowledgeQAConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentKnowledgeQAConfig) ProtoMessage() {}

func (x *AgentKnowledgeQAConfig) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentKnowledgeQAConfig.ProtoReflect.Descriptor instead.
func (*AgentKnowledgeQAConfig) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{5}
}

func (x *AgentKnowledgeQAConfig) GetFilter() *KnowledgeFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *AgentKnowledgeQAConfig) GetAgentModel() *AgentModelInfo {
	if x != nil {
		return x.AgentModel
	}
	return nil
}

type RunToolRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result         string           `protobuf:"bytes,1,opt,name=Result,proto3" json:"Result,omitempty"`                   // 执行结果，解析后的json字符串
	RawResult      string           `protobuf:"bytes,2,opt,name=RawResult,proto3" json:"RawResult,omitempty"`             // 执行结果，解析前的原始字符串
	StatisticInfos []*StatisticInfo `protobuf:"bytes,100,rep,name=StatisticInfos,proto3" json:"StatisticInfos,omitempty"` // LLM统计信息
}

func (x *RunToolRsp) Reset() {
	*x = RunToolRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunToolRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunToolRsp) ProtoMessage() {}

func (x *RunToolRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunToolRsp.ProtoReflect.Descriptor instead.
func (*RunToolRsp) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{6}
}

func (x *RunToolRsp) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *RunToolRsp) GetRawResult() string {
	if x != nil {
		return x.RawResult
	}
	return ""
}

func (x *RunToolRsp) GetStatisticInfos() []*StatisticInfo {
	if x != nil {
		return x.StatisticInfos
	}
	return nil
}

// 历史记录
type History struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Role    History_Role `protobuf:"varint,1,opt,name=role,proto3,enum=trpc.KEP.plugin_exec_server.History_Role" json:"role,omitempty"` // 消息的角色
	Content string       `protobuf:"bytes,2,opt,name=Content,proto3" json:"Content,omitempty"`                                          // 内容
	Images  []string     `protobuf:"bytes,3,rep,name=Images,proto3" json:"Images,omitempty"`                                            // 图片地址
}

func (x *History) Reset() {
	*x = History{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *History) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*History) ProtoMessage() {}

func (x *History) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use History.ProtoReflect.Descriptor instead.
func (*History) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{7}
}

func (x *History) GetRole() History_Role {
	if x != nil {
		return x.Role
	}
	return History_USER
}

func (x *History) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *History) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

// 标签
type Label struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name   string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`     // 标签名
	Values []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"` // 标签值
}

func (x *Label) Reset() {
	*x = Label{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Label) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Label) ProtoMessage() {}

func (x *Label) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Label.ProtoReflect.Descriptor instead.
func (*Label) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{8}
}

func (x *Label) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Label) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// 统计信息
type StatisticInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelName      string `protobuf:"bytes,1,opt,name=ModelName,proto3" json:"ModelName,omitempty"`            // 模型名称
	FirstTokenCost uint32 `protobuf:"varint,2,opt,name=FirstTokenCost,proto3" json:"FirstTokenCost,omitempty"` // 首token耗时
	TotalCost      uint32 `protobuf:"varint,3,opt,name=TotalCost,proto3" json:"TotalCost,omitempty"`           // 推理总耗时
	InputTokens    uint32 `protobuf:"varint,4,opt,name=InputTokens,proto3" json:"InputTokens,omitempty"`       // 输入token数量
	OutputTokens   uint32 `protobuf:"varint,5,opt,name=OutputTokens,proto3" json:"OutputTokens,omitempty"`     // 输出token数量
	TotalTokens    uint32 `protobuf:"varint,6,opt,name=TotalTokens,proto3" json:"TotalTokens,omitempty"`       // 输入+输出总token
}

func (x *StatisticInfo) Reset() {
	*x = StatisticInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatisticInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticInfo) ProtoMessage() {}

func (x *StatisticInfo) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticInfo.ProtoReflect.Descriptor instead.
func (*StatisticInfo) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{9}
}

func (x *StatisticInfo) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *StatisticInfo) GetFirstTokenCost() uint32 {
	if x != nil {
		return x.FirstTokenCost
	}
	return 0
}

func (x *StatisticInfo) GetTotalCost() uint32 {
	if x != nil {
		return x.TotalCost
	}
	return 0
}

func (x *StatisticInfo) GetInputTokens() uint32 {
	if x != nil {
		return x.InputTokens
	}
	return 0
}

func (x *StatisticInfo) GetOutputTokens() uint32 {
	if x != nil {
		return x.OutputTokens
	}
	return 0
}

func (x *StatisticInfo) GetTotalTokens() uint32 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

// 工具运行请求（流式）
type StreamRunToolReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type StreamRunToolReq_ReqType `protobuf:"varint,1,opt,name=Type,proto3,enum=trpc.KEP.plugin_exec_server.StreamRunToolReq_ReqType" json:"Type,omitempty"` // 请求类型
	Req  *RunToolReq              `protobuf:"bytes,100,opt,name=Req,proto3" json:"Req,omitempty"`                                                            // 请求参数
}

func (x *StreamRunToolReq) Reset() {
	*x = StreamRunToolReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreamRunToolReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamRunToolReq) ProtoMessage() {}

func (x *StreamRunToolReq) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamRunToolReq.ProtoReflect.Descriptor instead.
func (*StreamRunToolReq) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{10}
}

func (x *StreamRunToolReq) GetType() StreamRunToolReq_ReqType {
	if x != nil {
		return x.Type
	}
	return StreamRunToolReq_UNKNOWN_TYPE
}

func (x *StreamRunToolReq) GetReq() *RunToolReq {
	if x != nil {
		return x.Req
	}
	return nil
}

// 工具运行返回（流式）
type StreamRunToolRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32       `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`       // 返回码
	ErrMsg  string      `protobuf:"bytes,2,opt,name=ErrMsg,proto3" json:"ErrMsg,omitempty"`    // 错误信息
	IsFinal bool        `protobuf:"varint,3,opt,name=IsFinal,proto3" json:"IsFinal,omitempty"` // 是否结束
	Rsp     *RunToolRsp `protobuf:"bytes,100,opt,name=Rsp,proto3" json:"Rsp,omitempty"`        // 响应参数
}

func (x *StreamRunToolRsp) Reset() {
	*x = StreamRunToolRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_plugin_exec_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StreamRunToolRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamRunToolRsp) ProtoMessage() {}

func (x *StreamRunToolRsp) ProtoReflect() protoreflect.Message {
	mi := &file_plugin_exec_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamRunToolRsp.ProtoReflect.Descriptor instead.
func (*StreamRunToolRsp) Descriptor() ([]byte, []int) {
	return file_plugin_exec_proto_rawDescGZIP(), []int{11}
}

func (x *StreamRunToolRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *StreamRunToolRsp) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

func (x *StreamRunToolRsp) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *StreamRunToolRsp) GetRsp() *RunToolRsp {
	if x != nil {
		return x.Rsp
	}
	return nil
}

var File_plugin_exec_proto protoreflect.FileDescriptor

var file_plugin_exec_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x2d, 0x65, 0x78, 0x65, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1b, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x1a, 0x0a, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xce, 0x02, 0x0a,
	0x0a, 0x52, 0x75, 0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x41,
	0x70, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x41, 0x70, 0x70, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6f,
	0x6c, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6f, 0x6c, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x42, 0x6f, 0x64, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x42, 0x6f, 0x64, 0x79, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x55, 0x69, 0x6e, 0x12, 0x48, 0x0a, 0x09, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x09, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xb6, 0x02,
	0x0a, 0x0d, 0x54, 0x6f, 0x6f, 0x6c, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1e, 0x0a, 0x0a, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x3e, 0x0a,
	0x07, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x48, 0x69, 0x73,
	0x74, 0x6f, 0x72, 0x79, 0x52, 0x07, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x3a, 0x0a,
	0x06, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x52, 0x06, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x6b, 0x0a, 0x16, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x61, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x41, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x16,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x61,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x64, 0x0a, 0x0e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x54, 0x65, 0x6d,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x54, 0x6f, 0x70, 0x50,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x54, 0x6f, 0x70, 0x50, 0x22, 0x51, 0x0a, 0x1b,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x44,
	0x6f, 0x63, 0x41, 0x6e, 0x64, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x44,
	0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09,
	0x44, 0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x6c, 0x6c,
	0x51, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x41, 0x6c, 0x6c, 0x51, 0x61, 0x22,
	0xc5, 0x02, 0x0a, 0x0f, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0a, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x5c, 0x0a, 0x0c, 0x44, 0x6f, 0x63, 0x41, 0x6e, 0x64, 0x41,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78,
	0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x44, 0x6f, 0x63, 0x41, 0x6e, 0x64, 0x41,
	0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x0c, 0x44, 0x6f, 0x63, 0x41, 0x6e, 0x64, 0x41, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x12, 0x5e, 0x0a, 0x0b, 0x54, 0x61, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x61, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0b, 0x54, 0x61, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x22, 0x22, 0x0a, 0x0f, 0x54, 0x61, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e, 0x44, 0x10, 0x00, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x52, 0x10, 0x01, 0x22, 0xab, 0x01, 0x0a, 0x16, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x51, 0x41, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x44, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x4b, 0x0a, 0x0a, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0x96, 0x01, 0x0a, 0x0a, 0x52, 0x75, 0x6e, 0x54, 0x6f, 0x6f,
	0x6c, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x52, 0x61, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x52, 0x61, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x52, 0x0a, 0x0e, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x64, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0xb1,
	0x01, 0x0a, 0x07, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x3d, 0x0a, 0x04, 0x72, 0x6f,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x2e, 0x52,
	0x6f, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x22, 0x35, 0x0a, 0x04, 0x52,
	0x6f, 0x6c, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x53, 0x45, 0x52, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x41, 0x53, 0x53, 0x49, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f, 0x4e, 0x45,
	0x10, 0x03, 0x22, 0x33, 0x0a, 0x05, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xdb, 0x01, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x46, 0x69, 0x72, 0x73, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0e, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x73, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x09, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x20, 0x0a,
	0x0b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12,
	0x22, 0x0a, 0x0c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22, 0xca, 0x01, 0x0a, 0x10, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x52, 0x75, 0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x49, 0x0a, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x75, 0x6e,
	0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x2e, 0x52, 0x65, 0x71, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x03, 0x52, 0x65, 0x71, 0x18, 0x64, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x52, 0x75, 0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x52, 0x03, 0x52, 0x65, 0x71,
	0x22, 0x30, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x00, 0x12, 0x07, 0x0a,
	0x03, 0x52, 0x55, 0x4e, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c,
	0x10, 0x02, 0x22, 0x93, 0x01, 0x0a, 0x10, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x75, 0x6e,
	0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x45,
	0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x45, 0x72, 0x72,
	0x4d, 0x73, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x49, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x39, 0x0a,
	0x03, 0x52, 0x73, 0x70, 0x18, 0x64, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65,
	0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x54, 0x6f, 0x6f, 0x6c,
	0x52, 0x73, 0x70, 0x52, 0x03, 0x52, 0x73, 0x70, 0x2a, 0x79, 0x0a, 0x13, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x12,
	0x1d, 0x0a, 0x19, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x4c,
	0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x24,
	0x0a, 0x20, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x54,
	0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x5f, 0x41, 0x4e, 0x44, 0x5f,
	0x51, 0x41, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47,
	0x45, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x41,
	0x47, 0x10, 0x02, 0x32, 0xdc, 0x01, 0x0a, 0x0a, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x45, 0x78,
	0x65, 0x63, 0x12, 0x5b, 0x0a, 0x07, 0x52, 0x75, 0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x12, 0x27, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x54,
	0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x27, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x12,
	0x71, 0x0a, 0x0d, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x75, 0x6e, 0x54, 0x6f, 0x6f, 0x6c,
	0x12, 0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67,
	0x69, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x75, 0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x65, 0x71, 0x1a,
	0x2d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x70, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x52, 0x75, 0x6e, 0x54, 0x6f, 0x6f, 0x6c, 0x52, 0x73, 0x70, 0x28, 0x01,
	0x30, 0x01, 0x42, 0x48, 0x5a, 0x46, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62,
	0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e,
	0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_plugin_exec_proto_rawDescOnce sync.Once
	file_plugin_exec_proto_rawDescData = file_plugin_exec_proto_rawDesc
)

func file_plugin_exec_proto_rawDescGZIP() []byte {
	file_plugin_exec_proto_rawDescOnce.Do(func() {
		file_plugin_exec_proto_rawDescData = protoimpl.X.CompressGZIP(file_plugin_exec_proto_rawDescData)
	})
	return file_plugin_exec_proto_rawDescData
}

var file_plugin_exec_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_plugin_exec_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_plugin_exec_proto_goTypes = []interface{}{
	(KnowledgeFilterEnum)(0),             // 0: trpc.KEP.plugin_exec_server.KnowledgeFilterEnum
	(KnowledgeFilter_TagOperatorEnum)(0), // 1: trpc.KEP.plugin_exec_server.KnowledgeFilter.TagOperatorEnum
	(History_Role)(0),                    // 2: trpc.KEP.plugin_exec_server.History.Role
	(StreamRunToolReq_ReqType)(0),        // 3: trpc.KEP.plugin_exec_server.StreamRunToolReq.ReqType
	(*RunToolReq)(nil),                   // 4: trpc.KEP.plugin_exec_server.RunToolReq
	(*ToolExtraInfo)(nil),                // 5: trpc.KEP.plugin_exec_server.ToolExtraInfo
	(*AgentModelInfo)(nil),               // 6: trpc.KEP.plugin_exec_server.AgentModelInfo
	(*KnowledgeFilterDocAndAnswer)(nil),  // 7: trpc.KEP.plugin_exec_server.KnowledgeFilterDocAndAnswer
	(*KnowledgeFilter)(nil),              // 8: trpc.KEP.plugin_exec_server.KnowledgeFilter
	(*AgentKnowledgeQAConfig)(nil),       // 9: trpc.KEP.plugin_exec_server.AgentKnowledgeQAConfig
	(*RunToolRsp)(nil),                   // 10: trpc.KEP.plugin_exec_server.RunToolRsp
	(*History)(nil),                      // 11: trpc.KEP.plugin_exec_server.History
	(*Label)(nil),                        // 12: trpc.KEP.plugin_exec_server.Label
	(*StatisticInfo)(nil),                // 13: trpc.KEP.plugin_exec_server.StatisticInfo
	(*StreamRunToolReq)(nil),             // 14: trpc.KEP.plugin_exec_server.StreamRunToolReq
	(*StreamRunToolRsp)(nil),             // 15: trpc.KEP.plugin_exec_server.StreamRunToolRsp
}
var file_plugin_exec_proto_depIdxs = []int32{
	5,  // 0: trpc.KEP.plugin_exec_server.RunToolReq.ExtraInfo:type_name -> trpc.KEP.plugin_exec_server.ToolExtraInfo
	11, // 1: trpc.KEP.plugin_exec_server.ToolExtraInfo.History:type_name -> trpc.KEP.plugin_exec_server.History
	12, // 2: trpc.KEP.plugin_exec_server.ToolExtraInfo.Labels:type_name -> trpc.KEP.plugin_exec_server.Label
	9,  // 3: trpc.KEP.plugin_exec_server.ToolExtraInfo.AgentKnowledgeQaConfig:type_name -> trpc.KEP.plugin_exec_server.AgentKnowledgeQAConfig
	0,  // 4: trpc.KEP.plugin_exec_server.KnowledgeFilter.FilterType:type_name -> trpc.KEP.plugin_exec_server.KnowledgeFilterEnum
	7,  // 5: trpc.KEP.plugin_exec_server.KnowledgeFilter.DocAndAnswer:type_name -> trpc.KEP.plugin_exec_server.KnowledgeFilterDocAndAnswer
	1,  // 6: trpc.KEP.plugin_exec_server.KnowledgeFilter.TagOperator:type_name -> trpc.KEP.plugin_exec_server.KnowledgeFilter.TagOperatorEnum
	8,  // 7: trpc.KEP.plugin_exec_server.AgentKnowledgeQAConfig.Filter:type_name -> trpc.KEP.plugin_exec_server.KnowledgeFilter
	6,  // 8: trpc.KEP.plugin_exec_server.AgentKnowledgeQAConfig.AgentModel:type_name -> trpc.KEP.plugin_exec_server.AgentModelInfo
	13, // 9: trpc.KEP.plugin_exec_server.RunToolRsp.StatisticInfos:type_name -> trpc.KEP.plugin_exec_server.StatisticInfo
	2,  // 10: trpc.KEP.plugin_exec_server.History.role:type_name -> trpc.KEP.plugin_exec_server.History.Role
	3,  // 11: trpc.KEP.plugin_exec_server.StreamRunToolReq.Type:type_name -> trpc.KEP.plugin_exec_server.StreamRunToolReq.ReqType
	4,  // 12: trpc.KEP.plugin_exec_server.StreamRunToolReq.Req:type_name -> trpc.KEP.plugin_exec_server.RunToolReq
	10, // 13: trpc.KEP.plugin_exec_server.StreamRunToolRsp.Rsp:type_name -> trpc.KEP.plugin_exec_server.RunToolRsp
	4,  // 14: trpc.KEP.plugin_exec_server.PluginExec.RunTool:input_type -> trpc.KEP.plugin_exec_server.RunToolReq
	14, // 15: trpc.KEP.plugin_exec_server.PluginExec.StreamRunTool:input_type -> trpc.KEP.plugin_exec_server.StreamRunToolReq
	10, // 16: trpc.KEP.plugin_exec_server.PluginExec.RunTool:output_type -> trpc.KEP.plugin_exec_server.RunToolRsp
	15, // 17: trpc.KEP.plugin_exec_server.PluginExec.StreamRunTool:output_type -> trpc.KEP.plugin_exec_server.StreamRunToolRsp
	16, // [16:18] is the sub-list for method output_type
	14, // [14:16] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_plugin_exec_proto_init() }
func file_plugin_exec_proto_init() {
	if File_plugin_exec_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_plugin_exec_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunToolReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_exec_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolExtraInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_exec_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentModelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_exec_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeFilterDocAndAnswer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_exec_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KnowledgeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_exec_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AgentKnowledgeQAConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_exec_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunToolRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_exec_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*History); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_exec_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Label); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_exec_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatisticInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_exec_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreamRunToolReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_plugin_exec_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StreamRunToolRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_plugin_exec_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_plugin_exec_proto_goTypes,
		DependencyIndexes: file_plugin_exec_proto_depIdxs,
		EnumInfos:         file_plugin_exec_proto_enumTypes,
		MessageInfos:      file_plugin_exec_proto_msgTypes,
	}.Build()
	File_plugin_exec_proto = out.File
	file_plugin_exec_proto_rawDesc = nil
	file_plugin_exec_proto_goTypes = nil
	file_plugin_exec_proto_depIdxs = nil
}
