// ############################################################################
//
// PB 来源 -> https://git.woa.com/dialogue-platform/bot-dm/bot-dm-server.git
//
// ############################################################################

// KEP.bot-dm-server
//
// @(#)bot-dm.proto  December 07, 2023
// Copyright(c) 2023, boyucao@Tencent. All rights reserved.

// ######################################################################################### //
//                                                                                           //
//     PB 文件 [bot-dm.proto] 请移步到                                                       //
//                                                                                           //
//     https://git.woa.com/raven/pb-stub/blob/master/thirds-pb/bot-dm-server/bot-dm.proto    //
//                                                                                           //
// ######################################################################################### //

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.6.1
// source: workflow-dm.proto

package KEP_WF_DM

import (
	reflect "reflect"
	sync "sync"

	KEP_WF "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RunEnvType int32

const (
	RunEnvType_SANDBOX RunEnvType = 0 // 沙箱环境
	RunEnvType_PRODUCT RunEnvType = 1 // 正式环境
)

// Enum value maps for RunEnvType.
var (
	RunEnvType_name = map[int32]string{
		0: "SANDBOX",
		1: "PRODUCT",
	}
	RunEnvType_value = map[string]int32{
		"SANDBOX": 0,
		"PRODUCT": 1,
	}
)

func (x RunEnvType) Enum() *RunEnvType {
	p := new(RunEnvType)
	*p = x
	return p
}

func (x RunEnvType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RunEnvType) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_dm_proto_enumTypes[0].Descriptor()
}

func (RunEnvType) Type() protoreflect.EnumType {
	return &file_workflow_dm_proto_enumTypes[0]
}

func (x RunEnvType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RunEnvType.Descriptor instead.
func (RunEnvType) EnumDescriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{0}
}

type RequestType int32

const (
	RequestType_RUN  RequestType = 0 // 运行
	RequestType_STOP RequestType = 1 // 停止
)

// Enum value maps for RequestType.
var (
	RequestType_name = map[int32]string{
		0: "RUN",
		1: "STOP",
	}
	RequestType_value = map[string]int32{
		"RUN":  0,
		"STOP": 1,
	}
)

func (x RequestType) Enum() *RequestType {
	p := new(RequestType)
	*p = x
	return p
}

func (x RequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_dm_proto_enumTypes[1].Descriptor()
}

func (RequestType) Type() protoreflect.EnumType {
	return &file_workflow_dm_proto_enumTypes[1]
}

func (x RequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RequestType.Descriptor instead.
func (RequestType) EnumDescriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{1}
}

// 消息的角色
type Role int32

const (
	Role_USER      Role = 0 // 用户说的内容
	Role_ASSISTANT Role = 1 // 大模型回复的额内容
	Role_SYSTEM    Role = 2 // 一般作为系统Prompt用
	Role_NONE      Role = 3 // 无角色（与LLM交互时将不自动补齐角色名）
)

// Enum value maps for Role.
var (
	Role_name = map[int32]string{
		0: "USER",
		1: "ASSISTANT",
		2: "SYSTEM",
		3: "NONE",
	}
	Role_value = map[string]int32{
		"USER":      0,
		"ASSISTANT": 1,
		"SYSTEM":    2,
		"NONE":      3,
	}
)

func (x Role) Enum() *Role {
	p := new(Role)
	*p = x
	return p
}

func (x Role) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Role) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_dm_proto_enumTypes[2].Descriptor()
}

func (Role) Type() protoreflect.EnumType {
	return &file_workflow_dm_proto_enumTypes[2]
}

func (x Role) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Role.Descriptor instead.
func (Role) EnumDescriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{2}
}

type VerboseModeType int32

const (
	VerboseModeType_ALL              VerboseModeType = 0 // 返回每次对话信息、运行信息和报错信息。
	VerboseModeType_RECORD_AND_ERROR VerboseModeType = 1 // 返回每次对话信息和报错信息，
	VerboseModeType_RECORD_ONLY      VerboseModeType = 2 // 只返回每次对话信息
)

// Enum value maps for VerboseModeType.
var (
	VerboseModeType_name = map[int32]string{
		0: "ALL",
		1: "RECORD_AND_ERROR",
		2: "RECORD_ONLY",
	}
	VerboseModeType_value = map[string]int32{
		"ALL":              0,
		"RECORD_AND_ERROR": 1,
		"RECORD_ONLY":      2,
	}
)

func (x VerboseModeType) Enum() *VerboseModeType {
	p := new(VerboseModeType)
	*p = x
	return p
}

func (x VerboseModeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerboseModeType) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_dm_proto_enumTypes[3].Descriptor()
}

func (VerboseModeType) Type() protoreflect.EnumType {
	return &file_workflow_dm_proto_enumTypes[3]
}

func (x VerboseModeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerboseModeType.Descriptor instead.
func (VerboseModeType) EnumDescriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{3}
}

type RespondType int32

const (
	RespondType_RT_UNKNOWN RespondType = 0 // 未知
	RespondType_RT_LLM     RespondType = 1 // LLM
	RespondType_RT_CUSTOM  RespondType = 2 // 自定义
)

// Enum value maps for RespondType.
var (
	RespondType_name = map[int32]string{
		0: "RT_UNKNOWN",
		1: "RT_LLM",
		2: "RT_CUSTOM",
	}
	RespondType_value = map[string]int32{
		"RT_UNKNOWN": 0,
		"RT_LLM":     1,
		"RT_CUSTOM":  2,
	}
)

func (x RespondType) Enum() *RespondType {
	p := new(RespondType)
	*p = x
	return p
}

func (x RespondType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RespondType) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_dm_proto_enumTypes[4].Descriptor()
}

func (RespondType) Type() protoreflect.EnumType {
	return &file_workflow_dm_proto_enumTypes[4]
}

func (x RespondType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RespondType.Descriptor instead.
func (RespondType) EnumDescriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{4}
}

type WorkflowStatus int32

const (
	WorkflowStatus_PENDING  WorkflowStatus = 0 // 未知（命中的工作流不会有初始状态）
	WorkflowStatus_RUNNING  WorkflowStatus = 1 // 运行中
	WorkflowStatus_FAILED   WorkflowStatus = 2 // 运行失败
	WorkflowStatus_SUCCESS  WorkflowStatus = 3 // 运行完成
	WorkflowStatus_CANCELED WorkflowStatus = 4 // 已取消
)

// Enum value maps for WorkflowStatus.
var (
	WorkflowStatus_name = map[int32]string{
		0: "PENDING",
		1: "RUNNING",
		2: "FAILED",
		3: "SUCCESS",
		4: "CANCELED",
	}
	WorkflowStatus_value = map[string]int32{
		"PENDING":  0,
		"RUNNING":  1,
		"FAILED":   2,
		"SUCCESS":  3,
		"CANCELED": 4,
	}
)

func (x WorkflowStatus) Enum() *WorkflowStatus {
	p := new(WorkflowStatus)
	*p = x
	return p
}

func (x WorkflowStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WorkflowStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_dm_proto_enumTypes[5].Descriptor()
}

func (WorkflowStatus) Type() protoreflect.EnumType {
	return &file_workflow_dm_proto_enumTypes[5]
}

func (x WorkflowStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WorkflowStatus.Descriptor instead.
func (WorkflowStatus) EnumDescriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{5}
}

type RunNodeInfo_StatusType int32

const (
	RunNodeInfo_INIT     RunNodeInfo_StatusType = 0 // 初始状态
	RunNodeInfo_RUNNING  RunNodeInfo_StatusType = 1 // 运行中
	RunNodeInfo_SUCCESS  RunNodeInfo_StatusType = 2 // 运行成功
	RunNodeInfo_FAILED   RunNodeInfo_StatusType = 3 // 运行失败
	RunNodeInfo_CANCELED RunNodeInfo_StatusType = 4 // 已取消
)

// Enum value maps for RunNodeInfo_StatusType.
var (
	RunNodeInfo_StatusType_name = map[int32]string{
		0: "INIT",
		1: "RUNNING",
		2: "SUCCESS",
		3: "FAILED",
		4: "CANCELED",
	}
	RunNodeInfo_StatusType_value = map[string]int32{
		"INIT":     0,
		"RUNNING":  1,
		"SUCCESS":  2,
		"FAILED":   3,
		"CANCELED": 4,
	}
)

func (x RunNodeInfo_StatusType) Enum() *RunNodeInfo_StatusType {
	p := new(RunNodeInfo_StatusType)
	*p = x
	return p
}

func (x RunNodeInfo_StatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RunNodeInfo_StatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_workflow_dm_proto_enumTypes[6].Descriptor()
}

func (RunNodeInfo_StatusType) Type() protoreflect.EnumType {
	return &file_workflow_dm_proto_enumTypes[6]
}

func (x RunNodeInfo_StatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RunNodeInfo_StatusType.Descriptor instead.
func (RunNodeInfo_StatusType) EnumDescriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{7, 0}
}

type RunWorkflowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 应用下的会话ID。 是应用下唯一，不是全局唯一的
	SessionID string `protobuf:"bytes,1,opt,name=SessionID,proto3" json:"SessionID,omitempty" valid:"required~会话ID不能为空"`
	// 运行环境
	RunEnv RunEnvType `protobuf:"varint,2,opt,name=RunEnv,proto3,enum=trpc.KEP.bot_workflow_dm_server.RunEnvType" json:"RunEnv,omitempty"`
	// 请求类型，包含运行、停止
	RequestType RequestType `protobuf:"varint,3,opt,name=RequestType,proto3,enum=trpc.KEP.bot_workflow_dm_server.RequestType" json:"RequestType,omitempty"`
	// 应用ID
	AppID string `protobuf:"bytes,4,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~应用ID不能为空"`
	// 指定工作流
	WorkflowID string `protobuf:"bytes,5,opt,name=WorkflowID,proto3" json:"WorkflowID,omitempty"`
	// 当前的query。
	Query string `protobuf:"bytes,6,opt,name=Query,proto3" json:"Query,omitempty"`
	// 对话历史原始的内容
	QueryHistory []*Message `protobuf:"bytes,7,rep,name=QueryHistory,proto3" json:"QueryHistory,omitempty"`
	// 变量信息，传用户ID等变量信息
	CustomVariables map[string]*Variable `protobuf:"bytes,8,rep,name=CustomVariables,proto3" json:"CustomVariables,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 自定义参数
	RelatedRecordID string               `protobuf:"bytes,9,opt,name=RelatedRecordID,proto3" json:"RelatedRecordID,omitempty"`                                                                                         // query对应的recordID
	RecordID        string               `protobuf:"bytes,10,opt,name=RecordID,proto3" json:"RecordID,omitempty"`                                                                                                      // query的回复对应的recordID
	// 是否为调试
	IsDebug bool `protobuf:"varint,11,opt,name=IsDebug,proto3" json:"IsDebug,omitempty"`
	// 主模型名称
	MainModelName string `protobuf:"bytes,12,opt,name=MainModelName,proto3" json:"MainModelName,omitempty"`
	// 输入变量的值。 key：字段的名称； value：字段的值，可以为各种类型： int/float/string/图片url/视频url等
	Inputs map[string]string `protobuf:"bytes,13,rep,name=Inputs,proto3" json:"Inputs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 改写后的query
	RewriteQuery string `protobuf:"bytes,14,opt,name=RewriteQuery,proto3" json:"RewriteQuery,omitempty"`
	// 返回的信息类型
	VerboseMode VerboseModeType `protobuf:"varint,15,opt,name=VerboseMode,proto3,enum=trpc.KEP.bot_workflow_dm_server.VerboseModeType" json:"VerboseMode,omitempty"`
	// // 上一个query命中的workflowID，用来判断对话是否连续。（如选项卡就需要用来判断是否需要重新提问） // 咱不需要，使用历史对话判断即可
	// string LastQueryWorkflowID = 15;
	// 计费子业务项（上报大模型用量需要，DM对子业务项无感知）
	FinanceSubBizType string `protobuf:"bytes,16,opt,name=FinanceSubBizType,proto3" json:"FinanceSubBizType,omitempty"`
	// 计费标签，会带到计费明细中，可以基于该标签做计费过滤（上报大模型用量需要，DM对计费标签无感知）
	BillingTag map[string]string `protobuf:"bytes,17,rep,name=BillingTag,proto3" json:"BillingTag,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *RunWorkflowRequest) Reset() {
	*x = RunWorkflowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunWorkflowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunWorkflowRequest) ProtoMessage() {}

func (x *RunWorkflowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunWorkflowRequest.ProtoReflect.Descriptor instead.
func (*RunWorkflowRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{0}
}

func (x *RunWorkflowRequest) GetSessionID() string {
	if x != nil {
		return x.SessionID
	}
	return ""
}

func (x *RunWorkflowRequest) GetRunEnv() RunEnvType {
	if x != nil {
		return x.RunEnv
	}
	return RunEnvType_SANDBOX
}

func (x *RunWorkflowRequest) GetRequestType() RequestType {
	if x != nil {
		return x.RequestType
	}
	return RequestType_RUN
}

func (x *RunWorkflowRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *RunWorkflowRequest) GetWorkflowID() string {
	if x != nil {
		return x.WorkflowID
	}
	return ""
}

func (x *RunWorkflowRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *RunWorkflowRequest) GetQueryHistory() []*Message {
	if x != nil {
		return x.QueryHistory
	}
	return nil
}

func (x *RunWorkflowRequest) GetCustomVariables() map[string]*Variable {
	if x != nil {
		return x.CustomVariables
	}
	return nil
}

func (x *RunWorkflowRequest) GetRelatedRecordID() string {
	if x != nil {
		return x.RelatedRecordID
	}
	return ""
}

func (x *RunWorkflowRequest) GetRecordID() string {
	if x != nil {
		return x.RecordID
	}
	return ""
}

func (x *RunWorkflowRequest) GetIsDebug() bool {
	if x != nil {
		return x.IsDebug
	}
	return false
}

func (x *RunWorkflowRequest) GetMainModelName() string {
	if x != nil {
		return x.MainModelName
	}
	return ""
}

func (x *RunWorkflowRequest) GetInputs() map[string]string {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *RunWorkflowRequest) GetRewriteQuery() string {
	if x != nil {
		return x.RewriteQuery
	}
	return ""
}

func (x *RunWorkflowRequest) GetVerboseMode() VerboseModeType {
	if x != nil {
		return x.VerboseMode
	}
	return VerboseModeType_ALL
}

func (x *RunWorkflowRequest) GetFinanceSubBizType() string {
	if x != nil {
		return x.FinanceSubBizType
	}
	return ""
}

func (x *RunWorkflowRequest) GetBillingTag() map[string]string {
	if x != nil {
		return x.BillingTag
	}
	return nil
}

// 一次对话消息
type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Role     Role   `protobuf:"varint,1,opt,name=Role,proto3,enum=trpc.KEP.bot_workflow_dm_server.Role" json:"Role,omitempty"` // 角色
	Content  string `protobuf:"bytes,2,opt,name=Content,proto3" json:"Content,omitempty"`                                      // 内容
	RecordID string `protobuf:"bytes,3,opt,name=RecordID,proto3" json:"RecordID,omitempty"`                                    // record的ID
}

func (x *Message) Reset() {
	*x = Message{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{1}
}

func (x *Message) GetRole() Role {
	if x != nil {
		return x.Role
	}
	return Role_USER
}

func (x *Message) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Message) GetRecordID() string {
	if x != nil {
		return x.RecordID
	}
	return ""
}

type Variable struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`   // 关键字
	Value string `protobuf:"bytes,2,opt,name=Value,proto3" json:"Value,omitempty"` // 值
}

func (x *Variable) Reset() {
	*x = Variable{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Variable) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Variable) ProtoMessage() {}

func (x *Variable) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Variable.ProtoReflect.Descriptor instead.
func (*Variable) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{2}
}

func (x *Variable) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Variable) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type RunWorkflowReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      int32    `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`          // 0表示成功。 只要服务端的stream回复过一条消息后，error就没有作用了，只能通过code/message返回错误原因
	Message   string   `protobuf:"bytes,2,opt,name=Message,proto3" json:"Message,omitempty"`     // 错误原因
	SessionID string   `protobuf:"bytes,3,opt,name=SessionID,proto3" json:"SessionID,omitempty"` // 会话ID
	IsFinal   bool     `protobuf:"varint,4,opt,name=IsFinal,proto3" json:"IsFinal,omitempty"`    // 当前消息是否最后一条
	Respond   *Respond `protobuf:"bytes,5,opt,name=Respond,proto3" json:"Respond,omitempty"`     // 正常非空。
	// LLM统计信息。
	StatisticInfos []*StatisticInfo `protobuf:"bytes,100,rep,name=StatisticInfos,proto3" json:"StatisticInfos,omitempty"`
}

func (x *RunWorkflowReply) Reset() {
	*x = RunWorkflowReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunWorkflowReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunWorkflowReply) ProtoMessage() {}

func (x *RunWorkflowReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunWorkflowReply.ProtoReflect.Descriptor instead.
func (*RunWorkflowReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{3}
}

func (x *RunWorkflowReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RunWorkflowReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RunWorkflowReply) GetSessionID() string {
	if x != nil {
		return x.SessionID
	}
	return ""
}

func (x *RunWorkflowReply) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *RunWorkflowReply) GetRespond() *Respond {
	if x != nil {
		return x.Respond
	}
	return nil
}

func (x *RunWorkflowReply) GetStatisticInfos() []*StatisticInfo {
	if x != nil {
		return x.StatisticInfos
	}
	return nil
}

type Respond struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type                RespondType       `protobuf:"varint,1,opt,name=Type,proto3,enum=trpc.KEP.bot_workflow_dm_server.RespondType" json:"Type,omitempty"`                                                        // 回复类型
	Content             string            `protobuf:"bytes,2,opt,name=Content,proto3" json:"Content,omitempty"`                                                                                                    // 回复内容（合并后的）
	References          []*Reference      `protobuf:"bytes,3,rep,name=References,proto3" json:"References,omitempty"`                                                                                              // 文档数据
	WorkflowRunID       string            `protobuf:"bytes,4,opt,name=WorkflowRunID,proto3" json:"WorkflowRunID,omitempty"`                                                                                        // 工作流运行ID
	WorkflowID          string            `protobuf:"bytes,5,opt,name=WorkflowID,proto3" json:"WorkflowID,omitempty"`                                                                                              // 工作流ID
	WorkflowName        string            `protobuf:"bytes,6,opt,name=WorkflowName,proto3" json:"WorkflowName,omitempty"`                                                                                          // 工作流名称
	WorkflowStatus      WorkflowStatus    `protobuf:"varint,7,opt,name=WorkflowStatus,proto3,enum=trpc.KEP.bot_workflow_dm_server.WorkflowStatus" json:"WorkflowStatus,omitempty"`                                 // 工作流状态
	RunNodes            []*RunNodeInfo    `protobuf:"bytes,8,rep,name=RunNodes,proto3" json:"RunNodes,omitempty"`                                                                                                  // 节点的信息
	OptionCards         []string          `protobuf:"bytes,9,rep,name=OptionCards,proto3" json:"OptionCards,omitempty"`                                                                                            // 选项卡。
	ContentList         []string          `protobuf:"bytes,10,rep,name=ContentList,proto3" json:"ContentList,omitempty"`                                                                                           // 回复内容的数组。
	ThoughtList         []*ThoughtInfo    `protobuf:"bytes,11,rep,name=ThoughtList,proto3" json:"ThoughtList,omitempty"`                                                                                           // 思考过程的数组。
	HitOptionCardIndex  int32             `protobuf:"varint,12,opt,name=HitOptionCardIndex,proto3" json:"HitOptionCardIndex,omitempty"`                                                                            // 命中了哪个选项卡
	AnswerOutput        map[string]string `protobuf:"bytes,13,rep,name=AnswerOutput,proto3" json:"AnswerOutput,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 回复节点的自定义输出
	WorkflowReleaseTime string            `protobuf:"bytes,14,opt,name=WorkflowReleaseTime,proto3" json:"WorkflowReleaseTime,omitempty"`                                                                           // 工作流的发布时间（RFC3339格式，如： 2025-05-08T15:04:49+08:00）
}

func (x *Respond) Reset() {
	*x = Respond{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Respond) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Respond) ProtoMessage() {}

func (x *Respond) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Respond.ProtoReflect.Descriptor instead.
func (*Respond) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{4}
}

func (x *Respond) GetType() RespondType {
	if x != nil {
		return x.Type
	}
	return RespondType_RT_UNKNOWN
}

func (x *Respond) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Respond) GetReferences() []*Reference {
	if x != nil {
		return x.References
	}
	return nil
}

func (x *Respond) GetWorkflowRunID() string {
	if x != nil {
		return x.WorkflowRunID
	}
	return ""
}

func (x *Respond) GetWorkflowID() string {
	if x != nil {
		return x.WorkflowID
	}
	return ""
}

func (x *Respond) GetWorkflowName() string {
	if x != nil {
		return x.WorkflowName
	}
	return ""
}

func (x *Respond) GetWorkflowStatus() WorkflowStatus {
	if x != nil {
		return x.WorkflowStatus
	}
	return WorkflowStatus_PENDING
}

func (x *Respond) GetRunNodes() []*RunNodeInfo {
	if x != nil {
		return x.RunNodes
	}
	return nil
}

func (x *Respond) GetOptionCards() []string {
	if x != nil {
		return x.OptionCards
	}
	return nil
}

func (x *Respond) GetContentList() []string {
	if x != nil {
		return x.ContentList
	}
	return nil
}

func (x *Respond) GetThoughtList() []*ThoughtInfo {
	if x != nil {
		return x.ThoughtList
	}
	return nil
}

func (x *Respond) GetHitOptionCardIndex() int32 {
	if x != nil {
		return x.HitOptionCardIndex
	}
	return 0
}

func (x *Respond) GetAnswerOutput() map[string]string {
	if x != nil {
		return x.AnswerOutput
	}
	return nil
}

func (x *Respond) GetWorkflowReleaseTime() string {
	if x != nil {
		return x.WorkflowReleaseTime
	}
	return ""
}

type ThoughtInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Content    string `protobuf:"bytes,1,opt,name=Content,proto3" json:"Content,omitempty"`        // 思考内容
	StartTime  int64  `protobuf:"varint,2,opt,name=StartTime,proto3" json:"StartTime,omitempty"`   // 开始时间（毫秒时间戳）
	EndTime    int64  `protobuf:"varint,3,opt,name=EndTime,proto3" json:"EndTime,omitempty"`       // 结束时间（毫秒时间戳）
	NodeName   string `protobuf:"bytes,4,opt,name=NodeName,proto3" json:"NodeName,omitempty"`      // 节点名称
	ReplyIndex uint32 `protobuf:"varint,5,opt,name=ReplyIndex,proto3" json:"ReplyIndex,omitempty"` // 所在的回复的下标，下标值可能比回复数组长度大，因为思考可能对应空回复
}

func (x *ThoughtInfo) Reset() {
	*x = ThoughtInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThoughtInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThoughtInfo) ProtoMessage() {}

func (x *ThoughtInfo) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThoughtInfo.ProtoReflect.Descriptor instead.
func (*ThoughtInfo) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{5}
}

func (x *ThoughtInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ThoughtInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ThoughtInfo) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ThoughtInfo) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *ThoughtInfo) GetReplyIndex() uint32 {
	if x != nil {
		return x.ReplyIndex
	}
	return 0
}

type Reference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DocID    uint64 `protobuf:"varint,1,opt,name=DocID,proto3" json:"DocID,omitempty"` // 文档ID
	ID       uint64 `protobuf:"varint,2,opt,name=ID,proto3" json:"ID,omitempty"`       // QAID/SegmentID/RejectID
	Name     string `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty"`    // 显示名称
	Type     uint32 `protobuf:"varint,4,opt,name=Type,proto3" json:"Type,omitempty"`   // 来源类型。 QA = 1;  SEGMENT = 2;  REJECT = 3;
	Url      string `protobuf:"bytes,5,opt,name=Url,proto3" json:"Url,omitempty"`      // 关联链接
	DocBizID uint64 `protobuf:"varint,6,opt,name=DocBizID,proto3" json:"DocBizID,omitempty"`
	DocName  string `protobuf:"bytes,7,opt,name=DocName,proto3" json:"DocName,omitempty"`
	QABizID  uint64 `protobuf:"varint,8,opt,name=QABizID,proto3" json:"QABizID,omitempty"`
}

func (x *Reference) Reset() {
	*x = Reference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reference) ProtoMessage() {}

func (x *Reference) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reference.ProtoReflect.Descriptor instead.
func (*Reference) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{6}
}

func (x *Reference) GetDocID() uint64 {
	if x != nil {
		return x.DocID
	}
	return 0
}

func (x *Reference) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *Reference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Reference) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Reference) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *Reference) GetDocBizID() uint64 {
	if x != nil {
		return x.DocBizID
	}
	return 0
}

func (x *Reference) GetDocName() string {
	if x != nil {
		return x.DocName
	}
	return ""
}

func (x *Reference) GetQABizID() uint64 {
	if x != nil {
		return x.QABizID
	}
	return 0
}

type RunNodeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeID   string                 `protobuf:"bytes,1,opt,name=NodeID,proto3" json:"NodeID,omitempty"`                                                              // 节点ID
	NodeType KEP_WF.NodeType        `protobuf:"varint,2,opt,name=NodeType,proto3,enum=trpc.KEP.bot_task_config_wf_server.NodeType" json:"NodeType,omitempty"`        // 节点类型
	NodeName string                 `protobuf:"bytes,3,opt,name=NodeName,proto3" json:"NodeName,omitempty"`                                                          // 节点名称
	Status   RunNodeInfo_StatusType `protobuf:"varint,4,opt,name=Status,proto3,enum=trpc.KEP.bot_workflow_dm_server.RunNodeInfo_StatusType" json:"Status,omitempty"` // 状态
	Input    string                 `protobuf:"bytes,5,opt,name=Input,proto3" json:"Input,omitempty"`                                                                // 节点的输入。json字符串（含普通字符串）
	Output   string                 `protobuf:"bytes,6,opt,name=Output,proto3" json:"Output,omitempty"`                                                              // 节点的最终输出。json字符串（含普通字符串）
	//  string TaskInput = 7;       // 任务的输入。（如API的完整入参）
	TaskOutput       string `protobuf:"bytes,8,opt,name=TaskOutput,proto3" json:"TaskOutput,omitempty"`               // 任务的输出。（原始输出）
	FailMessage      string `protobuf:"bytes,9,opt,name=FailMessage,proto3" json:"FailMessage,omitempty"`             // 异常信息
	CostMilliSeconds uint32 `protobuf:"varint,10,opt,name=CostMilliSeconds,proto3" json:"CostMilliSeconds"` // 节点的总耗时。如果节点有多次调用，耗时为多次调用的总和。
	Reply            string `protobuf:"bytes,11,opt,name=Reply,proto3" json:"Reply,omitempty"`                        // 当前节点的回复内容。当前参数提取、消息节点、结束回复节点可能非空
	BelongNodeID     string `protobuf:"bytes,12,opt,name=BelongNodeID,proto3" json:"BelongNodeID,omitempty"`          // 节点所属工作流被引用时的引用节点的ID，所属的工作流是被引用的时候非空。（后面如果要支持多层嵌套的话，需要修改成数组）。
	IsCurrent        bool   `protobuf:"varint,13,opt,name=IsCurrent,proto3" json:"IsCurrent,omitempty"`               // 当前走过的节点。前端根据这个内容来判断是否展开节点，需要在chat中设置。
	FailCode         string `protobuf:"bytes,14,opt,name=FailCode,proto3" json:"FailCode,omitempty"`                  // 异常信息对应的错误码，为云API格式的二级错误码，如： "NodeErr.MissingParam"
	Log              string `protobuf:"bytes,15,opt,name=Log,proto3" json:"Log,omitempty"`                            // 节点的日志
	LogRef           string `protobuf:"bytes,16,opt,name=LogRef,proto3" json:"LogRef,omitempty"`                      // 节点的日志的完整内容的链接（当Log内容超过限制的时候此字段才有值）
	InputRef         string `protobuf:"bytes,17,opt,name=InputRef,proto3" json:"InputRef,omitempty"`                  // 节点的输入的完整内容的链接。（当Input内容超过限制的时候此字段才有值）
	OutputRef        string `protobuf:"bytes,18,opt,name=OutputRef,proto3" json:"OutputRef,omitempty"`                // 节点的输出的完整内容的链接。（当Output内容超过限制的时候此字段才有值）
	TaskOutputRef    string `protobuf:"bytes,19,opt,name=TaskOutputRef,proto3" json:"TaskOutputRef,omitempty"`        // 任务的原始输出的完整内容的链接。（当TaskOutput内容超过限制的时候此字段才有值）
	// LLM统计信息。
	StatisticInfos []*StatisticInfo `protobuf:"bytes,100,rep,name=StatisticInfos,proto3" json:"StatisticInfos,omitempty"`
}

func (x *RunNodeInfo) Reset() {
	*x = RunNodeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RunNodeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunNodeInfo) ProtoMessage() {}

func (x *RunNodeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunNodeInfo.ProtoReflect.Descriptor instead.
func (*RunNodeInfo) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{7}
}

func (x *RunNodeInfo) GetNodeID() string {
	if x != nil {
		return x.NodeID
	}
	return ""
}

func (x *RunNodeInfo) GetNodeType() KEP_WF.NodeType {
	if x != nil {
		return x.NodeType
	}
	return KEP_WF.NodeType_UNKNOWN
}

func (x *RunNodeInfo) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

func (x *RunNodeInfo) GetStatus() RunNodeInfo_StatusType {
	if x != nil {
		return x.Status
	}
	return RunNodeInfo_INIT
}

func (x *RunNodeInfo) GetInput() string {
	if x != nil {
		return x.Input
	}
	return ""
}

func (x *RunNodeInfo) GetOutput() string {
	if x != nil {
		return x.Output
	}
	return ""
}

func (x *RunNodeInfo) GetTaskOutput() string {
	if x != nil {
		return x.TaskOutput
	}
	return ""
}

func (x *RunNodeInfo) GetFailMessage() string {
	if x != nil {
		return x.FailMessage
	}
	return ""
}

func (x *RunNodeInfo) GetCostMilliSeconds() uint32 {
	if x != nil {
		return x.CostMilliSeconds
	}
	return 0
}

func (x *RunNodeInfo) GetReply() string {
	if x != nil {
		return x.Reply
	}
	return ""
}

func (x *RunNodeInfo) GetBelongNodeID() string {
	if x != nil {
		return x.BelongNodeID
	}
	return ""
}

func (x *RunNodeInfo) GetIsCurrent() bool {
	if x != nil {
		return x.IsCurrent
	}
	return false
}

func (x *RunNodeInfo) GetFailCode() string {
	if x != nil {
		return x.FailCode
	}
	return ""
}

func (x *RunNodeInfo) GetLog() string {
	if x != nil {
		return x.Log
	}
	return ""
}

func (x *RunNodeInfo) GetLogRef() string {
	if x != nil {
		return x.LogRef
	}
	return ""
}

func (x *RunNodeInfo) GetInputRef() string {
	if x != nil {
		return x.InputRef
	}
	return ""
}

func (x *RunNodeInfo) GetOutputRef() string {
	if x != nil {
		return x.OutputRef
	}
	return ""
}

func (x *RunNodeInfo) GetTaskOutputRef() string {
	if x != nil {
		return x.TaskOutputRef
	}
	return ""
}

func (x *RunNodeInfo) GetStatisticInfos() []*StatisticInfo {
	if x != nil {
		return x.StatisticInfos
	}
	return nil
}

// 统计信息
type StatisticInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelName      string `protobuf:"bytes,1,opt,name=ModelName,proto3" json:"ModelName"`            // 模型名称
	FirstTokenCost uint32 `protobuf:"varint,2,opt,name=FirstTokenCost,proto3" json:"FirstTokenCost"` // 首token耗时
	TotalCost      uint32 `protobuf:"varint,3,opt,name=TotalCost,proto3" json:"TotalCost"`           // 推理总耗时
	InputTokens    uint32 `protobuf:"varint,4,opt,name=InputTokens,proto3" json:"InputTokens"`       // 输入token数量
	OutputTokens   uint32 `protobuf:"varint,5,opt,name=OutputTokens,proto3" json:"OutputTokens"`     // 输出token数量
	TotalTokens    uint32 `protobuf:"varint,6,opt,name=TotalTokens,proto3" json:"TotalTokens"`       // 输入+输出总token
	IsSubWorkflow  bool   `protobuf:"varint,7,opt,name=IsSubWorkflow,proto3" json:"IsSubWorkflow,omitempty"`   // 是否为子工作流的统计信息
}

func (x *StatisticInfo) Reset() {
	*x = StatisticInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatisticInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatisticInfo) ProtoMessage() {}

func (x *StatisticInfo) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatisticInfo.ProtoReflect.Descriptor instead.
func (*StatisticInfo) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{8}
}

func (x *StatisticInfo) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *StatisticInfo) GetFirstTokenCost() uint32 {
	if x != nil {
		return x.FirstTokenCost
	}
	return 0
}

func (x *StatisticInfo) GetTotalCost() uint32 {
	if x != nil {
		return x.TotalCost
	}
	return 0
}

func (x *StatisticInfo) GetInputTokens() uint32 {
	if x != nil {
		return x.InputTokens
	}
	return 0
}

func (x *StatisticInfo) GetOutputTokens() uint32 {
	if x != nil {
		return x.OutputTokens
	}
	return 0
}

func (x *StatisticInfo) GetTotalTokens() uint32 {
	if x != nil {
		return x.TotalTokens
	}
	return 0
}

func (x *StatisticInfo) GetIsSubWorkflow() bool {
	if x != nil {
		return x.IsSubWorkflow
	}
	return false
}

type ClearSessionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SessionID string     `protobuf:"bytes,1,opt,name=SessionID,proto3" json:"SessionID,omitempty" valid:"required~会话ID不能为空"`                                            // 应用下的会话ID。 是应用下唯一，不是全局唯一的
	RunEnv    RunEnvType `protobuf:"varint,2,opt,name=RunEnv,proto3,enum=trpc.KEP.bot_workflow_dm_server.RunEnvType" json:"RunEnv,omitempty"` // 运行环境
	AppID     string     `protobuf:"bytes,3,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~应用ID不能为空"`                                                    // 应用ID
}

func (x *ClearSessionRequest) Reset() {
	*x = ClearSessionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearSessionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearSessionRequest) ProtoMessage() {}

func (x *ClearSessionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearSessionRequest.ProtoReflect.Descriptor instead.
func (*ClearSessionRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{9}
}

func (x *ClearSessionRequest) GetSessionID() string {
	if x != nil {
		return x.SessionID
	}
	return ""
}

func (x *ClearSessionRequest) GetRunEnv() RunEnvType {
	if x != nil {
		return x.RunEnv
	}
	return RunEnvType_SANDBOX
}

func (x *ClearSessionRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

// 错误码：
// 8000001： 参数不合法
// 8000002： 运行错误
// 8000101： session不存在
type ClearSessionReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ClearSessionReply) Reset() {
	*x = ClearSessionReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearSessionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearSessionReply) ProtoMessage() {}

func (x *ClearSessionReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearSessionReply.ProtoReflect.Descriptor instead.
func (*ClearSessionReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{10}
}

type UpsertAppToSandboxRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID                    string `protobuf:"bytes,1,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"`                                       // 应用ID。
	RetrievalWorkflowGroupID string `protobuf:"bytes,2,opt,name=RetrievalWorkflowGroupID,proto3" json:"RetrievalWorkflowGroupID,omitempty"` // 检索工作流的groupID。
	RetrievalWorkflowModel   string `protobuf:"bytes,3,opt,name=RetrievalWorkflowModel,proto3" json:"RetrievalWorkflowModel,omitempty"`     // 检索工作流的模型。
}

func (x *UpsertAppToSandboxRequest) Reset() {
	*x = UpsertAppToSandboxRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertAppToSandboxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertAppToSandboxRequest) ProtoMessage() {}

func (x *UpsertAppToSandboxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertAppToSandboxRequest.ProtoReflect.Descriptor instead.
func (*UpsertAppToSandboxRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{11}
}

func (x *UpsertAppToSandboxRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *UpsertAppToSandboxRequest) GetRetrievalWorkflowGroupID() string {
	if x != nil {
		return x.RetrievalWorkflowGroupID
	}
	return ""
}

func (x *UpsertAppToSandboxRequest) GetRetrievalWorkflowModel() string {
	if x != nil {
		return x.RetrievalWorkflowModel
	}
	return ""
}

type UpsertAppToSandboxReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpsertAppToSandboxReply) Reset() {
	*x = UpsertAppToSandboxReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertAppToSandboxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertAppToSandboxReply) ProtoMessage() {}

func (x *UpsertAppToSandboxReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertAppToSandboxReply.ProtoReflect.Descriptor instead.
func (*UpsertAppToSandboxReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{12}
}

type UpsertWorkflowToSandboxRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID    string           `protobuf:"bytes,1,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"`       // 应用ID。
	Workflow *KEP_WF.Workflow `protobuf:"bytes,2,opt,name=Workflow,proto3" json:"Workflow,omitempty" valid:"required~Workflow不能为空"` // 工作流
}

func (x *UpsertWorkflowToSandboxRequest) Reset() {
	*x = UpsertWorkflowToSandboxRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertWorkflowToSandboxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertWorkflowToSandboxRequest) ProtoMessage() {}

func (x *UpsertWorkflowToSandboxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertWorkflowToSandboxRequest.ProtoReflect.Descriptor instead.
func (*UpsertWorkflowToSandboxRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{13}
}

func (x *UpsertWorkflowToSandboxRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *UpsertWorkflowToSandboxRequest) GetWorkflow() *KEP_WF.Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

type UpsertWorkflowToSandboxReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpsertWorkflowToSandboxReply) Reset() {
	*x = UpsertWorkflowToSandboxReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertWorkflowToSandboxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertWorkflowToSandboxReply) ProtoMessage() {}

func (x *UpsertWorkflowToSandboxReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertWorkflowToSandboxReply.ProtoReflect.Descriptor instead.
func (*UpsertWorkflowToSandboxReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{14}
}

type DeleteWorkflowsInSandboxRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID       string   `protobuf:"bytes,1,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"`             // 应用ID
	WorkflowIDs []string `protobuf:"bytes,2,rep,name=WorkflowIDs,proto3" json:"WorkflowIDs,omitempty" valid:"required~WorkflowIDs不能为空"` // 工作流ID
}

func (x *DeleteWorkflowsInSandboxRequest) Reset() {
	*x = DeleteWorkflowsInSandboxRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWorkflowsInSandboxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkflowsInSandboxRequest) ProtoMessage() {}

func (x *DeleteWorkflowsInSandboxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkflowsInSandboxRequest.ProtoReflect.Descriptor instead.
func (*DeleteWorkflowsInSandboxRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{15}
}

func (x *DeleteWorkflowsInSandboxRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *DeleteWorkflowsInSandboxRequest) GetWorkflowIDs() []string {
	if x != nil {
		return x.WorkflowIDs
	}
	return nil
}

type DeleteWorkflowsInSandboxReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteWorkflowsInSandboxReply) Reset() {
	*x = DeleteWorkflowsInSandboxReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWorkflowsInSandboxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkflowsInSandboxReply) ProtoMessage() {}

func (x *DeleteWorkflowsInSandboxReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkflowsInSandboxReply.ProtoReflect.Descriptor instead.
func (*DeleteWorkflowsInSandboxReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{16}
}

type ReleaseWorkflowAppRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID                    string   `protobuf:"bytes,1,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"`                                       // 应用ID
	UpsertWorkflowIDs        []string `protobuf:"bytes,2,rep,name=UpsertWorkflowIDs,proto3" json:"UpsertWorkflowIDs,omitempty"`               // 工作流ID
	DeleteWorkflowIDs        []string `protobuf:"bytes,3,rep,name=DeleteWorkflowIDs,proto3" json:"DeleteWorkflowIDs,omitempty"`               // 工作流ID
	UpsertParameterIDs       []string `protobuf:"bytes,4,rep,name=UpsertParameterIDs,proto3" json:"UpsertParameterIDs,omitempty"`             // 变更的参数ID
	DeleteParameterIDs       []string `protobuf:"bytes,5,rep,name=DeleteParameterIDs,proto3" json:"DeleteParameterIDs,omitempty"`             // 删除的参数ID
	RetrievalWorkflowGroupID string   `protobuf:"bytes,6,opt,name=RetrievalWorkflowGroupID,proto3" json:"RetrievalWorkflowGroupID,omitempty" valid:"required~RetrievalWorkflowGroupID不能为空"` // 检索工作流的groupID。
	//  string RetrievalEntryGroupID = 7 [(trpc.go_tag) = 'valid:"required~RetrievalEntryGroupID不能为空"'];   // 检索词条的groupID。
	WorkflowReleaseTimes   map[string]string `protobuf:"bytes,8,rep,name=WorkflowReleaseTimes,proto3" json:"WorkflowReleaseTimes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 工作流发布时间（RFC3339格式，如： 2025-05-08T15:04:49+08:00），key为workflowID。
	RetrievalWorkflowModel string            `protobuf:"bytes,9,opt,name=RetrievalWorkflowModel,proto3" json:"RetrievalWorkflowModel,omitempty"`                                                                                     // 检索工作流的模型。
}

func (x *ReleaseWorkflowAppRequest) Reset() {
	*x = ReleaseWorkflowAppRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseWorkflowAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseWorkflowAppRequest) ProtoMessage() {}

func (x *ReleaseWorkflowAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseWorkflowAppRequest.ProtoReflect.Descriptor instead.
func (*ReleaseWorkflowAppRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{17}
}

func (x *ReleaseWorkflowAppRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *ReleaseWorkflowAppRequest) GetUpsertWorkflowIDs() []string {
	if x != nil {
		return x.UpsertWorkflowIDs
	}
	return nil
}

func (x *ReleaseWorkflowAppRequest) GetDeleteWorkflowIDs() []string {
	if x != nil {
		return x.DeleteWorkflowIDs
	}
	return nil
}

func (x *ReleaseWorkflowAppRequest) GetUpsertParameterIDs() []string {
	if x != nil {
		return x.UpsertParameterIDs
	}
	return nil
}

func (x *ReleaseWorkflowAppRequest) GetDeleteParameterIDs() []string {
	if x != nil {
		return x.DeleteParameterIDs
	}
	return nil
}

func (x *ReleaseWorkflowAppRequest) GetRetrievalWorkflowGroupID() string {
	if x != nil {
		return x.RetrievalWorkflowGroupID
	}
	return ""
}

func (x *ReleaseWorkflowAppRequest) GetWorkflowReleaseTimes() map[string]string {
	if x != nil {
		return x.WorkflowReleaseTimes
	}
	return nil
}

func (x *ReleaseWorkflowAppRequest) GetRetrievalWorkflowModel() string {
	if x != nil {
		return x.RetrievalWorkflowModel
	}
	return ""
}

type ReleaseWorkflowAppReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ReleaseWorkflowAppReply) Reset() {
	*x = ReleaseWorkflowAppReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseWorkflowAppReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseWorkflowAppReply) ProtoMessage() {}

func (x *ReleaseWorkflowAppReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseWorkflowAppReply.ProtoReflect.Descriptor instead.
func (*ReleaseWorkflowAppReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{18}
}

type DeleteWorkflowAppRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID string `protobuf:"bytes,1,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"` // 应用ID
}

func (x *DeleteWorkflowAppRequest) Reset() {
	*x = DeleteWorkflowAppRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWorkflowAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkflowAppRequest) ProtoMessage() {}

func (x *DeleteWorkflowAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkflowAppRequest.ProtoReflect.Descriptor instead.
func (*DeleteWorkflowAppRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteWorkflowAppRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

type DeleteWorkflowAppReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteWorkflowAppReply) Reset() {
	*x = DeleteWorkflowAppReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteWorkflowAppReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkflowAppReply) ProtoMessage() {}

func (x *DeleteWorkflowAppReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkflowAppReply.ProtoReflect.Descriptor instead.
func (*DeleteWorkflowAppReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{20}
}

type Parameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ParameterID       string          `protobuf:"bytes,1,opt,name=ParameterID,proto3" json:"ParameterID,omitempty" valid:"required~ParameterID不能为空"`                                               // 参数ID
	ParameterName     string          `protobuf:"bytes,2,opt,name=ParameterName,proto3" json:"ParameterName,omitempty" valid:"required~ParameterName不能为空"`                                           // 参数名称
	ParameterDesc     string          `protobuf:"bytes,3,opt,name=ParameterDesc,proto3" json:"ParameterDesc,omitempty"`                                           // 描述信息
	ValueType         KEP_WF.TypeEnum `protobuf:"varint,4,opt,name=ValueType,proto3,enum=trpc.KEP.bot_task_config_wf_server.TypeEnum" json:"ValueType,omitempty"` // 数据类型
	CorrectExamples   []string        `protobuf:"bytes,5,rep,name=CorrectExamples,proto3" json:"CorrectExamples,omitempty"`                                       // 正确示例
	IncorrectExamples []string        `protobuf:"bytes,6,rep,name=IncorrectExamples,proto3" json:"IncorrectExamples,omitempty"`                                   // 错误示例
	CustomAsk         string          `protobuf:"bytes,7,opt,name=CustomAsk,proto3" json:"CustomAsk,omitempty"`                                                   // 自定义询问语
}

func (x *Parameter) Reset() {
	*x = Parameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Parameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Parameter) ProtoMessage() {}

func (x *Parameter) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Parameter.ProtoReflect.Descriptor instead.
func (*Parameter) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{21}
}

func (x *Parameter) GetParameterID() string {
	if x != nil {
		return x.ParameterID
	}
	return ""
}

func (x *Parameter) GetParameterName() string {
	if x != nil {
		return x.ParameterName
	}
	return ""
}

func (x *Parameter) GetParameterDesc() string {
	if x != nil {
		return x.ParameterDesc
	}
	return ""
}

func (x *Parameter) GetValueType() KEP_WF.TypeEnum {
	if x != nil {
		return x.ValueType
	}
	return KEP_WF.TypeEnum_STRING
}

func (x *Parameter) GetCorrectExamples() []string {
	if x != nil {
		return x.CorrectExamples
	}
	return nil
}

func (x *Parameter) GetIncorrectExamples() []string {
	if x != nil {
		return x.IncorrectExamples
	}
	return nil
}

func (x *Parameter) GetCustomAsk() string {
	if x != nil {
		return x.CustomAsk
	}
	return ""
}

type UpsertParametersToSandboxRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID      string       `protobuf:"bytes,1,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"`           // 应用ID。
	Parameters []*Parameter `protobuf:"bytes,2,rep,name=Parameters,proto3" json:"Parameters,omitempty" valid:"required~Parameters不能为空"` // 参数列表。
}

func (x *UpsertParametersToSandboxRequest) Reset() {
	*x = UpsertParametersToSandboxRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertParametersToSandboxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertParametersToSandboxRequest) ProtoMessage() {}

func (x *UpsertParametersToSandboxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertParametersToSandboxRequest.ProtoReflect.Descriptor instead.
func (*UpsertParametersToSandboxRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{22}
}

func (x *UpsertParametersToSandboxRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *UpsertParametersToSandboxRequest) GetParameters() []*Parameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

type UpsertParametersToSandboxReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpsertParametersToSandboxReply) Reset() {
	*x = UpsertParametersToSandboxReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertParametersToSandboxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertParametersToSandboxReply) ProtoMessage() {}

func (x *UpsertParametersToSandboxReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertParametersToSandboxReply.ProtoReflect.Descriptor instead.
func (*UpsertParametersToSandboxReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{23}
}

type DeleteParametersInSandboxRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID        string   `protobuf:"bytes,1,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"`               // 应用ID
	ParameterIDs []string `protobuf:"bytes,2,rep,name=ParameterIDs,proto3" json:"ParameterIDs,omitempty" valid:"required~ParameterIDs不能为空"` // 参数ID
}

func (x *DeleteParametersInSandboxRequest) Reset() {
	*x = DeleteParametersInSandboxRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteParametersInSandboxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteParametersInSandboxRequest) ProtoMessage() {}

func (x *DeleteParametersInSandboxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteParametersInSandboxRequest.ProtoReflect.Descriptor instead.
func (*DeleteParametersInSandboxRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteParametersInSandboxRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *DeleteParametersInSandboxRequest) GetParameterIDs() []string {
	if x != nil {
		return x.ParameterIDs
	}
	return nil
}

type DeleteParametersInSandboxReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteParametersInSandboxReply) Reset() {
	*x = DeleteParametersInSandboxReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteParametersInSandboxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteParametersInSandboxReply) ProtoMessage() {}

func (x *DeleteParametersInSandboxReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteParametersInSandboxReply.ProtoReflect.Descriptor instead.
func (*DeleteParametersInSandboxReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{25}
}

type UpsertVariablesToSandboxRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID     string `protobuf:"bytes,1,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"` // 应用ID。
	Variables []*Var `protobuf:"bytes,2,rep,name=Variables,proto3" json:"Variables,omitempty" valid:"required~Variables不能为空"`
}

func (x *UpsertVariablesToSandboxRequest) Reset() {
	*x = UpsertVariablesToSandboxRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertVariablesToSandboxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertVariablesToSandboxRequest) ProtoMessage() {}

func (x *UpsertVariablesToSandboxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertVariablesToSandboxRequest.ProtoReflect.Descriptor instead.
func (*UpsertVariablesToSandboxRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{26}
}

func (x *UpsertVariablesToSandboxRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *UpsertVariablesToSandboxRequest) GetVariables() []*Var {
	if x != nil {
		return x.Variables
	}
	return nil
}

type Var struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VarID           string          `protobuf:"bytes,1,opt,name=VarID,proto3" json:"VarID,omitempty"`                                                           // 变量ID
	VarName         string          `protobuf:"bytes,2,opt,name=VarName,proto3" json:"VarName,omitempty"`                                                       // 变量名称
	VarDesc         string          `protobuf:"bytes,3,opt,name=VarDesc,proto3" json:"VarDesc,omitempty"`                                                       // 变量描述
	ValueType       KEP_WF.TypeEnum `protobuf:"varint,4,opt,name=ValueType,proto3,enum=trpc.KEP.bot_task_config_wf_server.TypeEnum" json:"ValueType,omitempty"` // 取值类型
	VarDefaultValue string          `protobuf:"bytes,5,opt,name=VarDefaultValue,proto3" json:"VarDefaultValue,omitempty"`                                       // 自定义变量默认值
}

func (x *Var) Reset() {
	*x = Var{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Var) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Var) ProtoMessage() {}

func (x *Var) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Var.ProtoReflect.Descriptor instead.
func (*Var) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{27}
}

func (x *Var) GetVarID() string {
	if x != nil {
		return x.VarID
	}
	return ""
}

func (x *Var) GetVarName() string {
	if x != nil {
		return x.VarName
	}
	return ""
}

func (x *Var) GetVarDesc() string {
	if x != nil {
		return x.VarDesc
	}
	return ""
}

func (x *Var) GetValueType() KEP_WF.TypeEnum {
	if x != nil {
		return x.ValueType
	}
	return KEP_WF.TypeEnum_STRING
}

func (x *Var) GetVarDefaultValue() string {
	if x != nil {
		return x.VarDefaultValue
	}
	return ""
}

type UpsertVariablesToSandboxReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpsertVariablesToSandboxReply) Reset() {
	*x = UpsertVariablesToSandboxReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpsertVariablesToSandboxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertVariablesToSandboxReply) ProtoMessage() {}

func (x *UpsertVariablesToSandboxReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertVariablesToSandboxReply.ProtoReflect.Descriptor instead.
func (*UpsertVariablesToSandboxReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{28}
}

type DeleteVariablesInSandboxRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID  string   `protobuf:"bytes,1,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"`   // 应用ID
	VarIDs []string `protobuf:"bytes,2,rep,name=VarIDs,proto3" json:"VarIDs,omitempty" valid:"required~VarIDs不能为空"` // 变量ID
}

func (x *DeleteVariablesInSandboxRequest) Reset() {
	*x = DeleteVariablesInSandboxRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteVariablesInSandboxRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVariablesInSandboxRequest) ProtoMessage() {}

func (x *DeleteVariablesInSandboxRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVariablesInSandboxRequest.ProtoReflect.Descriptor instead.
func (*DeleteVariablesInSandboxRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{29}
}

func (x *DeleteVariablesInSandboxRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *DeleteVariablesInSandboxRequest) GetVarIDs() []string {
	if x != nil {
		return x.VarIDs
	}
	return nil
}

type DeleteVariablesInSandboxReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteVariablesInSandboxReply) Reset() {
	*x = DeleteVariablesInSandboxReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteVariablesInSandboxReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVariablesInSandboxReply) ProtoMessage() {}

func (x *DeleteVariablesInSandboxReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVariablesInSandboxReply.ProtoReflect.Descriptor instead.
func (*DeleteVariablesInSandboxReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{30}
}

type DescribeWorkflowsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunEnv      RunEnvType `protobuf:"varint,1,opt,name=RunEnv,proto3,enum=trpc.KEP.bot_workflow_dm_server.RunEnvType" json:"RunEnv,omitempty"` // 运行环境
	AppID       string     `protobuf:"bytes,2,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"`                                                    // 应用ID
	WorkflowIDs []string   `protobuf:"bytes,3,rep,name=WorkflowIDs,proto3" json:"WorkflowIDs,omitempty"`                                        // 工作流ID。可为空，为空的时候的返回所有的工作流
}

func (x *DescribeWorkflowsRequest) Reset() {
	*x = DescribeWorkflowsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeWorkflowsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeWorkflowsRequest) ProtoMessage() {}

func (x *DescribeWorkflowsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeWorkflowsRequest.ProtoReflect.Descriptor instead.
func (*DescribeWorkflowsRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{31}
}

func (x *DescribeWorkflowsRequest) GetRunEnv() RunEnvType {
	if x != nil {
		return x.RunEnv
	}
	return RunEnvType_SANDBOX
}

func (x *DescribeWorkflowsRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *DescribeWorkflowsRequest) GetWorkflowIDs() []string {
	if x != nil {
		return x.WorkflowIDs
	}
	return nil
}

type DescribeWorkflowsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID     string             `protobuf:"bytes,1,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"` // 应用ID。
	Workflows []*KEP_WF.Workflow `protobuf:"bytes,2,rep,name=Workflows,proto3" json:"Workflows,omitempty"`
}

func (x *DescribeWorkflowsReply) Reset() {
	*x = DescribeWorkflowsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeWorkflowsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeWorkflowsReply) ProtoMessage() {}

func (x *DescribeWorkflowsReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeWorkflowsReply.ProtoReflect.Descriptor instead.
func (*DescribeWorkflowsReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{32}
}

func (x *DescribeWorkflowsReply) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *DescribeWorkflowsReply) GetWorkflows() []*KEP_WF.Workflow {
	if x != nil {
		return x.Workflows
	}
	return nil
}

type RetrieveWorkflowsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunEnv       RunEnvType `protobuf:"varint,1,opt,name=RunEnv,proto3,enum=trpc.KEP.bot_workflow_dm_server.RunEnvType" json:"RunEnv,omitempty"` // 运行环境
	AppID        string     `protobuf:"bytes,2,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"`                                                    // 应用ID
	RewriteQuery string     `protobuf:"bytes,3,opt,name=RewriteQuery,proto3" json:"RewriteQuery,omitempty"`                                      // 对话改写后query的内容，用来检索topN的工作流。
}

func (x *RetrieveWorkflowsRequest) Reset() {
	*x = RetrieveWorkflowsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveWorkflowsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveWorkflowsRequest) ProtoMessage() {}

func (x *RetrieveWorkflowsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveWorkflowsRequest.ProtoReflect.Descriptor instead.
func (*RetrieveWorkflowsRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{33}
}

func (x *RetrieveWorkflowsRequest) GetRunEnv() RunEnvType {
	if x != nil {
		return x.RunEnv
	}
	return RunEnvType_SANDBOX
}

func (x *RetrieveWorkflowsRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *RetrieveWorkflowsRequest) GetRewriteQuery() string {
	if x != nil {
		return x.RewriteQuery
	}
	return ""
}

type RetrieveWorkflowsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Workflows []*RetrieveWorkflow `protobuf:"bytes,1,rep,name=Workflows,proto3" json:"Workflows,omitempty"`
}

func (x *RetrieveWorkflowsReply) Reset() {
	*x = RetrieveWorkflowsReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveWorkflowsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveWorkflowsReply) ProtoMessage() {}

func (x *RetrieveWorkflowsReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveWorkflowsReply.ProtoReflect.Descriptor instead.
func (*RetrieveWorkflowsReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{34}
}

func (x *RetrieveWorkflowsReply) GetWorkflows() []*RetrieveWorkflow {
	if x != nil {
		return x.Workflows
	}
	return nil
}

type RetrieveWorkflow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkflowID   string   `protobuf:"bytes,1,opt,name=WorkflowID,proto3" json:"WorkflowID,omitempty"`     // 工作流ID
	WorkflowName string   `protobuf:"bytes,2,opt,name=WorkflowName,proto3" json:"WorkflowName,omitempty"` // 工作流名称
	WorkflowDesc string   `protobuf:"bytes,3,opt,name=WorkflowDesc,proto3" json:"WorkflowDesc,omitempty"` // 工作流描述
	Examples     []string `protobuf:"bytes,4,rep,name=Examples,proto3" json:"Examples,omitempty"`         // 示例问法
	Confidence   float32  `protobuf:"fixed32,5,opt,name=Confidence,proto3" json:"Confidence,omitempty"`   // 置信度
}

func (x *RetrieveWorkflow) Reset() {
	*x = RetrieveWorkflow{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetrieveWorkflow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveWorkflow) ProtoMessage() {}

func (x *RetrieveWorkflow) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveWorkflow.ProtoReflect.Descriptor instead.
func (*RetrieveWorkflow) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{35}
}

func (x *RetrieveWorkflow) GetWorkflowID() string {
	if x != nil {
		return x.WorkflowID
	}
	return ""
}

func (x *RetrieveWorkflow) GetWorkflowName() string {
	if x != nil {
		return x.WorkflowName
	}
	return ""
}

func (x *RetrieveWorkflow) GetWorkflowDesc() string {
	if x != nil {
		return x.WorkflowDesc
	}
	return ""
}

func (x *RetrieveWorkflow) GetExamples() []string {
	if x != nil {
		return x.Examples
	}
	return nil
}

func (x *RetrieveWorkflow) GetConfidence() float32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

type DescribeParametersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RunEnv       RunEnvType `protobuf:"varint,1,opt,name=RunEnv,proto3,enum=trpc.KEP.bot_workflow_dm_server.RunEnvType" json:"RunEnv,omitempty"` // 运行环境
	AppID        string     `protobuf:"bytes,2,opt,name=AppID,proto3" json:"AppID,omitempty" valid:"required~AppID不能为空"`                                                    // 应用ID
	ParameterIDs []string   `protobuf:"bytes,3,rep,name=ParameterIDs,proto3" json:"ParameterIDs,omitempty"`                                      // 参数ID。可为空，为空的时候的返回所有的参数
}

func (x *DescribeParametersRequest) Reset() {
	*x = DescribeParametersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeParametersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeParametersRequest) ProtoMessage() {}

func (x *DescribeParametersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeParametersRequest.ProtoReflect.Descriptor instead.
func (*DescribeParametersRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{36}
}

func (x *DescribeParametersRequest) GetRunEnv() RunEnvType {
	if x != nil {
		return x.RunEnv
	}
	return RunEnvType_SANDBOX
}

func (x *DescribeParametersRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *DescribeParametersRequest) GetParameterIDs() []string {
	if x != nil {
		return x.ParameterIDs
	}
	return nil
}

type DescribeParametersReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Parameters map[string]*Parameter `protobuf:"bytes,1,rep,name=Parameters,proto3" json:"Parameters,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 参数信息。 key：ParameterID
}

func (x *DescribeParametersReply) Reset() {
	*x = DescribeParametersReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeParametersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeParametersReply) ProtoMessage() {}

func (x *DescribeParametersReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeParametersReply.ProtoReflect.Descriptor instead.
func (*DescribeParametersReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{37}
}

func (x *DescribeParametersReply) GetParameters() map[string]*Parameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

type DebugWorkflowNodeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppID     string            `protobuf:"bytes,1,opt,name=AppID,proto3" json:"AppID,omitempty"`                                                                                           // 应用ID
	NodeJSON  string            `protobuf:"bytes,2,opt,name=NodeJSON,proto3" json:"NodeJSON,omitempty"`                                                                                     // 节点数据
	Inputs    map[string]string `protobuf:"bytes,3,rep,name=Inputs,proto3" json:"Inputs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 输入参数
	ToolInput *ToolInputData    `protobuf:"bytes,4,opt,name=ToolInput,proto3" json:"ToolInput,omitempty"`                                                                                   // 工具节点输入参数
}

func (x *DebugWorkflowNodeRequest) Reset() {
	*x = DebugWorkflowNodeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugWorkflowNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugWorkflowNodeRequest) ProtoMessage() {}

func (x *DebugWorkflowNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugWorkflowNodeRequest.ProtoReflect.Descriptor instead.
func (*DebugWorkflowNodeRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{38}
}

func (x *DebugWorkflowNodeRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *DebugWorkflowNodeRequest) GetNodeJSON() string {
	if x != nil {
		return x.NodeJSON
	}
	return ""
}

func (x *DebugWorkflowNodeRequest) GetInputs() map[string]string {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *DebugWorkflowNodeRequest) GetToolInput() *ToolInputData {
	if x != nil {
		return x.ToolInput
	}
	return nil
}

type ToolInputData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header string `protobuf:"bytes,1,opt,name=Header,proto3" json:"Header,omitempty"` // API节点头信息JSON文本
	Query  string `protobuf:"bytes,2,opt,name=Query,proto3" json:"Query,omitempty"`   // API节点URL参数JSON文本
	Body   string `protobuf:"bytes,3,opt,name=Body,proto3" json:"Body,omitempty"`     // API或代码节点输入参数JSON文本
}

func (x *ToolInputData) Reset() {
	*x = ToolInputData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ToolInputData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ToolInputData) ProtoMessage() {}

func (x *ToolInputData) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ToolInputData.ProtoReflect.Descriptor instead.
func (*ToolInputData) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{39}
}

func (x *ToolInputData) GetHeader() string {
	if x != nil {
		return x.Header
	}
	return ""
}

func (x *ToolInputData) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *ToolInputData) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

type DebugWorkflowNodeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     int32        `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`        // 0表示成功。
	Message  string       `protobuf:"bytes,2,opt,name=Message,proto3" json:"Message,omitempty"`   // 错误原因
	NodeData *RunNodeInfo `protobuf:"bytes,3,opt,name=NodeData,proto3" json:"NodeData,omitempty"` // 返回的节点信息
}

func (x *DebugWorkflowNodeReply) Reset() {
	*x = DebugWorkflowNodeReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugWorkflowNodeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugWorkflowNodeReply) ProtoMessage() {}

func (x *DebugWorkflowNodeReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugWorkflowNodeReply.ProtoReflect.Descriptor instead.
func (*DebugWorkflowNodeReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{40}
}

func (x *DebugWorkflowNodeReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DebugWorkflowNodeReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DebugWorkflowNodeReply) GetNodeData() *RunNodeInfo {
	if x != nil {
		return x.NodeData
	}
	return nil
}

type DebugWorkflowNodeDialogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 请求类型，包含运行、停止
	RequestType RequestType `protobuf:"varint,1,opt,name=RequestType,proto3,enum=trpc.KEP.bot_workflow_dm_server.RequestType" json:"RequestType,omitempty"`
	// 应用ID
	AppID string `protobuf:"bytes,2,opt,name=AppID,proto3" json:"AppID,omitempty"`
	// 节点数据
	NodeJSON string `protobuf:"bytes,3,opt,name=NodeJSON,proto3" json:"NodeJSON,omitempty"`
	// 输入变量
	Inputs map[string]string `protobuf:"bytes,4,rep,name=Inputs,proto3" json:"Inputs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 会话ID。
	SessionID string `protobuf:"bytes,5,opt,name=SessionID,proto3" json:"SessionID,omitempty"`
	// 用户输入
	Query string `protobuf:"bytes,6,opt,name=Query,proto3" json:"Query,omitempty"`
	// 配置的对话历史
	ConfiguredHistory string `protobuf:"bytes,7,opt,name=ConfiguredHistory,proto3" json:"ConfiguredHistory,omitempty"`
	// 对话历史原始的内容
	QueryHistory []*Message `protobuf:"bytes,8,rep,name=QueryHistory,proto3" json:"QueryHistory,omitempty"`
	// query对应的recordID
	RelatedRecordID string `protobuf:"bytes,9,opt,name=RelatedRecordID,proto3" json:"RelatedRecordID,omitempty"`
	// query的回复对应的recordID
	RecordID string `protobuf:"bytes,10,opt,name=RecordID,proto3" json:"RecordID,omitempty"`
	// 主模型名称
	MainModelName string `protobuf:"bytes,11,opt,name=MainModelName,proto3" json:"MainModelName,omitempty"`
	// 改写后的query
	RewriteQuery string `protobuf:"bytes,12,opt,name=RewriteQuery,proto3" json:"RewriteQuery,omitempty"`
	// API参数的值，用来给子工作流使用的
	CustomVariables map[string]*Variable `protobuf:"bytes,13,rep,name=CustomVariables,proto3" json:"CustomVariables,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 自定义参数
}

func (x *DebugWorkflowNodeDialogRequest) Reset() {
	*x = DebugWorkflowNodeDialogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugWorkflowNodeDialogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugWorkflowNodeDialogRequest) ProtoMessage() {}

func (x *DebugWorkflowNodeDialogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugWorkflowNodeDialogRequest.ProtoReflect.Descriptor instead.
func (*DebugWorkflowNodeDialogRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{41}
}

func (x *DebugWorkflowNodeDialogRequest) GetRequestType() RequestType {
	if x != nil {
		return x.RequestType
	}
	return RequestType_RUN
}

func (x *DebugWorkflowNodeDialogRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *DebugWorkflowNodeDialogRequest) GetNodeJSON() string {
	if x != nil {
		return x.NodeJSON
	}
	return ""
}

func (x *DebugWorkflowNodeDialogRequest) GetInputs() map[string]string {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *DebugWorkflowNodeDialogRequest) GetSessionID() string {
	if x != nil {
		return x.SessionID
	}
	return ""
}

func (x *DebugWorkflowNodeDialogRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *DebugWorkflowNodeDialogRequest) GetConfiguredHistory() string {
	if x != nil {
		return x.ConfiguredHistory
	}
	return ""
}

func (x *DebugWorkflowNodeDialogRequest) GetQueryHistory() []*Message {
	if x != nil {
		return x.QueryHistory
	}
	return nil
}

func (x *DebugWorkflowNodeDialogRequest) GetRelatedRecordID() string {
	if x != nil {
		return x.RelatedRecordID
	}
	return ""
}

func (x *DebugWorkflowNodeDialogRequest) GetRecordID() string {
	if x != nil {
		return x.RecordID
	}
	return ""
}

func (x *DebugWorkflowNodeDialogRequest) GetMainModelName() string {
	if x != nil {
		return x.MainModelName
	}
	return ""
}

func (x *DebugWorkflowNodeDialogRequest) GetRewriteQuery() string {
	if x != nil {
		return x.RewriteQuery
	}
	return ""
}

func (x *DebugWorkflowNodeDialogRequest) GetCustomVariables() map[string]*Variable {
	if x != nil {
		return x.CustomVariables
	}
	return nil
}

type DebugWorkflowNodeDialogReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      int32    `protobuf:"varint,1,opt,name=Code,proto3" json:"Code,omitempty"`          // 0表示成功。 只要服务端的stream回复过一条消息后，error就没有作用了，只能通过code/message返回错误原因
	Message   string   `protobuf:"bytes,2,opt,name=Message,proto3" json:"Message,omitempty"`     // 错误原因
	SessionID string   `protobuf:"bytes,3,opt,name=SessionID,proto3" json:"SessionID,omitempty"` // 会话ID
	IsFinal   bool     `protobuf:"varint,4,opt,name=IsFinal,proto3" json:"IsFinal,omitempty"`    // 当前消息是否最后一条
	Respond   *Respond `protobuf:"bytes,5,opt,name=Respond,proto3" json:"Respond,omitempty"`     // 正常非空。
	// LLM统计信息。
	StatisticInfos []*StatisticInfo `protobuf:"bytes,100,rep,name=StatisticInfos,proto3" json:"StatisticInfos,omitempty"`
}

func (x *DebugWorkflowNodeDialogReply) Reset() {
	*x = DebugWorkflowNodeDialogReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugWorkflowNodeDialogReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugWorkflowNodeDialogReply) ProtoMessage() {}

func (x *DebugWorkflowNodeDialogReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugWorkflowNodeDialogReply.ProtoReflect.Descriptor instead.
func (*DebugWorkflowNodeDialogReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{42}
}

func (x *DebugWorkflowNodeDialogReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DebugWorkflowNodeDialogReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DebugWorkflowNodeDialogReply) GetSessionID() string {
	if x != nil {
		return x.SessionID
	}
	return ""
}

func (x *DebugWorkflowNodeDialogReply) GetIsFinal() bool {
	if x != nil {
		return x.IsFinal
	}
	return false
}

func (x *DebugWorkflowNodeDialogReply) GetRespond() *Respond {
	if x != nil {
		return x.Respond
	}
	return nil
}

func (x *DebugWorkflowNodeDialogReply) GetStatisticInfos() []*StatisticInfo {
	if x != nil {
		return x.StatisticInfos
	}
	return nil
}

type StartWorkflowRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主账号Uin
	Uin string `protobuf:"bytes,1,opt,name=Uin,proto3" json:"Uin,omitempty"`
	// 工作流运行实例ID
	WorkflowRunID string `protobuf:"bytes,2,opt,name=WorkflowRunID,proto3" json:"WorkflowRunID,omitempty"`
	// 运行环境。
	RunEnv RunEnvType `protobuf:"varint,3,opt,name=RunEnv,proto3,enum=trpc.KEP.bot_workflow_dm_server.RunEnvType" json:"RunEnv,omitempty"`
	// 工作流ID
	WorkflowID string `protobuf:"bytes,4,opt,name=WorkflowID,proto3" json:"WorkflowID,omitempty"`
	// 应用ID
	AppID string `protobuf:"bytes,5,opt,name=AppID,proto3" json:"AppID,omitempty"`
	// 当前的query。
	Query string `protobuf:"bytes,6,opt,name=Query,proto3" json:"Query,omitempty"`
	// 主模型名称
	MainModelName string `protobuf:"bytes,7,opt,name=MainModelName,proto3" json:"MainModelName,omitempty"`
	// API参数
	CustomVariables map[string]string `protobuf:"bytes,8,rep,name=CustomVariables,proto3" json:"CustomVariables,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 是否为调试
	IsDebug bool `protobuf:"varint,30,opt,name=IsDebug,proto3" json:"IsDebug,omitempty"`
}

func (x *StartWorkflowRunRequest) Reset() {
	*x = StartWorkflowRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartWorkflowRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartWorkflowRunRequest) ProtoMessage() {}

func (x *StartWorkflowRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartWorkflowRunRequest.ProtoReflect.Descriptor instead.
func (*StartWorkflowRunRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{43}
}

func (x *StartWorkflowRunRequest) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *StartWorkflowRunRequest) GetWorkflowRunID() string {
	if x != nil {
		return x.WorkflowRunID
	}
	return ""
}

func (x *StartWorkflowRunRequest) GetRunEnv() RunEnvType {
	if x != nil {
		return x.RunEnv
	}
	return RunEnvType_SANDBOX
}

func (x *StartWorkflowRunRequest) GetWorkflowID() string {
	if x != nil {
		return x.WorkflowID
	}
	return ""
}

func (x *StartWorkflowRunRequest) GetAppID() string {
	if x != nil {
		return x.AppID
	}
	return ""
}

func (x *StartWorkflowRunRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *StartWorkflowRunRequest) GetMainModelName() string {
	if x != nil {
		return x.MainModelName
	}
	return ""
}

func (x *StartWorkflowRunRequest) GetCustomVariables() map[string]string {
	if x != nil {
		return x.CustomVariables
	}
	return nil
}

func (x *StartWorkflowRunRequest) GetIsDebug() bool {
	if x != nil {
		return x.IsDebug
	}
	return false
}

type StartWorkflowRunReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 是否已经运行中
	IsAlreadyRunning bool `protobuf:"varint,1,opt,name=IsAlreadyRunning,proto3" json:"IsAlreadyRunning,omitempty"`
}

func (x *StartWorkflowRunReply) Reset() {
	*x = StartWorkflowRunReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartWorkflowRunReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartWorkflowRunReply) ProtoMessage() {}

func (x *StartWorkflowRunReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartWorkflowRunReply.ProtoReflect.Descriptor instead.
func (*StartWorkflowRunReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{44}
}

func (x *StartWorkflowRunReply) GetIsAlreadyRunning() bool {
	if x != nil {
		return x.IsAlreadyRunning
	}
	return false
}

type StopWorkflowRunRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 工作流运行实例ID
	WorkflowRunID string `protobuf:"bytes,1,opt,name=WorkflowRunID,proto3" json:"WorkflowRunID,omitempty"`
}

func (x *StopWorkflowRunRequest) Reset() {
	*x = StopWorkflowRunRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopWorkflowRunRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopWorkflowRunRequest) ProtoMessage() {}

func (x *StopWorkflowRunRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopWorkflowRunRequest.ProtoReflect.Descriptor instead.
func (*StopWorkflowRunRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{45}
}

func (x *StopWorkflowRunRequest) GetWorkflowRunID() string {
	if x != nil {
		return x.WorkflowRunID
	}
	return ""
}

type StopWorkflowRunReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *StopWorkflowRunReply) Reset() {
	*x = StopWorkflowRunReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopWorkflowRunReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopWorkflowRunReply) ProtoMessage() {}

func (x *StopWorkflowRunReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopWorkflowRunReply.ProtoReflect.Descriptor instead.
func (*StopWorkflowRunReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{46}
}

type DescribeWorkflowRunNumRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 主账号Uin， 如果为空，则返回所有主账号的运行中的工作流数量。
	Uin string `protobuf:"bytes,1,opt,name=Uin,proto3" json:"Uin,omitempty"`
}

func (x *DescribeWorkflowRunNumRequest) Reset() {
	*x = DescribeWorkflowRunNumRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeWorkflowRunNumRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeWorkflowRunNumRequest) ProtoMessage() {}

func (x *DescribeWorkflowRunNumRequest) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeWorkflowRunNumRequest.ProtoReflect.Descriptor instead.
func (*DescribeWorkflowRunNumRequest) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{47}
}

func (x *DescribeWorkflowRunNumRequest) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

type DescribeWorkflowRunNumReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UinInfos map[string]*UinInfo `protobuf:"bytes,1,rep,name=UinInfos,proto3" json:"UinInfos,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DescribeWorkflowRunNumReply) Reset() {
	*x = DescribeWorkflowRunNumReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeWorkflowRunNumReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeWorkflowRunNumReply) ProtoMessage() {}

func (x *DescribeWorkflowRunNumReply) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeWorkflowRunNumReply.ProtoReflect.Descriptor instead.
func (*DescribeWorkflowRunNumReply) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{48}
}

func (x *DescribeWorkflowRunNumReply) GetUinInfos() map[string]*UinInfo {
	if x != nil {
		return x.UinInfos
	}
	return nil
}

type UinInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 运行中的工作流数量
	RunningNum int32 `protobuf:"varint,1,opt,name=RunningNum,proto3" json:"RunningNum,omitempty"`
}

func (x *UinInfo) Reset() {
	*x = UinInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_workflow_dm_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UinInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UinInfo) ProtoMessage() {}

func (x *UinInfo) ProtoReflect() protoreflect.Message {
	mi := &file_workflow_dm_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UinInfo.ProtoReflect.Descriptor instead.
func (*UinInfo) Descriptor() ([]byte, []int) {
	return file_workflow_dm_proto_rawDescGZIP(), []int{49}
}

func (x *UinInfo) GetRunningNum() int32 {
	if x != nil {
		return x.RunningNum
	}
	return 0
}

var File_workflow_dm_proto protoreflect.FileDescriptor

var file_workflow_dm_proto_rawDesc = []byte{
	0x0a, 0x11, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2d, 0x64, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x1a, 0x0a, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x25, 0x62, 0x6f, 0x74, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfe, 0x09, 0x0a, 0x12, 0x52, 0x75, 0x6e, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x47,
	0x0a, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x29, 0x92, 0xb8, 0x18, 0x25, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0xe4, 0xbc, 0x9a, 0xe8, 0xaf, 0x9d, 0x49, 0x44, 0xe4,
	0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x09, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x43, 0x0a, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e,
	0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x12, 0x4e, 0x0a, 0x0b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x05,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x29, 0x92, 0xb8, 0x18,
	0x25, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x7e, 0xe5, 0xba, 0x94, 0xe7, 0x94, 0xa8, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4,
	0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1e, 0x0a,
	0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x12, 0x14, 0x0a,
	0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x12, 0x4c, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x0c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x79, 0x12, 0x72, 0x0a, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x48, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x44, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x44, 0x12,
	0x1a, 0x0a, 0x08, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x44, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x49,
	0x73, 0x44, 0x65, 0x62, 0x75, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x49, 0x73,
	0x44, 0x65, 0x62, 0x75, 0x67, 0x12, 0x24, 0x0a, 0x0d, 0x4d, 0x61, 0x69, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4d, 0x61,
	0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x57, 0x0a, 0x06, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75,
	0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x65, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x52, 0x0a, 0x0b, 0x56, 0x65, 0x72, 0x62,
	0x6f, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x56, 0x65, 0x72, 0x62, 0x6f, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x56, 0x65, 0x72, 0x62, 0x6f, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x11,
	0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x75, 0x62, 0x42, 0x69, 0x7a, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x53, 0x75, 0x62, 0x42, 0x69, 0x7a, 0x54, 0x79, 0x70, 0x65, 0x12, 0x63, 0x0a, 0x0a, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x43,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x52, 0x75, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0a, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x1a,
	0x6d, 0x0a, 0x14, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3f, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x39,
	0x0a, 0x0b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f, 0x42, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7a, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x04, 0x52, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x25, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x04, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x49, 0x44, 0x22, 0x34, 0x0a, 0x08, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x94, 0x02, 0x0a, 0x10, 0x52,
	0x75, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x49,
	0x73, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x49, 0x73,
	0x46, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x42, 0x0a, 0x07, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64,
	0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64,
	0x52, 0x07, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x12, 0x56, 0x0a, 0x0e, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x64, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x22, 0xd5, 0x06, 0x0a, 0x07, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x12, 0x40, 0x0a,
	0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x0a, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x52, 0x75, 0x6e, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x57, 0x0a, 0x0e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x08, 0x52, 0x75, 0x6e, 0x4e,
	0x6f, 0x64, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e,
	0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x52, 0x75, 0x6e, 0x4e, 0x6f, 0x64,
	0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64,
	0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x61, 0x72, 0x64, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4e, 0x0a, 0x0b, 0x54, 0x68, 0x6f, 0x75, 0x67, 0x68,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x68,
	0x6f, 0x75, 0x67, 0x68, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x54, 0x68, 0x6f, 0x75, 0x67,
	0x68, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2e, 0x0a, 0x12, 0x48, 0x69, 0x74, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x12, 0x48, 0x69, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x72,
	0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x5e, 0x0a, 0x0c, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x2e, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x30, 0x0a, 0x13, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x3f, 0x0a, 0x11, 0x41, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x9b, 0x01, 0x0a, 0x0b, 0x54, 0x68,
	0x6f, 0x75, 0x67, 0x68, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4e,
	0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e,
	0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xbb, 0x01, 0x0a, 0x09, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x44, 0x6f, 0x63, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x44, 0x6f, 0x63, 0x49, 0x44, 0x12, 0x0e, 0x0a, 0x02, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49,
	0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x44, 0x6f, 0x63, 0x42, 0x69, 0x7a, 0x49,
	0x44, 0x12, 0x18, 0x0a, 0x07, 0x44, 0x6f, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x44, 0x6f, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x51,
	0x41, 0x42, 0x69, 0x7a, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x51, 0x41,
	0x42, 0x69, 0x7a, 0x49, 0x44, 0x22, 0xb7, 0x06, 0x0a, 0x0b, 0x52, 0x75, 0x6e, 0x4e, 0x6f, 0x64,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x12, 0x48, 0x0a,
	0x08, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x4e,
	0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x4f, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x47, 0x0a, 0x10, 0x43, 0x6f, 0x73, 0x74, 0x4d, 0x69, 0x6c, 0x6c,
	0x69, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x1b,
	0x92, 0xb8, 0x18, 0x17, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x22, 0x43, 0x6f, 0x73, 0x74, 0x4d, 0x69,
	0x6c, 0x6c, 0x69, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x22, 0x52, 0x10, 0x43, 0x6f, 0x73,
	0x74, 0x4d, 0x69, 0x6c, 0x6c, 0x69, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x42, 0x65, 0x6c, 0x6f, 0x6e, 0x67, 0x4e, 0x6f, 0x64,
	0x65, 0x49, 0x44, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x42, 0x65, 0x6c, 0x6f, 0x6e,
	0x67, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x73, 0x43, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x49, 0x73, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x46, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x64,
	0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x46, 0x61, 0x69, 0x6c, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x4c, 0x6f, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x4c, 0x6f, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x66, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x66, 0x12, 0x1a, 0x0a, 0x08, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x66, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x66, 0x12, 0x1c, 0x0a, 0x09, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x52, 0x65, 0x66, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x52, 0x65, 0x66, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x61, 0x73, 0x6b, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x52, 0x65, 0x66, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x54, 0x61,
	0x73, 0x6b, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x65, 0x66, 0x12, 0x56, 0x0a, 0x0e, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x64, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x22, 0x4a, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x4e, 0x49, 0x54, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x52,
	0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x03, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x22,
	0x91, 0x03, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x32, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x14, 0x92, 0xb8, 0x18, 0x10, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x22,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x52, 0x09, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x19, 0x92,
	0xb8, 0x18, 0x15, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x22, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x73, 0x74, 0x22, 0x52, 0x0e, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x09, 0x54, 0x6f, 0x74, 0x61,
	0x6c, 0x43, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x14, 0x92, 0xb8, 0x18,
	0x10, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x22, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74,
	0x22, 0x52, 0x09, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x0b,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x16, 0x92, 0xb8, 0x18, 0x12, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x22, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22, 0x52, 0x0b, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x17, 0x92, 0xb8,
	0x18, 0x13, 0x6a, 0x73, 0x6f, 0x6e, 0x3a, 0x22, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x22, 0x52, 0x0c, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x73, 0x12, 0x38, 0x0a, 0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x16, 0x92, 0xb8, 0x18, 0x12, 0x6a, 0x73,
	0x6f, 0x6e, 0x3a, 0x22, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22,
	0x52, 0x0b, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x24, 0x0a,
	0x0d, 0x49, 0x73, 0x53, 0x75, 0x62, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x49, 0x73, 0x53, 0x75, 0x62, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x22, 0xe4, 0x01, 0x0a, 0x13, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x09, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x29,
	0x92, 0xb8, 0x18, 0x25, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69,
	0x72, 0x65, 0x64, 0x7e, 0xe4, 0xbc, 0x9a, 0xe8, 0xaf, 0x9d, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8,
	0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x44, 0x12, 0x43, 0x0a, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x12, 0x3f, 0x0a, 0x05, 0x41, 0x70, 0x70,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x29, 0x92, 0xb8, 0x18, 0x25, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0xe5, 0xba,
	0x94, 0xe7, 0x94, 0xa8, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7,
	0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x22, 0x13, 0x0a, 0x11, 0x43, 0x6c,
	0x65, 0x61, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0xcd, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x53,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a,
	0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92, 0xb8,
	0x18, 0x22, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x7e, 0x41, 0x70, 0x70, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba,
	0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x3a, 0x0a, 0x18, 0x52,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x52,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x44, 0x12, 0x36, 0x0a, 0x16, 0x52, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x61, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x61, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22,
	0x19, 0x0a, 0x17, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x53, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xd3, 0x01, 0x0a, 0x1e, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x6f, 0x53,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a,
	0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92, 0xb8,
	0x18, 0x22, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x7e, 0x41, 0x70, 0x70, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba,
	0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x73, 0x0a, 0x08, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x42, 0x29, 0x92, 0xb8, 0x18,
	0x25, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x7e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4,
	0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x08, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x22, 0x1e, 0x0a, 0x1c, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0xaf, 0x01, 0x0a, 0x1f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x73, 0x49, 0x6e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x26, 0x92, 0xb8, 0x18, 0x22, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22,
	0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0x41, 0x70, 0x70, 0x49, 0x44, 0xe4, 0xb8,
	0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70,
	0x49, 0x44, 0x12, 0x4e, 0x0a, 0x0b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x2c, 0x92, 0xb8, 0x18, 0x28, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x73, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8,
	0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x0b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49,
	0x44, 0x73, 0x22, 0x1f, 0x0a, 0x1d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x73, 0x49, 0x6e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x98, 0x05, 0x0a, 0x19, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3c, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x26, 0x92, 0xb8, 0x18, 0x22, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0x41, 0x70, 0x70, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83,
	0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12,
	0x2c, 0x0a, 0x11, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x49, 0x44, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x73, 0x12, 0x2c, 0x0a,
	0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49,
	0x44, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x55,
	0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49, 0x44,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49, 0x44, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49, 0x44,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49, 0x44, 0x73, 0x12, 0x75, 0x0a, 0x18, 0x52,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x39, 0x92,
	0xb8, 0x18, 0x35, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x7e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83,
	0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x18, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x61, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x44, 0x12, 0x88, 0x01, 0x0a, 0x14, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x54, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x14, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x36, 0x0a,
	0x16, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x52,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x61, 0x6c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x47, 0x0a, 0x19, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x19,
	0x0a, 0x17, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x41, 0x70, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x58, 0x0a, 0x18, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92, 0xb8, 0x18, 0x22, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a,
	0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0x41, 0x70, 0x70, 0x49, 0x44, 0xe4,
	0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70,
	0x70, 0x49, 0x44, 0x22, 0x18, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x99, 0x03,
	0x0a, 0x09, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x0b, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2c, 0x92, 0xb8, 0x18, 0x28, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49,
	0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x0b,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49, 0x44, 0x12, 0x54, 0x0a, 0x0d, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2e, 0x92, 0xb8, 0x18, 0x2a, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9,
	0xba, 0x22, 0x52, 0x0d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x44, 0x65,
	0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x12, 0x4a, 0x0a, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x45, 0x78,
	0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x43, 0x6f,
	0x72, 0x72, 0x65, 0x63, 0x74, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x12, 0x2c, 0x0a,
	0x11, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72, 0x65, 0x63, 0x74, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11, 0x49, 0x6e, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x63, 0x74, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x73, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x41, 0x73, 0x6b, 0x22, 0xd9, 0x01, 0x0a, 0x20, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x54, 0x6f,
	0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c,
	0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92,
	0xb8, 0x18, 0x22, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x7e, 0x41, 0x70, 0x70, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8,
	0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x77, 0x0a, 0x0a,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x42, 0x2b, 0x92, 0xb8,
	0x18, 0x27, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x7e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0xe4, 0xb8, 0x8d, 0xe8,
	0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x22, 0x20, 0x0a, 0x1e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64, 0x62,
	0x6f, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xb3, 0x01, 0x0a, 0x20, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x05,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92, 0xb8, 0x18,
	0x22, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64,
	0x7e, 0x41, 0x70, 0x70, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7,
	0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x51, 0x0a, 0x0c, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49, 0x44, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x42, 0x2d, 0x92, 0xb8, 0x18, 0x29, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49,
	0x44, 0x73, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52,
	0x0c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49, 0x44, 0x73, 0x22, 0x20, 0x0a,
	0x1e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x49, 0x6e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0xcf, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x26, 0x92, 0xb8, 0x18, 0x22, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0x41, 0x70, 0x70, 0x49, 0x44, 0xe4, 0xb8, 0x8d,
	0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49,
	0x44, 0x12, 0x6e, 0x0a, 0x09, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x56, 0x61, 0x72, 0x42, 0x2a, 0x92, 0xb8, 0x18, 0x26,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4,
	0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x09, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65,
	0x73, 0x22, 0xc5, 0x01, 0x0a, 0x03, 0x56, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x56, 0x61, 0x72,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x56, 0x61, 0x72, 0x49, 0x44, 0x12,
	0x18, 0x0a, 0x07, 0x56, 0x61, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x56, 0x61, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x56, 0x61, 0x72,
	0x44, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x56, 0x61, 0x72, 0x44,
	0x65, 0x73, 0x63, 0x12, 0x4a, 0x0a, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x09, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x28, 0x0a, 0x0f, 0x56, 0x61, 0x72, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x56, 0x61, 0x72, 0x44, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x1f, 0x0a, 0x1d, 0x55, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x54, 0x6f, 0x53, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xa0, 0x01, 0x0a, 0x1f, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x49, 0x6e,
	0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c,
	0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92,
	0xb8, 0x18, 0x22, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72,
	0x65, 0x64, 0x7e, 0x41, 0x70, 0x70, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8,
	0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x3f, 0x0a, 0x06,
	0x56, 0x61, 0x72, 0x49, 0x44, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42, 0x27, 0x92, 0xb8,
	0x18, 0x23, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x7e, 0x56, 0x61, 0x72, 0x49, 0x44, 0x73, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8,
	0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x06, 0x56, 0x61, 0x72, 0x49, 0x44, 0x73, 0x22, 0x1f, 0x0a,
	0x1d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x49, 0x6e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xbf,
	0x01, 0x0a, 0x18, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x06, 0x52,
	0x75, 0x6e, 0x45, 0x6e, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75,
	0x6e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76,
	0x12, 0x3c, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x26, 0x92, 0xb8, 0x18, 0x22, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x7e, 0x41, 0x70, 0x70, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd,
	0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x20,
	0x0a, 0x0b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0b, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x73,
	0x22, 0xa2, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3c, 0x0a, 0x05, 0x41,
	0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92, 0xb8, 0x18, 0x22,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e,
	0x41, 0x70, 0x70, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9,
	0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x4a, 0x0a, 0x09, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x77, 0x66, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x09, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x73, 0x22, 0xc1, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65,
	0x76, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x43, 0x0a, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x12, 0x3c, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0x92, 0xb8, 0x18, 0x22, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x7e, 0x41, 0x70, 0x70, 0x49,
	0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd, 0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05,
	0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x65, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x22, 0x69, 0x0a, 0x16, 0x52, 0x65, 0x74,
	0x72, 0x69, 0x65, 0x76, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x4f, 0x0a, 0x09, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64,
	0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x09, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x73, 0x22, 0xb6, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x73,
	0x63, 0x12, 0x1a, 0x0a, 0x08, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x08, 0x45, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x12, 0x1e, 0x0a,
	0x0a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0a, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x22, 0xc2, 0x01,
	0x0a, 0x19, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65,
	0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x06, 0x52,
	0x75, 0x6e, 0x45, 0x6e, 0x76, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75,
	0x6e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76,
	0x12, 0x3c, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x26, 0x92, 0xb8, 0x18, 0x22, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x3a, 0x22, 0x72, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x64, 0x7e, 0x41, 0x70, 0x70, 0x49, 0x44, 0xe4, 0xb8, 0x8d, 0xe8, 0x83, 0xbd,
	0xe4, 0xb8, 0xba, 0xe7, 0xa9, 0xba, 0x22, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x22,
	0x0a, 0x0c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49, 0x44, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x49,
	0x44, 0x73, 0x22, 0xee, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x68,
	0x0a, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x48, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x69, 0x0a, 0x0f, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x40, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xb4, 0x02, 0x0a, 0x18, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x4a, 0x53,
	0x4f, 0x4e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x4a, 0x53,
	0x4f, 0x4e, 0x12, 0x5d, 0x0a, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x45, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x12, 0x4c, 0x0a, 0x09, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x54, 0x6f, 0x6f, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a,
	0x39, 0x0a, 0x0b, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x51, 0x0a, 0x0d, 0x54, 0x6f,
	0x6f, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x42, 0x6f, 0x64,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x42, 0x6f, 0x64, 0x79, 0x22, 0x90, 0x01,
	0x0a, 0x16, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x4e, 0x6f,
	0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x22, 0xf1, 0x06, 0x0a, 0x1e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x4e, 0x0a, 0x0b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x64,
	0x65, 0x4a, 0x53, 0x4f, 0x4e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x6f, 0x64,
	0x65, 0x4a, 0x53, 0x4f, 0x4e, 0x12, 0x63, 0x0a, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x2c,
	0x0a, 0x11, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x65, 0x64, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x65, 0x64, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x4c, 0x0a, 0x0c,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x0c, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x28, 0x0a, 0x0f, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x44, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x44,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x44,
	0x12, 0x24, 0x0a, 0x0d, 0x4d, 0x61, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4d, 0x61, 0x69, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x52, 0x65, 0x77, 0x72, 0x69, 0x74,
	0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x65,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x7e, 0x0a, 0x0f, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x54, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x62, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x6d, 0x0a, 0x14, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x3f, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xa0, 0x02, 0x0a, 0x1c, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x44,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x44, 0x12, 0x18, 0x0a, 0x07, 0x49, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x49, 0x73, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x42, 0x0a, 0x07, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x52, 0x07, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x12,
	0x56, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x18, 0x64, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73,
	0x74, 0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74,
	0x69, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0xdf, 0x03, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x55, 0x69, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x52, 0x75, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x44, 0x12, 0x43, 0x0a, 0x06, 0x52,
	0x75, 0x6e, 0x45, 0x6e, 0x76, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75,
	0x6e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76,
	0x12, 0x1e, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x44,
	0x12, 0x14, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x41, 0x70, 0x70, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x24, 0x0a, 0x0d,
	0x4d, 0x61, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x4d, 0x61, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x77, 0x0a, 0x0f, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x49,
	0x73, 0x44, 0x65, 0x62, 0x75, 0x67, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x49, 0x73,
	0x44, 0x65, 0x62, 0x75, 0x67, 0x1a, 0x42, 0x0a, 0x14, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x43, 0x0a, 0x15, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x2a, 0x0a, 0x10, 0x49, 0x73, 0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x52,
	0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x49, 0x73,
	0x41, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x22, 0x3e,
	0x0a, 0x16, 0x53, 0x74, 0x6f, 0x70, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x49, 0x44, 0x22, 0x16,
	0x0a, 0x14, 0x53, 0x74, 0x6f, 0x70, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x31, 0x0a, 0x1d, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x4e, 0x75, 0x6d,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x69, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x69, 0x6e, 0x22, 0xec, 0x01, 0x0a, 0x1b, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75,
	0x6e, 0x4e, 0x75, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x66, 0x0a, 0x08, 0x55, 0x69, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75,
	0x6e, 0x4e, 0x75, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x55, 0x69, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x55, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x1a, 0x65, 0x0a, 0x0d, 0x55, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x29, 0x0a, 0x07, 0x55, 0x69, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x4e, 0x75,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x4e, 0x75, 0x6d, 0x2a, 0x26, 0x0a, 0x0a, 0x52, 0x75, 0x6e, 0x45, 0x6e, 0x76, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x41, 0x4e, 0x44, 0x42, 0x4f, 0x58, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x10, 0x01, 0x2a, 0x20, 0x0a, 0x0b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x52, 0x55,
	0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x53, 0x54, 0x4f, 0x50, 0x10, 0x01, 0x2a, 0x35, 0x0a,
	0x04, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x55, 0x53, 0x45, 0x52, 0x10, 0x00, 0x12,
	0x0d, 0x0a, 0x09, 0x41, 0x53, 0x53, 0x49, 0x53, 0x54, 0x41, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x53, 0x59, 0x53, 0x54, 0x45, 0x4d, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f,
	0x4e, 0x45, 0x10, 0x03, 0x2a, 0x41, 0x0a, 0x0f, 0x56, 0x65, 0x72, 0x62, 0x6f, 0x73, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4c, 0x4c, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44,
	0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x02, 0x2a, 0x38, 0x0a, 0x0b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x54, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x54, 0x5f, 0x4c, 0x4c, 0x4d,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x54, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10,
	0x02, 0x2a, 0x51, 0x0a, 0x0e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a,
	0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c,
	0x45, 0x44, 0x10, 0x04, 0x32, 0xf7, 0x15, 0x0a, 0x0a, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x44, 0x6d, 0x12, 0x89, 0x01, 0x0a, 0x11, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x12, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12,
	0x7b, 0x0a, 0x0b, 0x52, 0x75, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x33,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x52, 0x75, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x75, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x12, 0x7a, 0x0a, 0x0c,
	0x43, 0x6c, 0x65, 0x61, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43,
	0x6c, 0x65, 0x61, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x32, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x8c, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x73,
	0x65, 0x72, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x12,
	0x3a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x53, 0x61, 0x6e,
	0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x41, 0x70, 0x70, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x9b, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x73, 0x65,
	0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64,
	0x62, 0x6f, 0x78, 0x12, 0x3f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x9e, 0x01, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x49, 0x6e, 0x53, 0x61, 0x6e, 0x64, 0x62,
	0x6f, 0x78, 0x12, 0x40, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x73, 0x49, 0x6e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x49, 0x6e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x8c, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x12, 0x3a, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x41,
	0x70, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x89, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x12, 0x39, 0x2e, 0x74, 0x72,
	0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64,
	0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x41, 0x70, 0x70, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x00, 0x12, 0xa1, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x12,
	0x41, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x73, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f,
	0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0xa1, 0x01, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53, 0x61, 0x6e, 0x64,
	0x62, 0x6f, 0x78, 0x12, 0x41, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64,
	0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x49, 0x6e, 0x53, 0x61, 0x6e, 0x64, 0x62,
	0x6f, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x9e, 0x01, 0x0a, 0x18, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x54, 0x6f, 0x53,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x12, 0x40, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64,
	0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f,
	0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e,
	0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x54, 0x6f, 0x53, 0x61, 0x6e, 0x64,
	0x62, 0x6f, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x9e, 0x01, 0x0a, 0x18, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x49, 0x6e,
	0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x12, 0x40, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x53, 0x61, 0x6e, 0x64, 0x62,
	0x6f, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x53, 0x61, 0x6e,
	0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x89, 0x01, 0x0a, 0x11,
	0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x73, 0x12, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x8c, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x62, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3a,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x89, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x62, 0x75, 0x67,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x39, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44,
	0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b,
	0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x00, 0x12, 0x9f, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f, 0x64, 0x65, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x12, 0x3f,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x6f,
	0x64, 0x65, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3d, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x4e,
	0x6f, 0x64, 0x65, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x28, 0x01, 0x30, 0x01, 0x12, 0x86, 0x01, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x12, 0x38, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62,
	0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x83, 0x01,
	0x0a, 0x0f, 0x53, 0x74, 0x6f, 0x70, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75,
	0x6e, 0x12, 0x37, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x52, 0x75, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f,
	0x70, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x00, 0x12, 0x98, 0x01, 0x0a, 0x16, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x75, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x3e,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x52, 0x75, 0x6e, 0x4e, 0x75, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x77, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x6d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x52, 0x75, 0x6e, 0x4e, 0x75, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x42, 0x3f,
	0x5a, 0x3d, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69,
	0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f,
	0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x4b, 0x45, 0x50, 0x5f, 0x57, 0x46, 0x5f, 0x44, 0x4d, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_workflow_dm_proto_rawDescOnce sync.Once
	file_workflow_dm_proto_rawDescData = file_workflow_dm_proto_rawDesc
)

func file_workflow_dm_proto_rawDescGZIP() []byte {
	file_workflow_dm_proto_rawDescOnce.Do(func() {
		file_workflow_dm_proto_rawDescData = protoimpl.X.CompressGZIP(file_workflow_dm_proto_rawDescData)
	})
	return file_workflow_dm_proto_rawDescData
}

var file_workflow_dm_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_workflow_dm_proto_msgTypes = make([]protoimpl.MessageInfo, 61)
var file_workflow_dm_proto_goTypes = []interface{}{
	(RunEnvType)(0),                          // 0: trpc.KEP.bot_workflow_dm_server.RunEnvType
	(RequestType)(0),                         // 1: trpc.KEP.bot_workflow_dm_server.RequestType
	(Role)(0),                                // 2: trpc.KEP.bot_workflow_dm_server.Role
	(VerboseModeType)(0),                     // 3: trpc.KEP.bot_workflow_dm_server.VerboseModeType
	(RespondType)(0),                         // 4: trpc.KEP.bot_workflow_dm_server.RespondType
	(WorkflowStatus)(0),                      // 5: trpc.KEP.bot_workflow_dm_server.WorkflowStatus
	(RunNodeInfo_StatusType)(0),              // 6: trpc.KEP.bot_workflow_dm_server.RunNodeInfo.StatusType
	(*RunWorkflowRequest)(nil),               // 7: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest
	(*Message)(nil),                          // 8: trpc.KEP.bot_workflow_dm_server.Message
	(*Variable)(nil),                         // 9: trpc.KEP.bot_workflow_dm_server.Variable
	(*RunWorkflowReply)(nil),                 // 10: trpc.KEP.bot_workflow_dm_server.RunWorkflowReply
	(*Respond)(nil),                          // 11: trpc.KEP.bot_workflow_dm_server.Respond
	(*ThoughtInfo)(nil),                      // 12: trpc.KEP.bot_workflow_dm_server.ThoughtInfo
	(*Reference)(nil),                        // 13: trpc.KEP.bot_workflow_dm_server.Reference
	(*RunNodeInfo)(nil),                      // 14: trpc.KEP.bot_workflow_dm_server.RunNodeInfo
	(*StatisticInfo)(nil),                    // 15: trpc.KEP.bot_workflow_dm_server.StatisticInfo
	(*ClearSessionRequest)(nil),              // 16: trpc.KEP.bot_workflow_dm_server.ClearSessionRequest
	(*ClearSessionReply)(nil),                // 17: trpc.KEP.bot_workflow_dm_server.ClearSessionReply
	(*UpsertAppToSandboxRequest)(nil),        // 18: trpc.KEP.bot_workflow_dm_server.UpsertAppToSandboxRequest
	(*UpsertAppToSandboxReply)(nil),          // 19: trpc.KEP.bot_workflow_dm_server.UpsertAppToSandboxReply
	(*UpsertWorkflowToSandboxRequest)(nil),   // 20: trpc.KEP.bot_workflow_dm_server.UpsertWorkflowToSandboxRequest
	(*UpsertWorkflowToSandboxReply)(nil),     // 21: trpc.KEP.bot_workflow_dm_server.UpsertWorkflowToSandboxReply
	(*DeleteWorkflowsInSandboxRequest)(nil),  // 22: trpc.KEP.bot_workflow_dm_server.DeleteWorkflowsInSandboxRequest
	(*DeleteWorkflowsInSandboxReply)(nil),    // 23: trpc.KEP.bot_workflow_dm_server.DeleteWorkflowsInSandboxReply
	(*ReleaseWorkflowAppRequest)(nil),        // 24: trpc.KEP.bot_workflow_dm_server.ReleaseWorkflowAppRequest
	(*ReleaseWorkflowAppReply)(nil),          // 25: trpc.KEP.bot_workflow_dm_server.ReleaseWorkflowAppReply
	(*DeleteWorkflowAppRequest)(nil),         // 26: trpc.KEP.bot_workflow_dm_server.DeleteWorkflowAppRequest
	(*DeleteWorkflowAppReply)(nil),           // 27: trpc.KEP.bot_workflow_dm_server.DeleteWorkflowAppReply
	(*Parameter)(nil),                        // 28: trpc.KEP.bot_workflow_dm_server.Parameter
	(*UpsertParametersToSandboxRequest)(nil), // 29: trpc.KEP.bot_workflow_dm_server.UpsertParametersToSandboxRequest
	(*UpsertParametersToSandboxReply)(nil),   // 30: trpc.KEP.bot_workflow_dm_server.UpsertParametersToSandboxReply
	(*DeleteParametersInSandboxRequest)(nil), // 31: trpc.KEP.bot_workflow_dm_server.DeleteParametersInSandboxRequest
	(*DeleteParametersInSandboxReply)(nil),   // 32: trpc.KEP.bot_workflow_dm_server.DeleteParametersInSandboxReply
	(*UpsertVariablesToSandboxRequest)(nil),  // 33: trpc.KEP.bot_workflow_dm_server.UpsertVariablesToSandboxRequest
	(*Var)(nil),                              // 34: trpc.KEP.bot_workflow_dm_server.Var
	(*UpsertVariablesToSandboxReply)(nil),    // 35: trpc.KEP.bot_workflow_dm_server.UpsertVariablesToSandboxReply
	(*DeleteVariablesInSandboxRequest)(nil),  // 36: trpc.KEP.bot_workflow_dm_server.DeleteVariablesInSandboxRequest
	(*DeleteVariablesInSandboxReply)(nil),    // 37: trpc.KEP.bot_workflow_dm_server.DeleteVariablesInSandboxReply
	(*DescribeWorkflowsRequest)(nil),         // 38: trpc.KEP.bot_workflow_dm_server.DescribeWorkflowsRequest
	(*DescribeWorkflowsReply)(nil),           // 39: trpc.KEP.bot_workflow_dm_server.DescribeWorkflowsReply
	(*RetrieveWorkflowsRequest)(nil),         // 40: trpc.KEP.bot_workflow_dm_server.RetrieveWorkflowsRequest
	(*RetrieveWorkflowsReply)(nil),           // 41: trpc.KEP.bot_workflow_dm_server.RetrieveWorkflowsReply
	(*RetrieveWorkflow)(nil),                 // 42: trpc.KEP.bot_workflow_dm_server.RetrieveWorkflow
	(*DescribeParametersRequest)(nil),        // 43: trpc.KEP.bot_workflow_dm_server.DescribeParametersRequest
	(*DescribeParametersReply)(nil),          // 44: trpc.KEP.bot_workflow_dm_server.DescribeParametersReply
	(*DebugWorkflowNodeRequest)(nil),         // 45: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeRequest
	(*ToolInputData)(nil),                    // 46: trpc.KEP.bot_workflow_dm_server.ToolInputData
	(*DebugWorkflowNodeReply)(nil),           // 47: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeReply
	(*DebugWorkflowNodeDialogRequest)(nil),   // 48: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogRequest
	(*DebugWorkflowNodeDialogReply)(nil),     // 49: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogReply
	(*StartWorkflowRunRequest)(nil),          // 50: trpc.KEP.bot_workflow_dm_server.StartWorkflowRunRequest
	(*StartWorkflowRunReply)(nil),            // 51: trpc.KEP.bot_workflow_dm_server.StartWorkflowRunReply
	(*StopWorkflowRunRequest)(nil),           // 52: trpc.KEP.bot_workflow_dm_server.StopWorkflowRunRequest
	(*StopWorkflowRunReply)(nil),             // 53: trpc.KEP.bot_workflow_dm_server.StopWorkflowRunReply
	(*DescribeWorkflowRunNumRequest)(nil),    // 54: trpc.KEP.bot_workflow_dm_server.DescribeWorkflowRunNumRequest
	(*DescribeWorkflowRunNumReply)(nil),      // 55: trpc.KEP.bot_workflow_dm_server.DescribeWorkflowRunNumReply
	(*UinInfo)(nil),                          // 56: trpc.KEP.bot_workflow_dm_server.UinInfo
	nil,                                      // 57: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.CustomVariablesEntry
	nil,                                      // 58: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.InputsEntry
	nil,                                      // 59: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.BillingTagEntry
	nil,                                      // 60: trpc.KEP.bot_workflow_dm_server.Respond.AnswerOutputEntry
	nil,                                      // 61: trpc.KEP.bot_workflow_dm_server.ReleaseWorkflowAppRequest.WorkflowReleaseTimesEntry
	nil,                                      // 62: trpc.KEP.bot_workflow_dm_server.DescribeParametersReply.ParametersEntry
	nil,                                      // 63: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeRequest.InputsEntry
	nil,                                      // 64: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogRequest.InputsEntry
	nil,                                      // 65: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogRequest.CustomVariablesEntry
	nil,                                      // 66: trpc.KEP.bot_workflow_dm_server.StartWorkflowRunRequest.CustomVariablesEntry
	nil,                                      // 67: trpc.KEP.bot_workflow_dm_server.DescribeWorkflowRunNumReply.UinInfosEntry
	(KEP_WF.NodeType)(0),                     // 68: trpc.KEP.bot_task_config_wf_server.NodeType
	(*KEP_WF.Workflow)(nil),                  // 69: trpc.KEP.bot_task_config_wf_server.Workflow
	(KEP_WF.TypeEnum)(0),                     // 70: trpc.KEP.bot_task_config_wf_server.TypeEnum
}
var file_workflow_dm_proto_depIdxs = []int32{
	0,  // 0: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.RunEnv:type_name -> trpc.KEP.bot_workflow_dm_server.RunEnvType
	1,  // 1: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.RequestType:type_name -> trpc.KEP.bot_workflow_dm_server.RequestType
	8,  // 2: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.QueryHistory:type_name -> trpc.KEP.bot_workflow_dm_server.Message
	57, // 3: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.CustomVariables:type_name -> trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.CustomVariablesEntry
	58, // 4: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.Inputs:type_name -> trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.InputsEntry
	3,  // 5: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.VerboseMode:type_name -> trpc.KEP.bot_workflow_dm_server.VerboseModeType
	59, // 6: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.BillingTag:type_name -> trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.BillingTagEntry
	2,  // 7: trpc.KEP.bot_workflow_dm_server.Message.Role:type_name -> trpc.KEP.bot_workflow_dm_server.Role
	11, // 8: trpc.KEP.bot_workflow_dm_server.RunWorkflowReply.Respond:type_name -> trpc.KEP.bot_workflow_dm_server.Respond
	15, // 9: trpc.KEP.bot_workflow_dm_server.RunWorkflowReply.StatisticInfos:type_name -> trpc.KEP.bot_workflow_dm_server.StatisticInfo
	4,  // 10: trpc.KEP.bot_workflow_dm_server.Respond.Type:type_name -> trpc.KEP.bot_workflow_dm_server.RespondType
	13, // 11: trpc.KEP.bot_workflow_dm_server.Respond.References:type_name -> trpc.KEP.bot_workflow_dm_server.Reference
	5,  // 12: trpc.KEP.bot_workflow_dm_server.Respond.WorkflowStatus:type_name -> trpc.KEP.bot_workflow_dm_server.WorkflowStatus
	14, // 13: trpc.KEP.bot_workflow_dm_server.Respond.RunNodes:type_name -> trpc.KEP.bot_workflow_dm_server.RunNodeInfo
	12, // 14: trpc.KEP.bot_workflow_dm_server.Respond.ThoughtList:type_name -> trpc.KEP.bot_workflow_dm_server.ThoughtInfo
	60, // 15: trpc.KEP.bot_workflow_dm_server.Respond.AnswerOutput:type_name -> trpc.KEP.bot_workflow_dm_server.Respond.AnswerOutputEntry
	68, // 16: trpc.KEP.bot_workflow_dm_server.RunNodeInfo.NodeType:type_name -> trpc.KEP.bot_task_config_wf_server.NodeType
	6,  // 17: trpc.KEP.bot_workflow_dm_server.RunNodeInfo.Status:type_name -> trpc.KEP.bot_workflow_dm_server.RunNodeInfo.StatusType
	15, // 18: trpc.KEP.bot_workflow_dm_server.RunNodeInfo.StatisticInfos:type_name -> trpc.KEP.bot_workflow_dm_server.StatisticInfo
	0,  // 19: trpc.KEP.bot_workflow_dm_server.ClearSessionRequest.RunEnv:type_name -> trpc.KEP.bot_workflow_dm_server.RunEnvType
	69, // 20: trpc.KEP.bot_workflow_dm_server.UpsertWorkflowToSandboxRequest.Workflow:type_name -> trpc.KEP.bot_task_config_wf_server.Workflow
	61, // 21: trpc.KEP.bot_workflow_dm_server.ReleaseWorkflowAppRequest.WorkflowReleaseTimes:type_name -> trpc.KEP.bot_workflow_dm_server.ReleaseWorkflowAppRequest.WorkflowReleaseTimesEntry
	70, // 22: trpc.KEP.bot_workflow_dm_server.Parameter.ValueType:type_name -> trpc.KEP.bot_task_config_wf_server.TypeEnum
	28, // 23: trpc.KEP.bot_workflow_dm_server.UpsertParametersToSandboxRequest.Parameters:type_name -> trpc.KEP.bot_workflow_dm_server.Parameter
	34, // 24: trpc.KEP.bot_workflow_dm_server.UpsertVariablesToSandboxRequest.Variables:type_name -> trpc.KEP.bot_workflow_dm_server.Var
	70, // 25: trpc.KEP.bot_workflow_dm_server.Var.ValueType:type_name -> trpc.KEP.bot_task_config_wf_server.TypeEnum
	0,  // 26: trpc.KEP.bot_workflow_dm_server.DescribeWorkflowsRequest.RunEnv:type_name -> trpc.KEP.bot_workflow_dm_server.RunEnvType
	69, // 27: trpc.KEP.bot_workflow_dm_server.DescribeWorkflowsReply.Workflows:type_name -> trpc.KEP.bot_task_config_wf_server.Workflow
	0,  // 28: trpc.KEP.bot_workflow_dm_server.RetrieveWorkflowsRequest.RunEnv:type_name -> trpc.KEP.bot_workflow_dm_server.RunEnvType
	42, // 29: trpc.KEP.bot_workflow_dm_server.RetrieveWorkflowsReply.Workflows:type_name -> trpc.KEP.bot_workflow_dm_server.RetrieveWorkflow
	0,  // 30: trpc.KEP.bot_workflow_dm_server.DescribeParametersRequest.RunEnv:type_name -> trpc.KEP.bot_workflow_dm_server.RunEnvType
	62, // 31: trpc.KEP.bot_workflow_dm_server.DescribeParametersReply.Parameters:type_name -> trpc.KEP.bot_workflow_dm_server.DescribeParametersReply.ParametersEntry
	63, // 32: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeRequest.Inputs:type_name -> trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeRequest.InputsEntry
	46, // 33: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeRequest.ToolInput:type_name -> trpc.KEP.bot_workflow_dm_server.ToolInputData
	14, // 34: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeReply.NodeData:type_name -> trpc.KEP.bot_workflow_dm_server.RunNodeInfo
	1,  // 35: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogRequest.RequestType:type_name -> trpc.KEP.bot_workflow_dm_server.RequestType
	64, // 36: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogRequest.Inputs:type_name -> trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogRequest.InputsEntry
	8,  // 37: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogRequest.QueryHistory:type_name -> trpc.KEP.bot_workflow_dm_server.Message
	65, // 38: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogRequest.CustomVariables:type_name -> trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogRequest.CustomVariablesEntry
	11, // 39: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogReply.Respond:type_name -> trpc.KEP.bot_workflow_dm_server.Respond
	15, // 40: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogReply.StatisticInfos:type_name -> trpc.KEP.bot_workflow_dm_server.StatisticInfo
	0,  // 41: trpc.KEP.bot_workflow_dm_server.StartWorkflowRunRequest.RunEnv:type_name -> trpc.KEP.bot_workflow_dm_server.RunEnvType
	66, // 42: trpc.KEP.bot_workflow_dm_server.StartWorkflowRunRequest.CustomVariables:type_name -> trpc.KEP.bot_workflow_dm_server.StartWorkflowRunRequest.CustomVariablesEntry
	67, // 43: trpc.KEP.bot_workflow_dm_server.DescribeWorkflowRunNumReply.UinInfos:type_name -> trpc.KEP.bot_workflow_dm_server.DescribeWorkflowRunNumReply.UinInfosEntry
	9,  // 44: trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest.CustomVariablesEntry.value:type_name -> trpc.KEP.bot_workflow_dm_server.Variable
	28, // 45: trpc.KEP.bot_workflow_dm_server.DescribeParametersReply.ParametersEntry.value:type_name -> trpc.KEP.bot_workflow_dm_server.Parameter
	9,  // 46: trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogRequest.CustomVariablesEntry.value:type_name -> trpc.KEP.bot_workflow_dm_server.Variable
	56, // 47: trpc.KEP.bot_workflow_dm_server.DescribeWorkflowRunNumReply.UinInfosEntry.value:type_name -> trpc.KEP.bot_workflow_dm_server.UinInfo
	40, // 48: trpc.KEP.bot_workflow_dm_server.WorkflowDm.RetrieveWorkflows:input_type -> trpc.KEP.bot_workflow_dm_server.RetrieveWorkflowsRequest
	7,  // 49: trpc.KEP.bot_workflow_dm_server.WorkflowDm.RunWorkflow:input_type -> trpc.KEP.bot_workflow_dm_server.RunWorkflowRequest
	16, // 50: trpc.KEP.bot_workflow_dm_server.WorkflowDm.ClearSession:input_type -> trpc.KEP.bot_workflow_dm_server.ClearSessionRequest
	18, // 51: trpc.KEP.bot_workflow_dm_server.WorkflowDm.UpsertAppToSandbox:input_type -> trpc.KEP.bot_workflow_dm_server.UpsertAppToSandboxRequest
	20, // 52: trpc.KEP.bot_workflow_dm_server.WorkflowDm.UpsertWorkflowToSandbox:input_type -> trpc.KEP.bot_workflow_dm_server.UpsertWorkflowToSandboxRequest
	22, // 53: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DeleteWorkflowsInSandbox:input_type -> trpc.KEP.bot_workflow_dm_server.DeleteWorkflowsInSandboxRequest
	24, // 54: trpc.KEP.bot_workflow_dm_server.WorkflowDm.ReleaseWorkflowApp:input_type -> trpc.KEP.bot_workflow_dm_server.ReleaseWorkflowAppRequest
	26, // 55: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DeleteWorkflowApp:input_type -> trpc.KEP.bot_workflow_dm_server.DeleteWorkflowAppRequest
	29, // 56: trpc.KEP.bot_workflow_dm_server.WorkflowDm.UpsertParametersToSandbox:input_type -> trpc.KEP.bot_workflow_dm_server.UpsertParametersToSandboxRequest
	31, // 57: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DeleteParametersInSandbox:input_type -> trpc.KEP.bot_workflow_dm_server.DeleteParametersInSandboxRequest
	33, // 58: trpc.KEP.bot_workflow_dm_server.WorkflowDm.UpsertVariablesToSandbox:input_type -> trpc.KEP.bot_workflow_dm_server.UpsertVariablesToSandboxRequest
	36, // 59: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DeleteVariablesInSandbox:input_type -> trpc.KEP.bot_workflow_dm_server.DeleteVariablesInSandboxRequest
	38, // 60: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DescribeWorkflows:input_type -> trpc.KEP.bot_workflow_dm_server.DescribeWorkflowsRequest
	43, // 61: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DescribeParameters:input_type -> trpc.KEP.bot_workflow_dm_server.DescribeParametersRequest
	45, // 62: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DebugWorkflowNode:input_type -> trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeRequest
	48, // 63: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DebugWorkflowNodeDialog:input_type -> trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogRequest
	50, // 64: trpc.KEP.bot_workflow_dm_server.WorkflowDm.StartWorkflowRun:input_type -> trpc.KEP.bot_workflow_dm_server.StartWorkflowRunRequest
	52, // 65: trpc.KEP.bot_workflow_dm_server.WorkflowDm.StopWorkflowRun:input_type -> trpc.KEP.bot_workflow_dm_server.StopWorkflowRunRequest
	54, // 66: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DescribeWorkflowRunNum:input_type -> trpc.KEP.bot_workflow_dm_server.DescribeWorkflowRunNumRequest
	41, // 67: trpc.KEP.bot_workflow_dm_server.WorkflowDm.RetrieveWorkflows:output_type -> trpc.KEP.bot_workflow_dm_server.RetrieveWorkflowsReply
	10, // 68: trpc.KEP.bot_workflow_dm_server.WorkflowDm.RunWorkflow:output_type -> trpc.KEP.bot_workflow_dm_server.RunWorkflowReply
	17, // 69: trpc.KEP.bot_workflow_dm_server.WorkflowDm.ClearSession:output_type -> trpc.KEP.bot_workflow_dm_server.ClearSessionReply
	19, // 70: trpc.KEP.bot_workflow_dm_server.WorkflowDm.UpsertAppToSandbox:output_type -> trpc.KEP.bot_workflow_dm_server.UpsertAppToSandboxReply
	21, // 71: trpc.KEP.bot_workflow_dm_server.WorkflowDm.UpsertWorkflowToSandbox:output_type -> trpc.KEP.bot_workflow_dm_server.UpsertWorkflowToSandboxReply
	23, // 72: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DeleteWorkflowsInSandbox:output_type -> trpc.KEP.bot_workflow_dm_server.DeleteWorkflowsInSandboxReply
	25, // 73: trpc.KEP.bot_workflow_dm_server.WorkflowDm.ReleaseWorkflowApp:output_type -> trpc.KEP.bot_workflow_dm_server.ReleaseWorkflowAppReply
	27, // 74: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DeleteWorkflowApp:output_type -> trpc.KEP.bot_workflow_dm_server.DeleteWorkflowAppReply
	30, // 75: trpc.KEP.bot_workflow_dm_server.WorkflowDm.UpsertParametersToSandbox:output_type -> trpc.KEP.bot_workflow_dm_server.UpsertParametersToSandboxReply
	32, // 76: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DeleteParametersInSandbox:output_type -> trpc.KEP.bot_workflow_dm_server.DeleteParametersInSandboxReply
	35, // 77: trpc.KEP.bot_workflow_dm_server.WorkflowDm.UpsertVariablesToSandbox:output_type -> trpc.KEP.bot_workflow_dm_server.UpsertVariablesToSandboxReply
	37, // 78: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DeleteVariablesInSandbox:output_type -> trpc.KEP.bot_workflow_dm_server.DeleteVariablesInSandboxReply
	39, // 79: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DescribeWorkflows:output_type -> trpc.KEP.bot_workflow_dm_server.DescribeWorkflowsReply
	44, // 80: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DescribeParameters:output_type -> trpc.KEP.bot_workflow_dm_server.DescribeParametersReply
	47, // 81: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DebugWorkflowNode:output_type -> trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeReply
	49, // 82: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DebugWorkflowNodeDialog:output_type -> trpc.KEP.bot_workflow_dm_server.DebugWorkflowNodeDialogReply
	51, // 83: trpc.KEP.bot_workflow_dm_server.WorkflowDm.StartWorkflowRun:output_type -> trpc.KEP.bot_workflow_dm_server.StartWorkflowRunReply
	53, // 84: trpc.KEP.bot_workflow_dm_server.WorkflowDm.StopWorkflowRun:output_type -> trpc.KEP.bot_workflow_dm_server.StopWorkflowRunReply
	55, // 85: trpc.KEP.bot_workflow_dm_server.WorkflowDm.DescribeWorkflowRunNum:output_type -> trpc.KEP.bot_workflow_dm_server.DescribeWorkflowRunNumReply
	67, // [67:86] is the sub-list for method output_type
	48, // [48:67] is the sub-list for method input_type
	48, // [48:48] is the sub-list for extension type_name
	48, // [48:48] is the sub-list for extension extendee
	0,  // [0:48] is the sub-list for field type_name
}

func init() { file_workflow_dm_proto_init() }
func file_workflow_dm_proto_init() {
	if File_workflow_dm_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_workflow_dm_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunWorkflowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Message); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Variable); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunWorkflowReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Respond); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThoughtInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RunNodeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatisticInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearSessionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClearSessionReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertAppToSandboxRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertAppToSandboxReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertWorkflowToSandboxRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertWorkflowToSandboxReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWorkflowsInSandboxRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWorkflowsInSandboxReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseWorkflowAppRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseWorkflowAppReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWorkflowAppRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteWorkflowAppReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Parameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertParametersToSandboxRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertParametersToSandboxReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteParametersInSandboxRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteParametersInSandboxReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertVariablesToSandboxRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Var); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpsertVariablesToSandboxReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteVariablesInSandboxRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteVariablesInSandboxReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeWorkflowsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeWorkflowsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveWorkflowsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveWorkflowsReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetrieveWorkflow); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeParametersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeParametersReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugWorkflowNodeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ToolInputData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugWorkflowNodeReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugWorkflowNodeDialogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugWorkflowNodeDialogReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartWorkflowRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartWorkflowRunReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopWorkflowRunRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopWorkflowRunReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeWorkflowRunNumRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeWorkflowRunNumReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_workflow_dm_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UinInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_workflow_dm_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   61,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_workflow_dm_proto_goTypes,
		DependencyIndexes: file_workflow_dm_proto_depIdxs,
		EnumInfos:         file_workflow_dm_proto_enumTypes,
		MessageInfos:      file_workflow_dm_proto_msgTypes,
	}.Build()
	File_workflow_dm_proto = out.File
	file_workflow_dm_proto_rawDesc = nil
	file_workflow_dm_proto_goTypes = nil
	file_workflow_dm_proto_depIdxs = nil
}
