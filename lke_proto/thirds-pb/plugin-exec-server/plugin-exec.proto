syntax = "proto3";

package trpc.KEP.plugin_exec_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/plugin_exec_server";

import "trpc.proto";

service PluginExec {
  // 工具运行
  // @alias=/RunTool
  rpc RunTool(RunToolReq) returns (RunToolRsp);

  // 工具运行（流式）
  rpc StreamRunTool(stream StreamRunToolReq) returns (stream StreamRunToolRsp);
}

message RunToolReq {
  string AppId = 1; // 应用id
  uint32 AppScene = 2; // 应用场景 1 评测 2 线上
  string PluginId = 3; // 插件id
  string ToolId = 4; // 工具id
  string HeaderValue = 5; // header值，json字符串
  string QueryValue = 6; // query值，json字符串
  string BodyValue = 7; // body值，json字符串
  string InputValue = 8; // 输入值，json字符串，为Query、Header、Body的合集
  string Uin = 9; // uin

  ToolExtraInfo ExtraInfo = 100; // 附加信息
}

// 附加信息
message ToolExtraInfo {
  string SystemRole = 1; // 系统角色
  string SessionId = 2; // 上下文SessionId 代码执行工具用
  repeated History History = 3; // 历史记录
  repeated Label Labels = 4; // 标签
  // agent 模式的知识库配置
  AgentKnowledgeQAConfig AgentKnowledgeQaConfig = 5;
}

message AgentModelInfo {
  // index 和别的地方对齐
  string ModelName = 8;
  float Temperature = 14;
  // TopP
  float TopP = 15;
}

enum KnowledgeFilterEnum {
  KNOWLEDGE_FILTER_TYPE_ALL = 0;          // 全部知识
  KNOWLEDGE_FILTER_TYPE_DOC_AND_QA = 1;   // 按文档和问答
  KNOWLEDGE_FILTER_TYPE_TAG = 2;          // 按标签
}

message KnowledgeFilterDocAndAnswer {
  repeated string DocBizIds = 5;    // 文档ID列表
  bool AllQa = 6;                   // 问答
}

message KnowledgeFilter {
  KnowledgeFilterEnum FilterType = 1;             // 知识检索筛选方式: [文档和问答] or [标签]
  KnowledgeFilterDocAndAnswer DocAndAnswer = 2;   // 文档和问答过滤器
  TagOperatorEnum TagOperator = 3;                // 标签之间的关系，可以是 AND OR，标签从 Labels 中取
  enum TagOperatorEnum {
    AND = 0; // AND
    OR = 1;  // OR
  }
}

message AgentKnowledgeQAConfig {
  // 知识检索筛选范围
  KnowledgeFilter Filter = 1;
  // 插件调用LLM时使用的模型配置，一般用于指定知识库问答插件的生成模型
  AgentModelInfo AgentModel = 9;
}

message RunToolRsp {
  string Result = 1; // 执行结果，解析后的json字符串
  string RawResult = 2; // 执行结果，解析前的原始字符串

  repeated StatisticInfo StatisticInfos = 100; // LLM统计信息
}

// 历史记录
message History {
  enum Role {
    USER = 0;      // 用户说的内容
    ASSISTANT = 1; // 大模型回复的额内容
    SYSTEM = 2;    // 一般作为系统Prompt用
    NONE = 3;      // 无角色（与LLM交互时将不自动补齐角色名）
  }
  Role role = 1; // 消息的角色
  string Content  = 2; // 内容
  repeated string Images  = 3; // 图片地址
}

// 标签
message Label {
  string name = 1; // 标签名
  repeated string values = 2; // 标签值
}

// 统计信息
message StatisticInfo {
  string ModelName = 1;       // 模型名称
  uint32 FirstTokenCost = 2;  // 首token耗时
  uint32 TotalCost = 3;       // 推理总耗时
  uint32 InputTokens = 4;     // 输入token数量
  uint32 OutputTokens = 5;    // 输出token数量
  uint32 TotalTokens = 6;     // 输入+输出总token
}

// 工具运行请求（流式）
message StreamRunToolReq {
  enum ReqType {
    UNKNOWN_TYPE = 0; // 未知类型
    RUN = 1;          // 工具执行
    CANCEL = 2;       // 取消执行
  }
  ReqType Type = 1;   // 请求类型

  RunToolReq Req = 100; // 请求参数
}

// 工具运行返回（流式）
message StreamRunToolRsp {
  int32 Code = 1;     // 返回码
  string ErrMsg = 2;  // 错误信息
  bool IsFinal = 3;   // 是否结束

  RunToolRsp Rsp = 100; // 响应参数
}