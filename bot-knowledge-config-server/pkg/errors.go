// Package pkg 用来写需要对外导出的变量、常量、类型和方法
package pkg

import (
	"git.code.oa.com/trpc-go/trpc-go/errs"
)

// errors 服务内部定义的错误要暴露出去，调用方需要引用它来判断错误类型
const (
	ErrCodeExcelContent               = 450081
	ErrCodeAnswerTooLong              = 400035
	ErrCodeAnswerTooShort             = 400031
	ErrCodeQuestionTooLong            = 400034
	ErrCodeQuestionTooShort           = 400030
	ErrCodeCateNameTooLong            = 400032
	ErrCodeCateNameTooShort           = 400029
	ErrCodeImportLengthExceedLimit    = 450083
	ErrCodeCateCountExceed            = 450084
	ErrCodeUserHasJoinCorp            = 450115
	ErrCodeExcelNumTooFew             = 400037
	ErrCodeExcelNumTooMany            = 400038
	ErrCodeSimilarQuestionExceedLimit = 400039
	ErrCodeSimilarQuestionRepeated    = 400040
	ErrCodeAIConf                     = 450085
)

const (
	SystemIntegratorNameQiDian = "企点" // 集成商表t_system_integrator中企点的名称
)

// ErrMap 集成商定制错误信息映射表 集成商id -> 通用错误信息 -> 定制错误信息
var ErrMap = map[string]map[error]error{
	SystemIntegratorNameQiDian: {
		ErrOverCharacterSizeLimit:   ErrOverCharacterSizeLimit4QiDian,
		ErrDocParseCharSizeExceeded: ErrDocParseCharSizeExceeded4QiDian,
	},
}

// errors 服务内部定义的错误要暴露出去，调用方需要引用它来判断错误类型
var (
	ErrAlreadyLocked                      = errs.New(400028, "正在处理中")
	ErrRobotInitFail                      = errs.New(400033, "初始化应用失败")
	ErrUnknownSplitModel                  = errs.New(400036, "未知的问题分割模式配置")
	ErrCorpNotFound                       = errs.New(400037, "企业不存在")
	ErrUnknownSplitProc                   = errs.New(400037, "未知的问题分割处理逻辑")
	ErrDocNotFound                        = errs.New(450001, "文档不存在")
	ErrPermissionDenied                   = errs.New(450002, "权限不足")
	ErrDocStatusNotAllowReCreateQA        = errs.New(450003, "文档状态不允许重新创建问答对")
	ErrUserNotFound                       = errs.New(450004, "用户不存在")
	ErrStaffNotFound                      = errs.New(450005, "员工不存在")
	ErrUserIsInValid                      = errs.New(450005, "用户已失效")
	ErrSessionNotFound                    = errs.New(450006, "用户未登录")
	ErrSmsSendFailed                      = errs.New(450007, "验证码发送失败")
	ErrVerifyCode                         = errs.New(450008, "验证码错误")
	ErrSmsSendLimit                       = errs.New(450009, "短信发送过于频繁")
	ErrVerifyFailedTooManyTimes           = errs.New(450010, "验证码输入错误超过5次，账号已被限制登录，请1小时后重试")
	ErrRobotNotFound                      = errs.New(450011, "应用不存在")
	ErrCateNotFound                       = errs.New(450012, "分类不存在")
	ErrQANotFound                         = errs.New(450013, "QA不存在")
	ErrQAIsDeleted                        = errs.New(450014, "QA已删除")
	ErrLockTaskFail                       = errs.New(450015, "锁定任务失败")
	ErrUnSupportFileType                  = errs.New(450016, "不支持的文件类型")
	ErrReleaseFail                        = errs.New(450017, "发布失败")
	ErrQASimilarNotFound                  = errs.New(450018, "相似QA不存在")
	ErrDocHasDeleted                      = errs.New(450019, "文档已删除")
	ErrDocExist                           = errs.New(450020, "文档已存在")
	ErrForbidRefer                        = errs.New(450022, "不允许操作引用")
	ErrStatusSuccess                      = errs.New(450022, "生成完成后才允许操作引用链接")
	ErrDocIsRelease                       = errs.New(450023, "文档相关QA正在发布中")
	ErrSystem                             = errs.New(450024, "系统错误")
	ErrReleaseNotFound                    = errs.New(450025, "版本不存在")
	ErrNoReleaseQA                        = errs.New(450027, "没有可发布的文档/问答/拒答/任务流程/应用配置")
	ErrSegmentNotFound                    = errs.New(450038, "文档段落不存在")
	ErrHasInAuditCorp                     = errs.New(450040, "存在审核中的企业")
	ErrTaskNotFound                       = errs.New(450040, "任务不存在")
	ErrDocForbidDelete                    = errs.New(450041, "文档不允许删除")
	ErrTaskIsNotInit                      = errs.New(450041, "任务不是初始化状态")
	ErrSegmentIsEmpty                     = errs.New(450042, "分片内容为空")
	ErrTaskIsStop                         = errs.New(450042, "任务已暂停")
	ErrQAIsPendingRelease                 = errs.New(450043, "QA正在发布中")
	ErrExcelIsEmpty                       = errs.New(450044, "Excel 文件内容为空")
	ErrDocTypeForIndex                    = errs.New(450045, "文档类型不支持生成索引")
	ErrTaskMaxTryTimes                    = errs.New(450045, "任务达到最大重试次数")
	ErrCorpInValid                        = errs.New(450046, "企业已失效")
	ErrSegmentAlreadyExist                = errs.New(450046, "文档段落已存在")
	ErrReleaseIsNotPending                = errs.New(450047, "发布单不是待回调状态")
	ErrSegmentQAAlreadyExist              = errs.New(450047, "文档段落QA已存在")
	ErrDocTplNotFound                     = errs.New(450049, "文档模板不存在")
	ErrDoingRelease                       = errs.New(450050, "正在发布中")
	ErrExcelHead                          = errs.New(450051, "Excel 文件表头错误")
	ErrQANotModifyFound                   = errs.New(450052, "当前版本没有修改这个QA")
	ErrGetTemplateFail                    = errs.New(450053, "获取模版文件失败")
	ErrGetDocListFail                     = errs.New(450054, "获取文档列表失败")
	ErrUserWaitAudit                      = errs.New(450055, "已提交注册，正在审核中，请联系 jessieewu 、miyahma")
	ErrLoginTimesLimit                    = errs.New(450056, "该手机号已达到最大登录限制，请明天再尝试登录")
	ErrAlreadyJoinedCorp                  = errs.New(450057, "请勿重复加入到当前企业")
	ErrParams                             = errs.New(450057, "参数异常，请重试")
	ErrSameDocUploading                   = errs.New(450058, "相同文档已上传")
	ErrRepeatCorpName                     = errs.New(450059, "公司全称不能重复")
	ErrSegmentNotForIndex                 = errs.New(450059, "文档段落不支持生成索引")
	ErrReleaseType                        = errs.New(450060, "发布类型错误")
	ErrVerifyCodeNotFound                 = errs.New(450061, "未发送验证码")
	ErrReferDocFail                       = errs.New(450062, "文档生成的问答不支持修改关联文档")
	ErrFetchURLFail                       = errs.New(450063, "请求url失败")
	ErrInvalidURL                         = errs.New(450064, "Invalid URL")
	ErrInvalidFileName                    = errs.New(450065, "文件名校验失败")
	ErrInvalidAuditSource                 = errs.New(450066, "非法审核来源")
	ErrReleaseQANotFound                  = errs.New(450067, "发布QA不存在")
	ErrUnknownSplitConfig                 = errs.New(450076, "未知的切分配置")
	ErrUnknownMergerType                  = errs.New(450077, "未知的合并器")
	ErrFetchURLTooBig                     = errs.New(450078, "网页内容大小超过限制（2MB），请重试。")
	ErrUnknownMiniChunkType               = errs.New(450079, "未识别的文档资源类型")
	ErrRobotInAudit                       = errs.New(450080, "应用审核中")
	ErrExcelContent                       = errs.New(450081, "Excel 文件内容错误")
	ErrCateCountExceed                    = errs.New(450084, "分类数量超过限制")
	ErrExportQA                           = errs.New(450085, "导出QA失败")
	ErrExportQATooMany                    = errs.New(450086, "数据过大无法导出")
	ErrExportQATaskTooMany                = errs.New(450087, "导出任务达到上限")
	ErrRobotQuotaNotEnough                = errs.New(450087, "应用配额不足")
	ErrAuditFlagNotFound                  = errs.New(450088, "文件对应审核标记未配置")
	ErrAuditFlagNotFount                  = errs.New(450088, "文件对应审核标记未配置")
	ErrUserHasNoCorp                      = errs.New(450089, "该账号未注册或未归属企业")
	ErrCateDepthExceed                    = errs.New(450090, "分类层级超过限制")
	ErrUserHasCorp                        = errs.New(450090, "请先退出当前企业，再注册新的企业")
	ErrCorpIsNotInAudit                   = errs.New(450091, "企业不在审核中")
	ErrInvalidCateID                      = errs.New(450091, "错误的分组 ID")
	ErrInvalidParentCateID                = errs.New(450091, "错误的父级分组 ID")
	ErrCateNameDuplicated                 = errs.New(450092, "分类名称重复")
	ErrStaffCorpNotMatch                  = errs.New(450092, "员工与企业不匹配")
	ErrUserHasJoinCorp                    = errs.New(450093, "必须退出当前企业才能加入")
	ErrOperateDoing                       = errs.New(450094, "请求处理中，请稍候")
	ErrStaffInValid                       = errs.New(450095, "员工信息无效")
	ErrRepeatRobotName                    = errs.New(450096, "昵称重复，请重新输入")
	ErrGetReferFail                       = errs.New(450098, "获取来源详情失败")
	ErrMarkReferFail                      = errs.New(450099, "来源打标失败")
	ErrAuditNotFound                      = errs.New(450100, "审核数据不存在")
	ErrReleaseIsNotInit                   = errs.New(450101, "发布记录不是待采集状态")
	ErrUnknownIndexID                     = errs.New(450102, "未知的索引 ID")
	ErrTelephone                          = errs.New(450103, "手机号码不正确")
	ErrRunningTestRecords                 = errs.New(450104, "分批评测任务中存在失败项")
	ErrDeleteRunningTest                  = errs.New(450105, "无法删除评测中任务")
	ErrInvalidSampleSetExcel              = errs.New(450106, "评测样本集文件解析异常")
	ErrTestNum                            = errs.New(450107, "任务数量异常")
	ErrTestRunning                        = errs.New(450108, "评测模式中，无法编辑") // 需要保证错误码为450108不变
	ErrSameCreateTest                     = errs.New(450109, "已有相同评测任务")
	ErrTestNotExist                       = errs.New(450110, "没有符合的评测任务")
	ErrNoWaitJudgeRecords                 = errs.New(450111, "没有待标注记录")
	ErrRecords                            = errs.New(450112, "没有标注记录")
	ErrExportTaskTypeNotFound             = errs.New(450115, "导出任务类型不存在")
	ErrRejectedQuestionNotFound           = errs.New(450117, "拒答问题不存在")
	ErrRejectedQuestionNotModifyFound     = errs.New(450118, "当前版本没有修改这个拒答问题")
	ErrCreateTest                         = errs.New(450119, "应用配置变更中，无法创建评测任务") //  需要保证错误码为450119不变
	ErrRejectedQuestionIsPendingRelease   = errs.New(450119, "拒答问题正在发布中")
	ErrOverCharacterSizeLimit             = errs.New(450120, "知识库字符数不足，可在官网或联系架构师/腾讯云客服购买。")
	ErrOverCharacterSizeLimit4QiDian      = errs.New(450120, "当前字符数总量已超限制，请先购买更多配额后再进行导入。")
	ErrUnsatisfiedReplyNotFound           = errs.New(450121, "不满意回复信息不存在")
	ErrExportUnsatisfiedReply             = errs.New(450122, "导出不满意回复失败")
	ErrExportrUnsatisfiedReplyTooMany     = errs.New(450123, "不满意回复数据过大无法导出")
	ErrUnsatisfiedReplyParams             = errs.New(450124, "不满意回复参数错误")
	ErrUnsatisfiedReplyAdding             = errs.New(450125, "不满意回复正在添加中")
	ErrAvatarURLFail                      = errs.New(450126, "头像地址错误，请重新上传")
	ErrTransferUnsatisfiedCountParams     = errs.New(450127, "不满意转人工阈值参数错误")
	ErrHTTPMethod                         = errs.New(450128, "HTTP请求方法不正确")
	ErrEmptyBody                          = errs.New(450129, "POST请求体为空")
	ErrAuditCallbackDoing                 = errs.New(450130, "审核回调中，请稍候")
	ErrDocIsNotExcel                      = errs.New(450131, "文档不是 Excel 格式")
	ErrDocIsExcel                         = errs.New(450132, "文档是 Excel 格式")
	ErrLoginTypeInvalid                   = errs.New(450133, "无效的登录类型")
	ErrLoginAccountPassword               = errs.New(450134, "账户密码不匹配")
	ErrLoginAccountFormat                 = errs.New(450135, "账号格式不对")
	ErrLoginAccountExist                  = errs.New(450136, "账号已存在")
	ErrUnknownDocType                     = errs.New(450137, "未知的文档类型")
	ErrAttributeLabelSystem               = errs.New(450138, "标签标识或标准词为系统关键字")
	ErrAttributeLabelAttrKeyInvalid       = errs.New(450139, "标签标识格式无效，请按照规范填写")
	ErrAttributeLabelAttrKeyExist         = errs.New(450140, "标签标识已存在")
	ErrAttributeLabelAttrHasUsed          = errs.New(450141, "标签已被使用")
	ErrAttributeLabelAttrNameExist        = errs.New(450142, "标签名称已存在")
	ErrAttributeLabelAttrNameMaxLen       = errs.New(450143, "标签名称字符长度超过限制")
	ErrAttributeLabelNameRepeated         = errs.New(450144, "标准词或同义词重复")
	ErrAttributeLabelHasRefer             = errs.New(450145, "标签已被关联")
	ErrAttributeLabelNameMaxLen           = errs.New(450146, "标准词名称字符长度超过限制")
	ErrAttributeLabelLimit                = errs.New(450147, "标准词数量超过限制")
	ErrAttributeLabelSimilarMaxLen        = errs.New(450148, "同义词字符长度超过限制")
	ErrAttributeLabelSimilarLimit         = errs.New(450149, "同义词数量超过限制")
	ErrAttributeLabelReferLimit           = errs.New(450150, "知识关联的标签数量超过限制")
	ErrAttributeLabelNotFound             = errs.New(450151, "标签标准词不存在")
	ErrAttributeLabelAttrLimit            = errs.New(450152, "标签数量超过限制")
	ErrAttributeLabelUploading            = errs.New(450153, "标签标准词相同文件已上传")
	ErrAttributeLabelSource               = errs.New(450154, "标签标准词来源错误")
	ErrAttributeLabelRefer                = errs.New(450155, "标签或标准词关联错误")
	ErrDocNotAllowEdit                    = errs.New(450156, "文档不允许编辑")
	ErrAttributeLabelDocQaSync            = errs.New(450157, "标签标准词关联的文档或问答正在同步中，不允许编辑")
	ErrAttributeLabelUpdating             = errs.New(450158, "标签标准词正在更新中，不允许操作")
	ErrAttributeLabelRepeated             = errs.New(450159, "标签标准词重复")
	ErrQANotAllowEdit                     = errs.New(450160, "QA无法编辑")
	ErrCreateAppealIng                    = errs.New(450161, "人工申诉正在新建中")
	ErrQaForbidDelete                     = errs.New(450162, "QA不允许删除，请检查当前状态")
	ErrAppealNotFound                     = errs.New(450163, "目标不符合申诉条件")
	ErrAuditAppealIng                     = errs.New(450164, "人工申诉正在审核中")
	ErrRetryRelease                       = errs.New(450165, "发布重试失败，必须是发布暂停中才可以重试")
	ErrRetryReleaseFail                   = errs.New(450166, "发布重试失败")
	ErrGetRelease                         = errs.New(450167, "获取发布记录失败")
	ErrReleasePause                       = errs.New(450168, "当前存在发布暂停内容，请重试发布上线成功后，再新建发布。")
	ErrUinNotMatch                        = errs.New(450169, "主账号UIN与子账号UIN不匹配")
	ErrSystemIntegratorNotFound           = errs.New(450170, "集成商不存在")
	ErrResourcePermissionDenied           = errs.New(450171, "资源权限不足")
	ErrSystemIntegratorUserParams         = errs.New(450172, "集成商登录用户参数错误")
	ErrFilterNotFound                     = errs.New(450173, "筛选器不存在")
	ErrGlobalKnowledgeNotFound            = errs.New(450174, "全局知识不存在")
	ErrAddGlobalKnowledge                 = errs.New(450175, "添加全局知识失败")
	ErrDelGlobalKnowledge                 = errs.New(450176, "删除全局知识失败")
	ErrUpdGlobalKnowledge                 = errs.New(450177, "修改全局知识失败")
	ErrGetGlobalKnowledge                 = errs.New(450178, "获取全局知识失败")
	ErrForceSyncGlobalKnowledge           = errs.New(450179, "获取全局知识失败")
	ErrActivateProduct                    = errs.New(450180, "产品开通失败")
	ErrParseFileTimeout                   = errs.New(450181, "解析文件超时")
	ErrEmptyName                          = errs.New(450182, "应用昵称不能为空")
	ErrEmptyAvatar                        = errs.New(450183, "应用头像不能为空")
	ErrDocParseTaskNotFound               = errs.New(450184, "文档解析任务未找到")
	ErrDocParseResultCallBackFail         = errs.New(450185, "回调失败")
	ErrDocParseResultCallBackErr          = errs.New(450186, "回调异常，请重试")
	ErrDocParseCharSizeExceeded           = errs.New(450187, "知识库字符数不足，可在官网或联系架构师/腾讯云客服购买。")
	ErrDocParseCharSizeExceeded4QiDian    = errs.New(450187, "当前字符数总量已超限制，请先购买更多配额后再进行导入。")
	ErrDocParseCosURLNotFound             = errs.New(450188, "文档解析COS地址未找到")
	ErrRobotOrDocNotFound                 = errs.New(450189, "应用或文档不存在")
	ErrUpdateDocStatusFail                = errs.New(450190, "更新文档状态失败")
	ErrUpdateDocParseTaskStatusFail       = errs.New(450191, "更新文档解析任务状态失败")
	ErrCreateDocToQATaskFail              = errs.New(450192, "创建文档生成问答对任务失败")
	ErrCreateDocToIndexTaskFail           = errs.New(450193, "创建问答生成索引任务失败")
	ErrUpdateDocStatusAndCharSizeFail     = errs.New(450194, "更新文档状态和字符数失败")
	ErrDocParseTaskFailNotFound           = errs.New(450195, "未找到符合条件的文档解析任务")
	ErrIllegalReleaseStatus               = errs.New(450196, "请单独筛选“已过期”状态！")
	ErrUpdateRobotUsedCharSizeFail        = errs.New(450197, "更新应用已使用字符数失败")
	ErrExcelParseFailInDateValidity       = errs.New(450198, "时间格式不正确")
	ErrExcelParseFailNotHalfHour          = errs.New(450199, "有效期时间不为30分钟倍数")
	ErrDocToQaExpiredFail                 = errs.New(450200, "当前文档已过期，请刷新后重试！")
	ErrFileExtNotMatch                    = errs.New(450201, "文件扩展名不匹配")
	ErrRetryDocParseTaskFail              = errs.New(450202, "文档解析任务重试失败")
	ErrRobotSplitStrategyFail             = errs.New(450203, "应用拆分规则失败")
	ErrStopDocParseFail                   = errs.New(450204, "终止文档解析任务失败")
	ErrCreateAuditFail                    = errs.New(450205, "创建审核任务失败")
	ErrIntentNotFound                     = errs.New(450206, "意图不存在")
	ErrCreateDocParseSplitSegmentTaskFail = errs.New(450208, "创建文档拆分文档提取分段任务，解析服务失败")
	ErrCreateDocParseSplitQATaskFail      = errs.New(450209, "创建文档拆分文档提取问答对任务，解析服务失败")
	ErrAttributeLabelDocQa                = errs.New(450210, "文档生成的问答对不能修改问答标签")
	ErrTrailProductFail                   = errs.New(450210, "试用产品开通失败")
	ErrMasterAccount                      = errs.New(450211, "只有主账号才能操作")
	ErrProductPermissionNotExist          = errs.New(450212, "产品权限不存在")
	ErrProductAlreadyOpen                 = errs.New(450213, "产品已开通")
	ErrDocCannotBeSubmittedForAudit       = errs.New(450215, "当前文档无法重新送审")
	ErrDocNoNeedAudit                     = errs.New(450216, "当前文档无需送审")
	ErrEmbeddingVersionNotMatch           = errs.New(450220, "embedding 版本不匹配")
	ErrEmbeddingUpgrading                 = errs.New(450221, "embedding 正在升级中")
	ErrAttributeLabelNotSupported         = errs.New(450222, "该类型不支持标签属性")
	ErrAttributeBelongsToDifferentApps    = errs.New(450223, "标签归属于不同应用")
	ErrUnknownDocTypeForIndexType         = errs.New(450224, "未知的文档类型")
	ErrAppUpgrading                       = errs.New(450225, "应用升级中")
	ErrFileSizeTooBig                     = errs.New(450226, "文件超过最大文件限制")
	ErrAttributeLabelEmpty                = errs.New(450227, "标签名称/标识/标准词/同义词为空")
	ErrOverModelTokenLimit                = errs.New(450228, "当前模型token资源余量无法支持本次任务，您可切换模型或联系官网客服进行购买。")
	ErrBotOverCharacterSizeLimit          = errs.New(450229, "当前应用已达字符数上限，请删除部分文档后重新上传或新建应用。")
	ErrReqQaListExceedLimit               = errs.New(450230, "单次请求的问答对数量超过限制")
	ErrGetAppFail                         = errs.New(450300, "获取应用实例失败")
	ErrAppTypeNotFound                    = errs.New(450301, "应用类型不存在")
	ErrAppQuotaNotEnough                  = errs.New(450400, "应用配额不足")
	ErrAppTypeInvalid                     = errs.New(450401, "不支持应用类型")
	ErrNotFoundModel                      = errs.New(450402, "未找到模型配置")
	ErrAppNotFound                        = errs.New(450403, "应用不存在")
	ErrAppTypeSupportFilters              = errs.New(450404, "应用类型不支持检索配置")
	ErrAppScenceNotSupport                = errs.New(450405, "当前应用识别场景不支持")
	ErrAppNotSupportModelList             = errs.New(450406, "未找到相应模型列表")
	ErrAppInitFail                        = errs.New(450407, "初始化应用失败")
	ErrReleaseConfigNotFound              = errs.New(450412, "没有符合条件的配置项")
	ErrCorpTrialExpired                   = errs.New(450415, "企业试用已过期")
	ErrNotInWhiteList                     = errs.New(450416, "不在白名单中")
	ErrNotDefaultModel                    = errs.New(450417, "未找到默认模型配置")
	ErrNotInvalidModel                    = errs.New(450418, "模型名无效")
	ErrNotAppConfig                       = errs.New(450419, "缺失应用配置请求参数")
	ErrAppNotPublish                      = errs.New(450420, "当前应用未发布，请确保发布后重试")
	ErrCorpHasRegister                    = errs.New(450421, "企业已注册")
	ErrModuleNotExist                     = errs.New(450422, "模块不存在")
	ErrCreateIntentFail                   = errs.New(450426, "创建意图失败")
	ErrUpdateIntentFail                   = errs.New(450427, "修改意图信息失败")
	ErrGetIntentPolicyFail                = errs.New(450428, "获取意图策略失败")
	ErrUpdateIntentPolicyFail             = errs.New(450429, "修改策略信息失败")
	ErrCorpAppNotEqual                    = errs.New(450438, "企业与应用归属企业不一致")
	ErrFileUrlFail                        = errs.New(450439, "链接地址不合法")
	ErrFileUrlNotFound                    = errs.New(450440, "链接地址无法访问")
	ErrAppealTypeNotMatch                 = errs.New(450902, "申诉类型和ID不匹配")
	ErrDocQAFileFail                      = errs.New(450903, "导入问答文件类型错误")
	ErrDocIDFail                          = errs.New(450904, "文档ID参数错误")
	ErrDocFilterFlagFail                  = errs.New(450905, "文档筛选标识位错误")
	ErrDocQaTaskNotFound                  = errs.New(460001, "文档生成问答任务不存在")
	ErrDocQaTaskStatusFail                = errs.New(460002, "文档生成问答任务状态错误")
	ErrGetSegmentFail                     = errs.New(460003, "文档生成问答任务查询分片错误")
	ErrGetQaExistsFail                    = errs.New(460004, "查询问答去重逻辑错误")
	ErrGetListQaTaskFail                  = errs.New(460005, "获取生成问答任务列表失败")
	ErrDeleteQaTaskStatusFail             = errs.New(460006, "任务当前状态不可删除")
	ErrCancelQaTaskStatusFail             = errs.New(460007, "任务当前状态不可取消")
	ErrStopQaTaskStatusFail               = errs.New(460008, "任务当前状态不可暂停")
	ErrContinueQaTaskStatusFail           = errs.New(460009, "任务当前状态不可继续")
	ErrQaTaskSegmentFail                  = errs.New(460010, "没有可生成的文档分片")
	ErrQaTaskExistsOrgDataFail            = errs.New(460011, "清空文档问答任务orgData数据失败")
	ErrGeneratingFail                     = errs.New(460012, "该文档已有/生成中/暂停/失败/的任务请处理")
	ErrGeneratingNoTokenBalance           = errs.New(460013, "资源已耗尽，请购买资源后继续")
	ErrModifyQaExpireFail                 = errs.New(460014, "批量编辑QA过期时间有失败项请检查")
	ErrModifyQaDocFail                    = errs.New(460015, "批量编辑QA关联文档有失败项请检查")
	ErrRetryQaTaskStatusFail              = errs.New(460016, "任务当前状态不可重试")
	ErrSynonymsNotFound                   = errs.New(460018, "同义词不存在")
	ErrSynonymsForbidDelete               = errs.New(460019, "同义词不允许删除，请检查当前状态")
	ErrExportSynonyms                     = errs.New(460020, "导出同义词失败")
	ErrExportSynonymsTooMany              = errs.New(460021, "导出同义词条数过多，请减少导出条数")
	ErrSynonymsInvalidStandard            = errs.New(460022, "标准词词条不合法")
	ErrSynonymsInvalidWord                = errs.New(460023, "同义词词条不合法")
	ErrSynonymsTooMany                    = errs.New(460024, "同义词词条过多")
	ErrSynonymsIsDeleted                  = errs.New(460025, "同义词已删除")
	ErrSynonymsListUploading              = errs.New(460026, "同义词列表文件已上传")
	ErrSynonymsInvalidDupError            = errs.New(460027, "无效的同义词词条重复错误")
	ErrSynonymsTaskNotFound               = errs.New(460028, "同义词任务不存在")
	ErrSynonymsTaskImportFailWithConflict = errs.New(460029, "导入同义词失败(存在冲突)")
	ErrAppealCallbackDoing                = errs.New(460030, "申诉回调中，请稍候")
	ErrDescribeDocLimit                   = errs.New(460031, "文档数量超过限制")
	ErrCreateQADocType                    = errs.New(460032, "文档生成问答,文档类型错误")
	ErrStopTestStatus                     = errs.New(460033, "当前评测任务状态不可停止")
	ErrRetryTestStatus                    = errs.New(460034, "当前评测任务状态不可重试")
	ErrDocCannotRename                    = errs.New(460035, "文档当前状态不可重命名")
	ErrGenerateSimilarParams              = errs.New(460035, "生成相似问,主问不可为空")
	ErrDocSegmentPrefixNotMatch           = errs.New(460036, "文档切片前缀与原始文件名不匹配")
	ErrDocNameNotMatch                    = errs.New(460037, "重命名文档原始名称与现有记录不一致")
	ErrDocNameExtNotMatch                 = errs.New(460038, "文档扩展名不匹配")
	ErrDocNameVerifyFailed                = errs.New(460039, "文档名校验失败")
	ErrDocNameNotChanged                  = errs.New(460040, "文档名未变更")
	ErrDocDiffDocNotFound                 = errs.New(460041, "文档不存在,发起对比任务失败")
	ErrCreateDocDiffTaskFail              = errs.New(460042, "已选文档状态更变或删除，无法进行对比")
	ErrCreateDocDiffTaskInTaskFail        = errs.New(460043, "已选文档存在未完成的对比任务,无法进行对比")
	ErrCreateDocDiffTaskInQaFail          = errs.New(460044, "已选文档存在未完成的问答生成任务,无法进行对比")
	ErrCreateDocDiffTaskDescribeFail      = errs.New(460045, "对比任务结果不存在")
	ErrHandleDocDiffSizeFail              = errs.New(460046, "单次处理对比任务条数超过上限")
	ErrHandleDocDiffFail                  = errs.New(460047, "部分对比任务处理无法操作")
	ErrHandleDocDiffNotFound              = errs.New(460048, "对比任务不存在")
	ErrHandleDocDiffNotFail               = errs.New(460049, "无法操作")
	ErrDocDiffQaNotRetry                  = errs.New(460050, "文档对比任务产生的问答任务不支持重试")
	ErrCreateDocDiffTaskDocTypeFail       = errs.New(460051, "已选文档类型不一致,无法进行对比")
	ErrDocDiffTaskRunIng                  = errs.New(460052, "该文档正在进行对比任务,请结束对比后重试")
	ErrHandleDiffTypeOperationFail        = errs.New(460053, "文档对比任务处理类型错误")
	ErrHandleDocOperationFail             = errs.New(460054, "文档对比任务文档操作类型错误")
	ErrHandleQaOperationFail              = errs.New(460055, "文档对比任务问答操作类型错误")
	ErrHandleQaOperationReNameFail        = errs.New(460056, "文档对比任务重命名参数错误")
	ErrDocIsModifyingOrDeleting           = errs.New(460057, "文档正在更新或者删除操作中")
	ErrQaIsModifyingOrDeleting            = errs.New(460058, "问答正在更新或者删除操作中")
	ErrVideoURLFail                       = errs.New(470100, "视频url不支持请检查")
	ErrInvalidDocQaExcel                  = errs.New(470101, "问答模板文件解析异常")
	ErrGenerateQALimitFail                = errs.New(470102, "生成问答文档数量超过单次上限")
	ErrHandleDocDiffTaskInDiffFail        = errs.New(470103, "有相同文档的任务不能同时处理")
	ErrHandleDocDiffTaskDocTypeFail       = errs.New(470104, "当前文档是不可操作的状态")
	ErrHandleDocDiffTaskTypeFail          = errs.New(470105, "当前任务状态不能处理")
	ErrHandleDocDiffTaskDocNotFoundFail   = errs.New(470106, "文档不存在或被删除不能处理")
	ErrInvalidExpireTime                  = errs.New(470107, "自定义到期时间不能早于当前时间")
	ErrUserIdInCustomVariablesIsEmpty     = errs.New(470108, "自定义变量中用户ID为空")
	ErrReleaseMaxCount                    = errs.New(470109, "您的账户未发布文档数量已达到上限，请先发布部分文档后再进行上传。")
	ErrGetReleaseFail                     = errs.New(470110, "获取未发布文档数量失败")
	ErrContextInvalid                     = errs.New(471003, "context无效")
	ErrParameterInvalid                   = errs.New(471004, "请求参数无效")
	ErrRelatedAppExist                    = errs.New(471005, "存在关联的应用")
	ErrCreateSharedKnowledgeAppFailed     = errs.New(471006, "创建共享知识库应用失败")
	ErrDeleteSharedKnowledgeAppFailed     = errs.New(471007, "删除共享知识库应用失败")
	ErrGetUserNameFailed                  = errs.New(471008, "获取用户名称失败")
	ErrCreateSharedKnowledgeRecordFailed  = errs.New(471009, "创建共享知识库记录失败")
	ErrQuerySharedKnowledgeRecordFailed   = errs.New(471010, "检索共享知识库记录失败")
	ErrSharedKnowledgeConvertFailed       = errs.New(471011, "共享知识库转换失败")
	ErrGetShareKnowledgeAppListFailed     = errs.New(471012, "获取知识库应用清单失败")
	ErrDeleteSharedKnowledgeRecordFailed  = errs.New(471013, "删除共享知识库记录失败")
	ErrSharedKnowledgeRecordNotFound      = errs.New(471014, "共享知识库记录不存在")
	ErrKnowledgeSchemaTaskNotFound        = errs.New(471015, "知识库schema任务记录不存在")
	ErrSharedKnowledgeExist               = errs.New(471016, "共享知识库应用名称已存在")
	ErrSharedKnowledgeNameQueryFailed     = errs.New(471017, "共享知识库应用名称检索失败")

	// 连接外部数据库的错误码
	ErrOpenDbSourceFail         = errs.New(4720000, "连接外部数据库失败，请检查地址、端口以及账户密码等信息")
	ErrDbSourceTypeNotSupport   = errs.New(4720001, "数据库类型错误, 范围为 mysql, sql_server")
	ErrDbSourceTableBlankFail   = errs.New(4720002, "选择的数据表不存在内容")
	ErrTypeConvertFail          = errs.New(4720003, "可视化数据转化失败")
	ErrAddDbSourceFail          = errs.New(4720004, "添加失败")
	ErrUpdateDbSourceGetFail    = errs.New(4720005, "更新失败， 获取原始记录失败")
	ErrUpdateDbSourceCreateFail = errs.New(4720006, "更新失败, 创建新记录失败")
	ErrPageNumberInvalid        = errs.New(4720007, "页码不合法")
	ErrDataNotExistOrIsDeleted  = errs.New(4720008, "数据不存在, 或者已经被删除")
	ErrDbTableNumIsInvalid      = errs.New(4720009, "添加数据库表数量异常")
	ErrNameInvalid              = errs.New(4720010, "数据库库名、表名或列名格式错误, 需要以字母开头，仅包含字母数字下划线")
	ErrDbNameBanned             = errs.New(4720011, "默认的系统数据库禁止添加")
	ErrDbNameIsInvalid          = errs.New(4720012, "数据库名称有误")
	ErrPasswordDecodeFail       = errs.New(4720013, "密码有误")
	ErrDbSourceTimeOut          = errs.New(4720014, "连接外部数据库超时")
	ErrDbSourceNoPermission     = errs.New(4720015, "数据不存在或用户权限不足")
	ErrDbSourceTableEmpty       = errs.New(4720016, "数据库表为空")
	ErrDataBase                 = errs.New(4720017, "数据库错误")
	ErrInvalidFields            = errs.New(4720018, "检测到敏感词, 请修改")
	ErrWriteIntoEsFail          = errs.New(4720019, "外部数据写入到检索引擎失败")
	ErrDbTableSizeInvalid       = errs.New(4720020, "数据库添加的数据表大小超过限制")
	ErrDbSourceInputExtraLong   = errs.New(4720021, "输入的参数过长")

	//权限相关报错
	ErrCommonFail                          = errs.New(470107, "系统错误")
	ErrCustUserNameFail                    = errs.New(470108, "用户名称长度错误")
	ErrThirdUserIdFail                     = errs.New(470109, "第三方用户id错误")
	ErrCustUserNameExist                   = errs.New(470110, "用户名称重复")
	ErrLkeUserIdExist                      = errs.New(470111, "第三方用户id重复")
	ErrUserRoleEmpty                       = errs.New(470112, "用户绑定的角色不能为空")
	ErrCreateUserFail                      = errs.New(470113, "创建用户失败")
	ErrUserNotExist                        = errs.New(470113, "用户不存在")
	ErrBatchEditUserId                     = errs.New(470114, "用户业务数组不能为空")
	ErrUpdateUserFail                      = errs.New(470115, "更新用户数据失败")
	ErrGetUserListFail                     = errs.New(470116, "获取用户列表失败")
	ErrGetUserRoleListFail                 = errs.New(470117, "获取用户角色绑定关系失败")
	ErrGetRoleListFail                     = errs.New(470118, "获取角色信息失败")
	ErrDetailUserFail                      = errs.New(470118, "获取用户详情失败")
	ErrDeleteUserFail                      = errs.New(470118, "删除用户失败")
	ErrDeleteUserRoleFail                  = errs.New(470119, "删除用户角色绑定关系失败")
	ErrGetUserConfigFail                   = errs.New(470120, "获取特殊权限配置失败")
	ErrSetCustUserConfigFail               = errs.New(470121, "设置特殊权限配置失败")
	ErrSetThirdAclNotToken                 = errs.New(470122, "token不能为空")
	ErrSetThirdAclNotSecret                = errs.New(470123, "secret_id和secret_key不能为空")
	ErrSetThirdAclTypeFail                 = errs.New(470124, "类型错误")
	ErrSetThirdAclTokenUrlFail             = errs.New(470125, "获取token接口url不能为空")
	ErrSetThirdAclAclUrlFail               = errs.New(470126, "验证权限接口url不能为空")
	ErrSetThirdAclFail                     = errs.New(470127, "设置外部权限接口失败")
	ErrGetThirdAclFail                     = errs.New(470128, "获取外部权限接口失败")
	ErrRoleNameFail                        = errs.New(470129, "角色名称长度错误")
	ErrRoleNameExist                       = errs.New(470130, "角色已存在")
	ErrRoleSearchTypeFail                  = errs.New(470131, "搜索类型错误")
	ErrRoleKnowledgeFail                   = errs.New(470132, "知识点错误")
	ErrRoleKnowledgeTypeFail               = errs.New(470133, "知识点类型错误")
	ErrDeleteRoleFail                      = errs.New(470134, "删除角色失败")
	ErrRoleNotExist                        = errs.New(470135, "角色不存在")
	ErrRoleModifyFail                      = errs.New(470136, "修改角色失败")
	ErrSetCustUserConfigRoleFail           = errs.New(470137, "设置的角色不存在")
	ErrSetThirdAclConfigLock               = errs.New(470138, "其他用户正在操作,请稍后再试")
	ErrSetAppShareKGFailed                 = errs.New(470139, "应用引用共享知识库失败")
	ErrGetAppShareKGListFailed             = errs.New(470140, "应用获取引用共享知识库列表失败")
	ErrRetrieveBaseSharedKGFailed          = errs.New(470141, "获取共享知识库基础信息失败")
	ErrGetAppFailed                        = errs.New(470142, "获取应用信息失败")
	ErrGetKnowledgeFailed                  = errs.New(470143, "获取知识库信息失败")
	ErrDocIsDisabled                       = errs.New(470144, "文档已经是停用状态")
	ErrDocIsEnabled                        = errs.New(470145, "文档已经是启用状态")
	ErrRoleConditionFail                   = errs.New(470146, "角色标签逻辑错误")
	ErrRoleTypeFail                        = errs.New(470147, "角色类型错误")
	ErrQAIsDisabled                        = errs.New(470148, "问答已经是停用状态")
	ErrQAIsEnabled                         = errs.New(470149, "问答已经是启用状态")
	ErrUserMaxLimit                        = errs.New(470150, "用户达到最大上限,不允许在创建")
	ErrRoleMaxLimit                        = errs.New(470151, "角色达到最大上限,不允许在创建")
	ErrGetDocSegmentTooLarge               = errs.New(470152, "获取切片数据超出限制")
	ErrDocSegmentNotFound                  = errs.New(470153, "切片数据未找到")
	ErrDocSegmentKeywordsMaxLimit          = errs.New(470154, "切片关键词长度超出限制")
	ErrGetEmbedding                        = errs.New(470155, "获取向量失败")
	ErrGetEmbeddingEmpty                   = errs.New(470156, "获取向量为空")
	ErrDocNotSupportInterveneFailed        = errs.New(470157, "当前文档状态不支持解析切分干预")
	ErrDocSegmentOperationNotAllowedFailed = errs.New(470158, "当前切片不支持操作")
	ErrDocSegmentSheetNotFound             = errs.New(470159, "表格Sheet信息未找到")

	// 下面的7位错误码不要再增加了，接着上面的6位错误码继续加
	ErrQAIsNotExist                = errs.New(4500141, "该问答不存在")
	ErrRealtimeDocParseFailed      = errs.New(4505001, "实时文档解析失败")
	ErrParamsNotExpected           = errs.New(4505002, "参数校验失败，请检查传参是否符合预期")
	ErrDocCharSizeNotAllowCreateQA = errs.New(4506001, "字符数超限文档不允许创建问答对")
	ErrRetrievalConfig             = errs.New(4507001, "向量召回和关键词召回不可同时关闭")
	ErrNoTokenBalance              = errs.New(4507002, "token已用尽")
	ErrDocNotStable                = errs.New(4508000, "超量检查文档处于非稳态")
	ErrQANotStable                 = errs.New(4508001, "超量检查问答处于非稳态")
)

// TMsgDataCountReqError 数据统计校验报错封装
func TMsgDataCountReqError(msg string) error {
	return errs.New(450600, msg)
}

// ErrWrapf 错误封装
func ErrWrapf(err error, format string, args ...interface{}) error {
	return errs.Wrapf(err, errs.Code(err), format, args...)
}

// ErrWrapf 错误封装
func ErrWrapWithMsgf(err error, format string, args ...interface{}) error {
	return errs.Wrapf(err, errs.Code(err), errs.Msg(err)+format, args...)
}

// ConvertErrMsg 转换错误信息，可根据集成商需求定制错误信息
func ConvertErrMsg(systemIntegratorName string, err error) error {
	if errMap, ok1 := ErrMap[systemIntegratorName]; ok1 {
		if newErr, ok2 := errMap[err]; ok2 {
			return newErr
		}
	}
	return err
}
