package dao

import (
	"context"
	"errors"
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/pkg"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/model"

	"gorm.io/gorm"
)

var globalDocSegmentOrgDataDao *DocSegmentOrgDataDao

const (
	docSegmentOrgDataTableName                = "t_doc_segment_org_data"
	DocSegmentOrgDataTblColBusinessID         = "business_id"
	DocSegmentOrgDataTblColAppBizID           = "app_biz_id"
	DocSegmentOrgDataTblColCorpBizID          = "corp_biz_id"
	DocSegmentOrgDataTblColStaffBizID         = "staff_biz_id"
	DocSegmentOrgDataTblColDocBizID           = "doc_biz_id"
	DocSegmentOrgDataTblColOrgData            = "org_data"
	DocSegmentOrgDataTblColAddMethod          = "add_method"
	DocSegmentOrgDataTblColSegmentType        = "segment_type"
	DocSegmentOrgDataTblColOrgPageNumbers     = "org_page_numbers"
	DocSegmentOrgDataTblColOrgSheetData       = "sheet_data"
	DocSegmentOrgDataTblColIsTemporaryDeleted = "is_temporary_deleted"
	DocSegmentOrgDataTblColIsDeleted          = "is_deleted"
	DocSegmentOrgDataTblColIsDisabled         = "is_disabled"
	DocSegmentOrgDataTblColCreateTime         = "create_time"
	DocSegmentOrgDataTblColUpdateTime         = "update_time"
)

const (
	getSegmentByDocIDAndKeywords = `
		SELECT
    		/*+ MAX_EXECUTION_TIME(20000) */ %s
		FROM
		    %s
		WHERE
		    corp_biz_id = ? AND app_biz_id = ? AND doc_biz_id = ? AND is_deleted = ? AND org_data LIKE ?
		ORDER BY
		    create_time ASC
		LIMIT ?,?
		`
)

var DocSegmentOrgDataTblColList = []string{
	DocSegmentOrgDataTblColBusinessID,
	DocSegmentOrgDataTblColAppBizID,
	DocSegmentOrgDataTblColCorpBizID,
	DocSegmentOrgDataTblColStaffBizID,
	DocSegmentOrgDataTblColDocBizID,
	DocSegmentOrgDataTblColOrgData,
	DocSegmentOrgDataTblColAddMethod,
	DocSegmentOrgDataTblColSegmentType,
	DocSegmentOrgDataTblColOrgPageNumbers,
	DocSegmentOrgDataTblColOrgSheetData,
	DocSegmentOrgDataTblColIsTemporaryDeleted,
	DocSegmentOrgDataTblColIsDisabled,
	DocSegmentOrgDataTblColIsDeleted,
	DocSegmentOrgDataTblColCreateTime,
	DocSegmentOrgDataTblColUpdateTime,
}

type DocSegmentOrgDataDao struct {
	BaseDao
}

// DocSegmentOrgDataDao 获取全局的数据操作对象
func GetDocSegmentOrgDataDao() *DocSegmentOrgDataDao {
	if globalDocSegmentOrgDataDao == nil {
		globalDocSegmentOrgDataDao = &DocSegmentOrgDataDao{*globalBaseDao}
	}
	return globalDocSegmentOrgDataDao
}

type DocSegmentOrgDataFilter struct {
	CorpBizID          uint64
	AppBizID           uint64
	DocBizID           uint64
	BusinessIDs        []uint64
	IsDeleted          *int
	IsTemporaryDeleted *int
	AddMethod          *int
	Keywords           string
	Offset             uint32
	Limit              uint32
	OrderColumn        []string
	OrderDirection     []string
}

// 生成查询条件，必须按照索引的顺序排列
func (d *DocSegmentOrgDataDao) generateCondition(ctx context.Context, session *gorm.DB, filter *DocSegmentOrgDataFilter) {
	if filter.CorpBizID != 0 {
		session.Where(DocSegmentOrgDataTblColCorpBizID+sqlEqual, filter.CorpBizID)
	}
	if filter.AppBizID != 0 {
		session.Where(DocSegmentOrgDataTblColAppBizID+sqlEqual, filter.AppBizID)
	}
	if len(filter.BusinessIDs) > 0 {
		session = session.Where(DocSegmentOrgDataTblColBusinessID+sqlIn, filter.BusinessIDs)
	}
	if filter.DocBizID != 0 {
		session = session.Where(DocSegmentOrgDataTblColDocBizID+sqlEqual, filter.DocBizID)
	}
	// is_deleted必须加入查询条件
	if filter.IsDeleted != nil {
		session.Where(DocSegmentOrgDataTblColIsDeleted+sqlEqual, *filter.IsDeleted)
	}
	if filter.IsTemporaryDeleted != nil {
		session.Where(DocSegmentOrgDataTblColIsTemporaryDeleted+sqlEqual, *filter.IsTemporaryDeleted)
	}
	if filter.AddMethod != nil {
		session.Where(DocSegmentOrgDataTblColAddMethod+sqlEqual, *filter.AddMethod)
	}
}

// GetSegmentByDocID 获取文档切片
func (d *DocSegmentOrgDataDao) GetSegmentByDocID(ctx context.Context, selectColumns []string, filter *DocSegmentOrgDataFilter) (
	[]*model.DocSegmentOrgData, error) {
	orgDataList := make([]*model.DocSegmentOrgData, 0)
	if filter == nil {
		log.ErrorContextf(ctx, "GetSegmentByDocID|filter is null")
		return orgDataList, pkg.ErrSystem
	}
	if filter.Limit == 0 {
		// 为0正常返回空结果即可
		return orgDataList, nil
	}
	if filter.Limit > DocSegmentInterveneMaxPageSize {
		// 限制单次查询最大条数
		log.ErrorContextf(ctx, "GetSegmentByDocID|is limit too large:%d", filter.Limit)
		return orgDataList, pkg.ErrGetDocSegmentTooLarge
	}
	var session *gorm.DB
	if filter.Keywords != "" {
		log.InfoContextf(ctx, "getSegmentByDocIDAndKeywords")
		querySQL := fmt.Sprintf(getSegmentByDocIDAndKeywords, strings.Join(selectColumns, ","),
			docSegmentOrgDataTableName)
		keywordsArg := fmt.Sprintf("%%%s%%", special.Replace(filter.Keywords))
		res := d.tdsqlGormDB.WithContext(ctx).Raw(querySQL, filter.CorpBizID, filter.AppBizID, filter.DocBizID,
			model.DocIsNotDeleted, keywordsArg, filter.Offset, filter.Limit).Scan(&orgDataList)
		if res.Error != nil {
			log.ErrorContextf(ctx, "GetSegmentByDocID|execute sql failed|err: %+v", res.Error)
			return orgDataList, res.Error
		}
		return orgDataList, nil
	}
	session = d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTableName).Select(selectColumns)
	d.generateCondition(ctx, session, filter)
	session = session.Offset(int(filter.Offset)).Limit(int(filter.Limit))
	if len(filter.OrderColumn) == len(filter.OrderDirection) {
		for i, orderColumn := range filter.OrderColumn {
			if filter.OrderDirection[i] != SqlOrderByAsc && filter.OrderDirection[i] != SqlOrderByDesc {
				log.ErrorContextf(ctx, "GetSegmentByDocID|invalid order direction:%s", filter.OrderDirection[i])
				continue
			}
			session.Order(orderColumn + " " + filter.OrderDirection[i])
		}
	}
	res := session.Find(&orgDataList)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetSegmentByDocID|execute sql failed|err: %+v", res.Error)
		return orgDataList, res.Error
	}
	return orgDataList, nil
}

// GetDocOrgDataByBizID 获取单个切片数据
func (d *DocSegmentOrgDataDao) GetDocOrgDataByBizID(ctx context.Context, selectColumns []string, corpBizID,
	appBizID, docBizID, businessID uint64) (*model.DocSegmentOrgData, error) {
	orgData := &model.DocSegmentOrgData{}
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTableName).Select(selectColumns)
	session = session.Where(DocSegmentOrgDataTblColCorpBizID+sqlEqual, corpBizID).Where(
		DocSegmentOrgDataTblColAppBizID+sqlEqual, appBizID).Where(
		DocSegmentOrgDataTblColDocBizID+sqlEqual, docBizID).Where(
		DocSegmentOrgDataTblColBusinessID+sqlEqual, businessID).Where(
		DocSegmentOrgDataTblColIsDeleted+sqlEqual, IsNotDeleted).Where(
		DocSegmentOrgDataTblColIsTemporaryDeleted+sqlEqual, IsNotDeleted)
	res := session.Take(&orgData)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetDocOrgDataByBizID execute sql failed, err: %+v", res.Error)
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return orgData, res.Error
		}
	}
	return orgData, nil
}

// GetDocOrgDataCountByDocBizID 获取切片总数
func (d *DocSegmentOrgDataDao) GetDocOrgDataCountByDocBizID(ctx context.Context, corpBizID,
	appBizID, docBizID uint64) (int64, error) {
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTableName)
	session = session.Where(DocSegmentOrgDataTblColCorpBizID+sqlEqual, corpBizID).Where(
		DocSegmentOrgDataTblColAppBizID+sqlEqual, appBizID).Where(
		DocSegmentOrgDataTblColDocBizID+sqlEqual, docBizID).Where(
		DocSegmentOrgDataTblColIsDeleted+sqlEqual, IsNotDeleted).Where(
		DocSegmentOrgDataTblColIsTemporaryDeleted+sqlEqual, IsNotDeleted)
	count := int64(0)
	res := session.Count(&count)
	if res.Error != nil {
		log.ErrorContextf(ctx, "execute sql failed, err: %+v", res.Error)
		return 0, res.Error
	}
	return count, nil
}

// GetLastOrgDataByCurrentOrgDataBizID 获取该切片的上一个切片，依赖business_id顺序
func (d *DocSegmentOrgDataDao) GetLastOrgDataByCurrentOrgDataBizID(ctx context.Context, selectColumns []string, corpBizID,
	appBizID, docBizID, curBusinessID uint64) (*model.DocSegmentOrgData, error) {
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTableName).Select(selectColumns)
	session = session.Where(DocSegmentOrgDataTblColCorpBizID+sqlEqual, corpBizID).Where(
		DocSegmentOrgDataTblColAppBizID+sqlEqual, appBizID).Where(
		DocSegmentOrgDataTblColDocBizID+sqlEqual, docBizID).Where(
		DocSegmentOrgDataTblColIsTemporaryDeleted+sqlEqual, IsNotDeleted).Where(
		DocSegmentOrgDataTblColBusinessID+sqlLess, curBusinessID).Order(
		DocSegmentOrgDataTblColBusinessID + " " + SqlOrderByDesc)
	orgData := &model.DocSegmentOrgData{}
	res := session.Take(&orgData)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetLastOrgDataByCurrentOrgDataBizID execute sql failed, err: %+v", res.Error)
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return orgData, res.Error
		}
	}
	return orgData, nil
}

// GetAfterOrgDataByCurrentOrgDataBizID 获取该切片的下一个切片，依赖business_id顺序
func (d *DocSegmentOrgDataDao) GetAfterOrgDataByCurrentOrgDataBizID(ctx context.Context, selectColumns []string, corpBizID,
	appBizID, docBizID, curBusinessID uint64) (*model.DocSegmentOrgData, error) {
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTableName).Select(selectColumns)
	session = session.Where(DocSegmentOrgDataTblColCorpBizID+sqlEqual, corpBizID).Where(
		DocSegmentOrgDataTblColAppBizID+sqlEqual, appBizID).Where(
		DocSegmentOrgDataTblColDocBizID+sqlEqual, docBizID).Where(
		DocSegmentOrgDataTblColIsTemporaryDeleted+sqlEqual, IsNotDeleted).Where(
		DocSegmentOrgDataTblColBusinessID+sqlMore, curBusinessID).Order(
		DocSegmentOrgDataTblColBusinessID + " " + SqlOrderByAsc)
	orgData := &model.DocSegmentOrgData{}
	res := session.Take(&orgData)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetAfterOrgDataByCurrentOrgDataBizID execute sql failed, err: %+v", res.Error)
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return orgData, res.Error
		}
	}
	return orgData, nil
}

// CreateDocSegmentOrgData 创建org_data
func (d *DocSegmentOrgDataDao) CreateDocSegmentOrgData(ctx context.Context, orgData *model.DocSegmentOrgData) error {
	res := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTableName).Create(&orgData)
	if res.Error != nil {
		log.ErrorContextf(ctx, "CreateDocSegmentOrgData execute sql failed, err: %+v", res.Error)
		return res.Error
	}
	return nil
}

// UpdateDocSegmentOrgData 更新org_data
func (d *DocSegmentOrgDataDao) UpdateDocSegmentOrgData(ctx context.Context, tx *gorm.DB, updateColumns []string,
	corpBizID uint64, appBizID uint64, docBizID uint64, businessIDs []uint64, orgData *model.DocSegmentOrgData) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	session := tx.WithContext(ctx).Table(docSegmentOrgDataTableName).Select(updateColumns).
		Where(DocSegmentOrgDataTblColCorpBizID+sqlEqual, corpBizID).
		Where(DocSegmentOrgDataTblColAppBizID+sqlEqual, appBizID).
		Where(DocSegmentOrgDataTblColDocBizID+sqlEqual, docBizID).
		Where(DocSegmentOrgDataTblColBusinessID+sqlIn, businessIDs)
	res := session.Updates(orgData)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateDocSegmentOrgData failed for businessId: %d, err: %+v", businessIDs, res.Error)
		return res.Error
	}
	return nil
}

// UpdateDocSegmentOrgDataByDocBizID 更新文档的org_data
func (d *DocSegmentOrgDataDao) UpdateDocSegmentOrgDataByDocBizID(ctx context.Context, tx *gorm.DB, updateColumns []string,
	corpBizID uint64, appBizID uint64, docBizID uint64, orgData *model.DocSegmentOrgData) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	session := tx.WithContext(ctx).Table(docSegmentOrgDataTableName).Select(updateColumns).
		Where(DocSegmentOrgDataTblColCorpBizID+sqlEqual, corpBizID).
		Where(DocSegmentOrgDataTblColAppBizID+sqlEqual, appBizID).
		Where(DocSegmentOrgDataTblColDocBizID+sqlEqual, docBizID)
	res := session.Updates(orgData)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateDocSegmentOrgData failed for DocBizID: %d, err: %+v", docBizID, res.Error)
		return res.Error
	}
	return nil
}

// DeleteDocSegmentOrgData 删除org_data
func (d *DocSegmentOrgDataDao) DeleteDocSegmentOrgData(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, businessIDs []uint64) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentOrgDataTblColIsDeleted, DocSegmentOrgDataTblColUpdateTime}
	segment := &model.DocSegmentOrgData{
		IsDeleted:  IsDeleted,  // 是否删除
		UpdateTime: time.Now(), // 更新时间
	}
	if err := d.UpdateDocSegmentOrgData(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, businessIDs, segment); err != nil {
		log.ErrorContextf(ctx, "DeleteDocSegmentSheet failed for businessIds: %+v, err: %+v", businessIDs, err)
		return err
	}
	return nil
}

// TemporaryDeleteDocSegmentOrgData 临时删除org_data
func (d *DocSegmentOrgDataDao) TemporaryDeleteDocSegmentOrgData(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, businessIDs []uint64) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentOrgDataTblColIsTemporaryDeleted, DocSegmentOrgDataTblColUpdateTime}
	segment := &model.DocSegmentOrgData{
		IsTemporaryDeleted: IsDeleted,  // 是否删除
		UpdateTime:         time.Now(), // 更新时间
	}
	if err := d.UpdateDocSegmentOrgData(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, businessIDs, segment); err != nil {
		log.ErrorContextf(ctx, "DeleteDocSegmentSheet failed for businessIds: %+v, err: %+v", businessIDs, err)
		return err
	}
	return nil
}

// DisabledDocSegmentOrgData 停用切片
func (d *DocSegmentOrgDataDao) DisabledDocSegmentOrgData(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, businessIDs []uint64) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentOrgDataTblColIsDisabled, DocSegmentOrgDataTblColUpdateTime}
	segment := &model.DocSegmentOrgData{
		IsDisabled: model.SegmentIsDisabled, // 停用切片
		UpdateTime: time.Now(),              // 更新时间
	}
	if err := d.UpdateDocSegmentOrgData(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, businessIDs, segment); err != nil {
		log.ErrorContextf(ctx, "DisabledDocSegmentOrgData| failed for businessIds: %+v, err: %+v", businessIDs, err)
		return err
	}
	return nil
}

// EnableDocSegmentOrgData 启用切片
func (d *DocSegmentOrgDataDao) EnableDocSegmentOrgData(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, businessIDs []uint64) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentOrgDataTblColIsDisabled, DocSegmentOrgDataTblColUpdateTime}
	segment := &model.DocSegmentOrgData{
		IsDisabled: model.SegmentIsEnable, // 启用切片
		UpdateTime: time.Now(),            // 更新时间
	}
	if err := d.UpdateDocSegmentOrgData(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, businessIDs, segment); err != nil {
		log.ErrorContextf(ctx, "EnableDocSegmentOrgData| failed for businessIds: %+v, err: %+v", businessIDs, err)
		return err
	}
	return nil
}

// DeleteDocSegmentOrgDataByDocBizID 删除文档的org_data
func (d *DocSegmentOrgDataDao) DeleteDocSegmentOrgDataByDocBizID(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentOrgDataTblColIsDeleted, DocSegmentOrgDataTblColUpdateTime}
	segment := &model.DocSegmentOrgData{
		IsDeleted:  IsDeleted,  // 是否删除
		UpdateTime: time.Now(), // 更新时间
	}
	if err := d.UpdateDocSegmentOrgDataByDocBizID(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, segment); err != nil {
		log.ErrorContextf(ctx, "DeleteDocSegmentOrgDataByDocBizID failed for DocBizID:%d, err: %+v", docBizID, err)
		return err
	}
	return nil
}

// RealityDeleteDocSegmentOrgDataByDocBizID 硬性删除文档的org_data
func (d *DocSegmentOrgDataDao) RealityDeleteDocSegmentOrgDataByDocBizID(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	session := tx.WithContext(ctx).Table(docSegmentOrgDataTableName).
		Where(DocSegmentOrgDataTblColCorpBizID+sqlEqual, corpBizID).
		Where(DocSegmentOrgDataTblColAppBizID+sqlEqual, appBizID).
		Where(DocSegmentOrgDataTblColDocBizID+sqlEqual, docBizID)
	res := session.Delete(&model.DocSegmentOrgData{})
	if res.Error != nil {
		log.ErrorContextf(ctx, "RealityDeleteDocSegmentOrgDataByDocBizID|docBizID:%d|err:%+v",
			docBizID, res.Error)
		return res.Error
	}
	return nil
}
