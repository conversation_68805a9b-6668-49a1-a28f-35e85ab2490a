// Package dao TODO
// @Author: halelv
// @Date: 2024/5/24 21:25
package dao

import (
	"context"
	"fmt"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/config"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/model"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/util"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/pkg"
	"git.woa.com/dialogue-platform/common/v3/errors"
	pb "git.woa.com/dialogue-platform/lke_proto/pb-protocol/knowledge"
	jsoniter "github.com/json-iterator/go"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// ParseOfflineDocTaskResult 解析实时文档解析结果
// // 1. 结果文件下载并解析
// // 2. 解析结果同步数据库
func (d *dao) ParseOfflineDocTaskResult(ctx context.Context,
	doc *model.Doc, docParse model.DocParse, segmentType uint32) (err error) {
	log.InfoContextf(ctx, "ParseOfflineDocTaskResult|doc:%+v, docParse:%+v, segmentType:%d",
		doc, docParse, segmentType)
	// 获取解析结果COS URL
	if docParse.Result == "" {
		return pkg.ErrDocParseCosURLNotFound
	}
	result := &pb.FileParserCallbackReq{}
	err = jsoniter.UnmarshalFromString(docParse.Result, result)
	if err != nil {
		log.ErrorContextf(ctx, "ParseOfflineDocTaskResult|jsoniter.UnmarshalFromString failed, err:%+v", err)
		return err
	}
	printFileParserPretty(ctx, result)
	log.InfoContextf(ctx, "ParseOfflineDocTaskResult|taskResult:%+v", result)
	resultDataMap := result.GetResults()

	// 全局数据（需要保证并发安全）
	// 短链接
	shortURLSyncMap := &sync.Map{}
	// OrgData
	// 创建哈希表存储唯一字符串和OrgData的ID
	orgDataSyncMap := &sync.Map{}
	// BigData
	// 创建哈希表存储唯一字符串和BigData的ID
	bigDataSyncMap := &sync.Map{}
	// text2sql/tableID
	// 为了解决一个table(如：Excel中的sheet）被切分到多个cos文件中时，保证TableID是相同的
	// key: <string>, ${DocID}_${TableName} value: <uint64>, TableID
	text2sqlTableIDMap := &sync.Map{}
	// 切片图片表存储唯一图片URL和图片ID
	imageDataSyncMap := &sync.Map{}

	wg := sync.WaitGroup{}
	wg.Add(3)

	errChan := make(chan error, 3)

	// 处理split数据
	go func() {
		defer errors.PanicHandler()
		// 解析拆分的文档片段
		log.InfoContextf(ctx, "ParseOfflineDocTaskResult|handler TYPE_DOC")
		if err := d.handlerOfflineSplitData(ctx, doc, segmentType,
			shortURLSyncMap, orgDataSyncMap, bigDataSyncMap, text2sqlTableIDMap, imageDataSyncMap,
			resultDataMap[int32(pb.FileParserSubDataType_FILE_PARSER_SUB_DATA_TYPE_DOC)]); err != nil {
			errChan <- err
		}
		wg.Done()
	}()
	go func() {
		defer errors.PanicHandler()
		// 解析拆分的表格片段
		log.InfoContextf(ctx, "ParseOfflineDocTaskResult|handler TYPE_ES_TABLE")
		if err := d.handlerOfflineSplitData(ctx, doc, segmentType,
			shortURLSyncMap, orgDataSyncMap, bigDataSyncMap, text2sqlTableIDMap, imageDataSyncMap,
			resultDataMap[int32(pb.FileParserSubDataType_FILE_PARSER_SUB_DATA_TYPE_ES_TABLE)]); err != nil {
			errChan <- err
		}
		wg.Done()
	}()
	go func() {
		defer errors.PanicHandler()
		// 解析拆分的Text2Sql片段
		log.InfoContextf(ctx, "ParseOfflineDocTaskResult|handler TYPE_TEXT2SQL_TABLE")
		if err := d.handlerOfflineSplitData(ctx, doc, segmentType,
			shortURLSyncMap, orgDataSyncMap, bigDataSyncMap, text2sqlTableIDMap, imageDataSyncMap,
			resultDataMap[int32(pb.FileParserSubDataType_FILE_PARSER_SUB_DATA_TYPE_TEXT2SQL_TABLE)]); err != nil {
			errChan <- err
		}
		wg.Done()
	}()

	wg.Wait()

	for {
		select {
		case err = <-errChan:
			log.ErrorContextf(ctx, "ParseOfflineDocTaskResult|failed|err:%+v", err)
			return err
		default:
			log.InfoContextf(ctx, "ParseOfflineDocTaskResult|success|doc:%+v", doc)
			return nil
		}
	}
}

// GetOfflineDocParseResult 获取解析结果
func (d *dao) GetOfflineDocParseResult(ctx context.Context, docParse model.DocParse) (result string, err error) {
	cbReq := &pb.FileParserCallbackReq{}
	err = jsoniter.UnmarshalFromString(docParse.Result, cbReq)
	if err != nil {
		log.ErrorContextf(ctx, "GetOfflineDocParseResult|jsoniter.UnmarshalFromString failed, err:%+v", err)
		return "", err
	}
	cosURL := cbReq.GetDebugInfo().GetParseResultCosUrl()
	if len(cosURL) == 0 {
		return "", nil
	}
	parse := new(pb.ParseResult)
	parseResultObj, err := d.GetObject(ctx, cosURL)
	if err != nil {
		log.ErrorContextf(ctx, "GetOfflineDocParseResult:%+v,err:%+v", cosURL, err)
		return "", err
	}
	err = proto.Unmarshal(parseResultObj, parse)
	if err != nil {
		log.ErrorContextf(ctx, "GetOfflineDocParseResult.proto Unmarshal data err:%+v", err)
		return "", err
	}
	log.InfoContextf(ctx, "KBAgentGetOneDocSummary parseResult.Result:%+v", parse.Result)
	return parse.Result, nil
}

// handlerOfflineSplitData 处理拆分数据
func (d *dao) handlerOfflineSplitData(ctx context.Context, doc *model.Doc, segmentType uint32,
	shortURLSyncMap, orgDataSyncMap, bigDataSyncMap, text2sqlTableIDMap, imageDataSyncMap *sync.Map,
	data *pb.FileParserCallbackReq_DataResult) (err error) {
	log.InfoContextf(ctx, "handlerOfflineSplitData|doc:%+v, segmentType:%d, data:%+v", doc, segmentType, data)
	if data == nil {
		log.InfoContextf(ctx, "handlerOfflineSplitData|data is nil, ignore")
		return nil
	}
	if data.TotalFileNumber == 0 || data.TotalFileNumber != int32(len(data.Result)) {
		err = fmt.Errorf("data:%+v illegal", data)
		return err
	}

	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// 解析结果channel
	parseResultChan := make(chan parseResult, data.TotalFileNumber)

	// 并发下载解析文件
	wg := &sync.WaitGroup{}
	wg.Add(int(data.TotalFileNumber))

	for _, res := range data.Result {
		go func(ctx context.Context, res *pb.FileParserCallbackReq_Result, shortURLSyncMap *sync.Map) {
			defer errors.PanicHandler()
			log.InfoContextf(ctx, "handlerOfflineSplitData|res:%+v goroutine init", res)
			select {
			case <-ctx.Done():
				wg.Done()
				log.InfoContextf(ctx, "handlerOfflineSplitData|res:%+v goroutine ctx.Done()", res)
				return
			default:
				// 获取worker资源
				<-fileCfg.offlineWorkerChan
				defer func() {
					wg.Done()
					// 释放worker资源
					fileCfg.offlineWorkerChan <- struct{}{}
				}()
				log.InfoContextf(ctx, "handlerOfflineSplitData|res:%+v goroutine run", res)
				// 解析文件
				docPageContents, tablePageContents, text2SqlResults, err :=
					d.getSplitDataFromCosURL(ctx, shortURLSyncMap, d.storageCli.GetBucket(ctx), res.Result)
				if err != nil {
					log.ErrorContextf(ctx, "handlerOfflineSplitData|getSplitDataFromCosURL failed, "+
						"err:%+v", err)
				}
				parseResultChan <- parseResult{
					index:             res.CurrentFileIndex,
					err:               err,
					docPageContents:   docPageContents,
					tablePageContents: tablePageContents,
					text2SqlResults:   text2SqlResults,
				}
				log.InfoContextf(ctx, "handlerOfflineSplitData|res:%+v goroutine done", res)
			}
		}(ctx, res, shortURLSyncMap)
	}

	// 等待所有解析 goroutine 完成并关闭解析结果channel
	go func(ctx context.Context) {
		defer errors.PanicHandler()
		wg.Wait()
		close(parseResultChan)
		log.InfoContextf(ctx, "handlerOfflineSplitData|all res data complete")
	}(ctx)

	// 顺序处理解析结果
	err = d.dealOfflineSplitResultByOrder(ctx, doc,
		shortURLSyncMap, orgDataSyncMap, bigDataSyncMap, text2sqlTableIDMap, imageDataSyncMap, segmentType, parseResultChan)
	if err != nil {
		log.ErrorContextf(ctx, "handlerOfflineSplitData|dealOfflineSplitResultByOrder failed, err:%+v", err)
		return err
	}
	return nil
}

// dealOfflineSplitResultByOrder 顺序处理拆分结果数据
func (d *dao) dealOfflineSplitResultByOrder(ctx context.Context, doc *model.Doc,
	shortURLSyncMap, orgDataSyncMap, bigDataSyncMap, text2sqlTableIDMap, imageDataSyncMap *sync.Map,
	segmentType uint32, parseResultChan chan parseResult) (err error) {
	log.InfoContextf(ctx, "dealOfflineSplitResultByOrder|run|doc:%+v, segmentType:%d", doc, segmentType)
	nextIndex := int32(0)
	resultMap := make(map[int32]parseResult)

	for resultChan := range parseResultChan {
		if resultChan.err != nil {
			return resultChan.err
		}

		resultMap[resultChan.index] = resultChan

		// 顺序处理
		for {
			if result, ok := resultMap[nextIndex]; ok {
				log.InfoContextf(ctx, "dealOfflineSplitResultByOrder|dealIndex:%d", nextIndex)
				var releaseStatus uint32
				var typ int
				if segmentType == model.SegmentTypeIndex {
					releaseStatus, typ = model.SegmentReleaseStatusInit, model.SegmentTypeIndex
				}
				if segmentType == model.SegmentTypeQA {
					releaseStatus, typ = model.SegmentReleaseStatusNotRequired, model.SegmentTypeQA
				}
				var segments []*model.DocSegmentExtend
				segments, err = d.newOfflineDocSegmentFromPageContent(ctx, doc, result.docPageContents,
					result.tablePageContents, result.text2SqlResults, releaseStatus, text2sqlTableIDMap, typ)
				if err != nil {
					return err
				}
				// 写DB 存储BigData和OrgData
				if err = d.createSegmentWithBigData(ctx,
					shortURLSyncMap, orgDataSyncMap, bigDataSyncMap, imageDataSyncMap, segments, doc.RobotID); err != nil {
					return err
				}
				// 释放内存
				delete(resultMap, nextIndex)
				nextIndex++
			} else {
				break
			}
		}
	}

	// 处理完成后Map应该不会有值
	if len(resultMap) > 0 {
		err = fmt.Errorf("resultMap has unprocessed data, err:%+v", err)
		log.ErrorContextf(ctx, "dealOfflineSplitResultByOrder|failed, err:%+v|len(resultMap):%d, nextIndex:%d",
			err, len(resultMap), nextIndex)
		return err
	}

	log.InfoContextf(ctx, "dealOfflineSplitResultByOrder|done")
	return nil
}

func replacePrefix(org string, title string, renamePrefix string) string {
	if renamePrefix == "" {
		return org
	}
	return strings.Replace(org, title, renamePrefix, 1)
}

// newOfflineDocSegmentFromPageContent 转换为DB存储segment
func (d *dao) newOfflineDocSegmentFromPageContent(ctx context.Context, doc *model.Doc,
	docPageContents, tablePageContents []*pb.PageContent, text2SQLTables *pb.Tables,
	releaseStatus uint32, text2sqlTableIDMap *sync.Map, typ int) ([]*model.DocSegmentExtend, error) {
	title := strings.TrimSuffix(doc.FileName, filepath.Ext(doc.FileName)) + ": \n"
	segments := make([]*model.DocSegmentExtend, 0, len(docPageContents))
	// 文档
	renamePrefix := ""
	if doc.FileNameInAudit != "" {
		// 文档导入重命名的场景,需要将文档切片的前缀替换成新的前缀
		renamePrefix = util.FileNameNoSuffix(doc.FileNameInAudit) + ": \n"
	}
	if text2SQLTables != nil && text2SQLTables.MetaData != nil {
		text2SQLTables.MetaData.FileName = replacePrefix(text2SQLTables.MetaData.FileName, title, renamePrefix)
	}
	for _, pageContent := range docPageContents {
		pgContent := replacePrefix(util.String(pageContent), title, renamePrefix)
		orgData := replacePrefix(pageContent.GetPageContentOrgString(), title, renamePrefix)
		if err := d.checkPageContentAndOrgDataLen(ctx, doc.ID, pgContent, orgData); err != nil {
			return nil, err
		}
		segments = append(segments, &model.DocSegmentExtend{
			DocSegment: model.DocSegment{
				RobotID:         doc.RobotID,
				CorpID:          doc.CorpID,
				StaffID:         doc.StaffID,
				DocID:           doc.ID,
				Outputs:         "",
				FileType:        doc.FileType,
				SegmentType:     model.SegmentTypeSegment,
				Title:           replacePrefix(title, title, renamePrefix),
				PageContent:     pgContent,
				OrgData:         orgData,
				SplitModel:      "",
				Status:          model.SegmentStatusInit,
				ReleaseStatus:   releaseStatus,
				IsDeleted:       model.SegmentIsNotDeleted,
				Type:            typ,
				NextAction:      model.SegNextActionAdd,
				RichTextIndex:   int(pageContent.GetRichContentId()),
				UpdateTime:      time.Now(),
				StartChunkIndex: int(pageContent.GetOrgStart()),
				EndChunkIndex:   int(pageContent.GetOrgEnd()),
				LinkerKeep:      pageContent.GetLinkerKeep(),
				CreateTime:      time.Now(),
				BatchID:         doc.BatchID,
				BigStart:        pageContent.GetBigStart(),
				BigEnd:          pageContent.GetBigEnd(),
				BigString:       replacePrefix(pageContent.GetPageContentBigString(), title, renamePrefix),
				Images:          pageContent.GetImages(),
				OrgPageNumbers:  d.convertOrgPageNumbers2Str(ctx, pageContent.GetOrgPageNumbers()),
				BigPageNumbers:  d.convertBigPageNumbers2Str(ctx, pageContent.GetBigPageNumbers()),
				SheetData:       d.convertSheetData2Str(ctx, pageContent.GetSheetData()),
			},
			ExpireStart: doc.ExpireStart,
			ExpireEnd:   doc.ExpireEnd,
		})
	}
	// 表格
	for _, pageContent := range tablePageContents {
		pgContent := util.String(pageContent)
		orgData := pageContent.GetPageContentOrgString()
		if err := d.checkPageContentAndOrgDataLen(ctx, doc.ID, pgContent, orgData); err != nil {
			return nil, err
		}
		segments = append(segments, &model.DocSegmentExtend{
			DocSegment: model.DocSegment{
				RobotID:         doc.RobotID,
				CorpID:          doc.CorpID,
				StaffID:         doc.StaffID,
				DocID:           doc.ID,
				Outputs:         "",
				FileType:        doc.FileType,
				SegmentType:     model.SegmentTypeTable,
				Title:           replacePrefix(title, title, renamePrefix),
				PageContent:     pgContent,
				OrgData:         orgData,
				SplitModel:      "",
				Status:          model.SegmentStatusInit,
				ReleaseStatus:   releaseStatus,
				IsDeleted:       model.SegmentIsNotDeleted,
				Type:            typ,
				NextAction:      model.SegNextActionAdd,
				RichTextIndex:   int(pageContent.GetRichContentId()),
				UpdateTime:      time.Now(),
				StartChunkIndex: int(pageContent.GetOrgStart()),
				EndChunkIndex:   int(pageContent.GetOrgEnd()),
				LinkerKeep:      pageContent.GetLinkerKeep(),
				CreateTime:      time.Now(),
				BatchID:         doc.BatchID,
				BigStart:        pageContent.GetBigStart(),
				BigEnd:          pageContent.GetBigEnd(),
				BigString:       pageContent.GetPageContentBigString(),
				Images:          pageContent.GetImages(),
				OrgPageNumbers:  d.convertOrgPageNumbers2Str(ctx, pageContent.GetOrgPageNumbers()),
				BigPageNumbers:  d.convertBigPageNumbers2Str(ctx, pageContent.GetBigPageNumbers()),
				SheetData:       d.convertSheetData2Str(ctx, pageContent.GetSheetData()),
			},
			ExpireStart: doc.ExpireStart,
			ExpireEnd:   doc.ExpireEnd,
		})
	}
	s0 := d.appendText2sqlSegments(ctx, doc, replacePrefix(title, title, renamePrefix), text2SQLTables, releaseStatus, text2sqlTableIDMap, typ)
	if len(s0) > 0 {
		segments = append(segments, s0...)
	}
	return segments, nil
}

// convertOrgPageNumbers2Str 转换切片页码信息
func (d *dao) convertOrgPageNumbers2Str(ctx context.Context, orgPageNumbers []int32) string {
	if len(orgPageNumbers) == 0 {
		return ""
	}
	orgPageStr, err := jsoniter.MarshalToString(orgPageNumbers)
	if err != nil {
		log.ErrorContextf(ctx, "convertOrgPageNumbers2Str|jsoniter.MarshalToString|"+
			"failed|orgPageNumbers:%+v|err:%+v", orgPageNumbers, err)
	}
	return orgPageStr
}

// convertBigPageNumbers2Str 转换切片页码信息
func (d *dao) convertBigPageNumbers2Str(ctx context.Context, bigPageNumbers []int32) string {
	if len(bigPageNumbers) == 0 {
		return ""
	}
	bigPageStr, err := jsoniter.MarshalToString(bigPageNumbers)
	if err != nil {
		log.ErrorContextf(ctx, "convertBigPageNumbers2Str|jsoniter.MarshalToString|"+
			"failed|bigPageNumbers:%+v|err:%+v", bigPageNumbers, err)
	}
	return bigPageStr
}

// convertSheetData2Str 转换切片页码信息
func (d *dao) convertSheetData2Str(ctx context.Context, sheetData []*pb.PageContent_SheetData) string {
	if len(sheetData) == 0 {
		return ""
	}
	for _, data := range sheetData {
		if data == nil {
			log.ErrorContextf(ctx, "convertSheetData2Str|sheetData slice is nil|"+
				"sheetData:%+v", sheetData)
			return ""
		}
	}
	sheetPageStr, err := jsoniter.MarshalToString(sheetData)
	if err != nil {
		log.ErrorContextf(ctx, "convertSheetData2Str|jsoniter.MarshalToString|"+
			"failed|sheetData:%+v|err:%+v", sheetData, err)
	}
	return sheetPageStr
}

// appendText2sqlSegments 追加text2sql的分片
// purpose: 用于QA还是文档索引的Index
func (d *dao) appendText2sqlSegments(ctx context.Context, doc *model.Doc, title string, tableFiles *pb.Tables,
	releaseStatus uint32, text2sqlTableIDMap *sync.Map, purpose int) []*model.DocSegmentExtend {

	segments := make([]*model.DocSegmentExtend, 0)
	if tableFiles == nil {
		log.WarnContextf(ctx, "appendText2sqlSegments|DocID:%d|tableFiles is empty", doc.ID)
		return nil
	}

	options := protojson.MarshalOptions{
		UseEnumNumbers:  false, // 使用枚举的名称(字符串)
		EmitUnpopulated: false, // 是否包含未设置的值
		// Indent:          "  ",
		EmitDefaultValues: true,
	}
	// 将 protobuf 消息序列化为 JSON
	tableStr, _ := options.Marshal(tableFiles)
	log.InfoContextf(ctx, "appendText2sqlSegments|DocID:%d|tableStr:%s", doc.ID, string(tableStr))

	meta, contents, err := d.buildText2sqlSegments(ctx, doc, text2sqlTableIDMap, tableFiles)
	if err != nil {
		log.ErrorContextf(ctx, "appendText2sqlSegments|buildText2sqlSegments|DocID:%d|err:%+v|", doc.ID, err)
		return nil
	}
	m, err := jsoniter.Marshal(meta)
	if err != nil {
		log.ErrorContextf(ctx, "appendText2sqlSegments|DocID:%d|err:%+v|meta:%+v", doc.ID, err, meta)
		return nil
	}
	if len([]rune(string(m))) > config.App().Text2sqlPageContentMaxLength {
		log.WarnContextf(ctx, "appendText2sqlSegments|DocID:%d|meta.length:%d|Text2sqlPageContentMaxLength:%d",
			doc.ID, len([]rune(string(m))), config.App().Text2sqlPageContentMaxLength)
		return nil
	}
	metaSegment := newText2sqlSegments(doc, model.SegmentTypeText2SQLMeta, title, string(m), releaseStatus, purpose)
	segments = append(segments, metaSegment)

	for i, content := range contents {
		c, err := jsoniter.Marshal(content)
		if err != nil {
			log.ErrorContextf(ctx, "appendText2sqlSegments|[%d]|DocID:%d|err:%+v|content:%+v", i, doc.ID, err, content)
			return nil
		}
		if len([]rune(string(c))) > config.App().Text2sqlPageContentMaxLength {
			log.WarnContextf(ctx, "appendText2sqlSegments|DocID:%d|[%d]|content.length:%d|MaxLength:%d",
				doc.ID, i, len([]rune(string(c))), config.App().Text2sqlPageContentMaxLength)
			return nil
		}
		// title 这里可以加上tableName，便于排查问题
		c0 := newText2sqlSegments(doc, model.SegmentTypeText2SQLContent, title, string(c), releaseStatus, purpose)
		segments = append(segments, c0)
	}
	log.InfoContextf(ctx, "appendText2sqlSegments|DocID:%d|contents.len:%d|segments.len:%d", doc.ID,
		len(contents), len(segments))

	return segments
}

func (d *dao) buildText2sqlSegments(ctx context.Context, doc *model.Doc, text2sqlTableIDMap *sync.Map,
	tableFile *pb.Tables) (*model.Text2SQLSegmentMeta, []*model.Text2SQLSegmentContent, error) {

	meta := new(model.Text2SQLSegmentMeta)
	meta.Version = model.Text2sqlVersion1

	contents := make([]*model.Text2SQLSegmentContent, 0)
	// 对应到Excel文件是文件名；
	fileName := tableFile.GetMetaData().GetFileName()
	if len(fileName) == 0 {
		log.ErrorContextf(ctx, "buildText2sqlSegments|fileName is empty|doc:%+v", doc)
		return nil, nil, fmt.Errorf("fileName is empty")
	}

	log.InfoContextf(ctx, "buildText2sqlSegments|fileName:%s|Tables.len:%d", fileName, len(tableFile.GetTables()))

	meta.FileName = fileName
	meta.TableMetas = make([]*model.Text2SQLSegmentTableMeta, 0, len(tableFile.GetTables()))
	// 对应到Excel文件是一个个 Sheet
	for ti, table := range tableFile.GetTables() {

		headers := table.GetMetaData().GetHeaders()
		tableName := table.GetMetaData().GetTableName()
		log.InfoContextf(ctx, "buildText2sqlSegments|fileName:%s|[%d]|tableName:%s|headers.len:%d", fileName,
			ti, tableName, len(headers))

		key := fmt.Sprintf("%d_%s", doc.ID, table.GetMetaData().GetTableName())
		var tableID uint64
		if tid, ok := text2sqlTableIDMap.Load(key); ok {
			tableID = tid.(uint64)
		} else {
			tableID = d.GenerateSeqID()
			text2sqlTableIDMap.Store(key, tableID)
		}
		tableMeta := &model.Text2SQLSegmentTableMeta{
			TableID:   strconv.FormatUint(tableID, 10),
			TableName: table.GetMetaData().GetTableName(),
			DataType:  model.TableDataType(table.GetMetaData().GetDataType()),
			Headers:   make([]*model.Text2SQLSegmentTableMetaHeader, 0, len(headers)),
			Message:   table.GetMetaData().GetMessage(),
		}
		for hi, header := range headers {
			log.InfoContextf(ctx, "buildText2sqlSegments|fileName:%s|[%d]|tableName:%s|headerType:%s", fileName, hi,
				tableName, header.GetType().String())
			metaHeader := &model.Text2SQLSegmentTableMetaHeader{
				Type: model.TableHeaderType(header.GetType()),
			}
			metaHeader.Rows = make([]*model.Text2SQLRow, 0, len(header.GetRows()))
			for _, row := range header.GetRows() {
				r0 := &model.Text2SQLRow{}
				r0.Cells = make([]*model.Text2SQLCell, 0, len(row.GetCells()))
				for _, cell := range row.GetCells() {
					r0.Cells = append(r0.Cells, &model.Text2SQLCell{
						Value:    cell.GetValue(),
						DataType: model.TableDataCellDataType(cell.GetCellDataType()),
					})
				}
				metaHeader.Rows = append(metaHeader.Rows, r0)
			}
			tableMeta.Headers = append(tableMeta.Headers, metaHeader)
		}
		meta.TableMetas = append(meta.TableMetas, tableMeta)

		for i, row := range table.GetRows() {
			content := new(model.Text2SQLSegmentContent)
			content.Version = model.Text2sqlVersion1
			content.TableID = strconv.FormatUint(tableID, 10)
			content.RowNum = int64(i)
			content.Cells = make([]*model.Text2SQLCell, 0, len(row.GetCells()))
			for _, cell := range row.GetCells() {
				content.Cells = append(content.Cells, &model.Text2SQLCell{
					Value: cell.GetValue(),
					//  知识引擎v2.4.0版本，content cells里面的DataType字段无意义，20240701
					// DataType: model.TableDataCellDataType(cell.GetCellDataType()),
				})
			}
			contents = append(contents, content)
		}
	}

	return meta, contents, nil
}

func newText2sqlSegments(doc *model.Doc, segmentType, title string, pageContent string, releaseStatus uint32,
	purpose int) *model.DocSegmentExtend {

	return &model.DocSegmentExtend{
		DocSegment: model.DocSegment{
			RobotID:         doc.RobotID,
			CorpID:          doc.CorpID,
			StaffID:         doc.StaffID,
			DocID:           doc.ID,
			Outputs:         "",
			FileType:        doc.FileType,
			SegmentType:     segmentType,
			Title:           title,
			PageContent:     pageContent,
			OrgData:         "",
			SplitModel:      "",
			Status:          model.SegmentStatusInit,
			ReleaseStatus:   releaseStatus,
			IsDeleted:       model.SegmentIsNotDeleted,
			Type:            purpose,
			NextAction:      model.SegNextActionAdd,
			RichTextIndex:   0,
			UpdateTime:      time.Now(),
			StartChunkIndex: 0,
			EndChunkIndex:   0,
			LinkerKeep:      false,
			CreateTime:      time.Now(),
			BatchID:         doc.BatchID,
			BigStart:        0,
			BigEnd:          0,
			BigString:       "",
		},
		ExpireStart: doc.ExpireStart,
		ExpireEnd:   doc.ExpireEnd,
	}
}

// printFileParserPretty 打印解析结果
func printFileParserPretty(ctx context.Context, req *pb.FileParserCallbackReq) {
	for k, result := range req.GetResults() {
		dataType := pb.FileParserSubDataType_name[k]
		log.InfoContextf(ctx, "printFileParserPretty|%s|total:%d", dataType, result.GetTotalFileNumber())
		for i, r := range result.GetResult() {
			log.InfoContextf(ctx, "printFileParserPretty|%s|[%d]|r:%+v", dataType, i, r)
		}
	}
}

func (d *dao) checkPageContentAndOrgDataLen(ctx context.Context, docID uint64, pgContent, orgData string) error {
	if utf8.RuneCountInString(pgContent) > config.App().PageContentMaxLength {
		log.ErrorContextf(ctx, "checkPageContentAndOrgDataLen|page_content is too long, length:%d|docID:%d, "+
			"pageContent:%v", utf8.RuneCountInString(pgContent), docID, pgContent)
		return fmt.Errorf("page_content is too long")
	}
	if utf8.RuneCountInString(orgData) > config.App().OrgDataMaxLength {
		log.ErrorContextf(ctx, "checkPageContentAndOrgDataLen|org_data is too long, length:%d|docID:%d, "+
			"orgData:%v", utf8.RuneCountInString(orgData), docID, orgData)
		return fmt.Errorf("org_data is too long")
	}
	return nil
}
