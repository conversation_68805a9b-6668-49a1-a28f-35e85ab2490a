// bot-knowledge-config-server
//
// @(#)doc_dao.go  星期四, 一月 16, 2025
// Copyright(c) 2025, zrwang@Tencent. All rights reserved.

package dao

import (
	"context"
	"errors"
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/pkg"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/model"

	"gorm.io/gorm"
)

var globalDocSegmentSheetTemporaryDao *DocSegmentSheetTemporaryDao

const (
	docSegmentSheetTemporaryTableName                        = "t_doc_segment_sheet_temporary"
	DocSegmentSheetTemporaryTblColBusinessID                 = "business_id"
	DocSegmentSheetTemporaryTblColAppBizID                   = "app_biz_id"
	DocSegmentSheetTemporaryTblColCorpBizID                  = "corp_biz_id"
	DocSegmentSheetTemporaryTblColStaffBizID                 = "staff_biz_id"
	DocSegmentSheetTemporaryTblColDocBizID                   = "doc_biz_id"
	DocSegmentSheetTemporaryTblColBucket                     = "bucket"
	DocSegmentSheetTemporaryTblColRegion                     = "region"
	DocSegmentSheetTemporaryTblColCosURL                     = "cos_url"
	DocSegmentSheetTemporaryTblColCosHash                    = "cos_hash"
	DocSegmentSheetTemporaryTblColFileName                   = "file_name"
	DocSegmentSheetTemporaryTblColFileType                   = "file_type"
	DocSegmentSheetTemporaryTblColSheetOrder                 = "sheet_order"
	DocSegmentSheetTemporaryTblColSheetName                  = "sheet_name"
	DocSegmentSheetTemporaryTblColSheetTotalNum              = "sheet_total_num"
	DocSegmentSheetTemporaryTblColVersion                    = "version"
	DocSegmentSheetTemporaryTblColIsDeleted                  = "is_deleted"
	DocSegmentSheetTemporaryTblColIsDisabled                 = "is_disabled"
	DocSegmentSheetTemporaryTblColIsDisabledRetrievalEnhance = "is_disabled_retrieval_enhance"
	DocSegmentSheetTemporaryTblColCreateTime                 = "create_time"
	DocSegmentSheetTemporaryTblColUpdateTime                 = "update_time"

	docSegmentSheetTemporaryTableMaxPageSize = 1000
)

var DocSegmentSheetTemporaryTblColList = []string{DocSegmentSheetTemporaryTblColBusinessID,
	DocSegmentSheetTemporaryTblColAppBizID, DocSegmentSheetTemporaryTblColCorpBizID, DocSegmentSheetTemporaryTblColStaffBizID,
	DocSegmentSheetTemporaryTblColBucket, DocSegmentSheetTemporaryTblColRegion, DocSegmentSheetTemporaryTblColCosURL,
	DocSegmentSheetTemporaryTblColCosHash, DocSegmentSheetTemporaryTblColFileName, DocSegmentSheetTemporaryTblColFileType,
	DocSegmentSheetTemporaryTblColSheetOrder, DocSegmentSheetTemporaryTblColSheetName,
	DocSegmentSheetTemporaryTblColSheetTotalNum, DocSegmentSheetTemporaryTblColVersion, DocSegmentSheetTemporaryTblColIsDeleted,
	DocSegmentSheetTemporaryTblColIsDisabled, DocSegmentSheetTemporaryTblColIsDisabledRetrievalEnhance,
	DocSegmentSheetTemporaryTblColCreateTime, DocSegmentSheetTemporaryTblColUpdateTime}

type DocSegmentSheetTemporaryDao struct {
	BaseDao
}

// GetDocSegmentSheetTemporaryDao 获取全局的数据操作对象
func GetDocSegmentSheetTemporaryDao() *DocSegmentSheetTemporaryDao {
	if globalDocSegmentSheetTemporaryDao == nil {
		globalDocSegmentSheetTemporaryDao = &DocSegmentSheetTemporaryDao{*globalBaseDao}
	}
	return globalDocSegmentSheetTemporaryDao
}

type DocSegmentSheetTemporaryFilter struct {
	BusinessIDs     []uint64
	CorpBizID       uint64 // 企业 ID
	AppBizID        uint64
	DocBizID        uint64
	IsDeleted       *int
	SheetName       string // 查询sheet名称
	CosHash         string
	Version         *int
	UpdateTimeStart time.Time
	UpdateTimeEnd   time.Time
	Offset          uint32
	Limit           uint32
	OrderColumn     []string
	OrderDirection  []string
}

// 生成查询条件，必须按照索引的顺序排列
func (d *DocSegmentSheetTemporaryDao) generateCondition(ctx context.Context, session *gorm.DB, filter *DocSegmentSheetTemporaryFilter) {
	if filter.CorpBizID != 0 {
		session.Where(DocSegmentSheetTemporaryTblColCorpBizID+sqlEqual, filter.CorpBizID)
	}
	if filter.AppBizID != 0 {
		session.Where(DocSegmentSheetTemporaryTblColAppBizID+sqlEqual, filter.AppBizID)
	}
	if filter.DocBizID != 0 {
		session = session.Where(DocSegmentSheetTemporaryTblColDocBizID+sqlEqual, filter.DocBizID)
	}
	// is_deleted必须加入查询条件
	if filter.IsDeleted != nil {
		session.Where(DocSegmentSheetTemporaryTblColIsDeleted+sqlEqual, *filter.IsDeleted)
	}
	if filter.SheetName != "" {
		// 文件名或者审核中的文件名相同
		newStr := fileNameReplacer.Replace(filter.SheetName)
		session.Where(DocSegmentSheetTemporaryTblColSheetName+sqlEqual, newStr)
	}
}

// GetSheetList 获取文档的sheet列表
func (d *DocSegmentSheetTemporaryDao) GetSheetList(ctx context.Context, selectColumns []string,
	filter *DocSegmentSheetTemporaryFilter) ([]*model.DocSegmentSheetTemporary, error) {
	sheet := make([]*model.DocSegmentSheetTemporary, 0)
	if filter.Limit == 0 {
		// 为0正常返回空结果即可
		return sheet, nil
	}
	if filter.Limit > docSegmentSheetTemporaryTableMaxPageSize {
		// 限制单次查询最大条数
		err := fmt.Errorf("limit %d exceed max page size %d",
			filter.Limit, docSegmentSheetTemporaryTableMaxPageSize)
		log.ErrorContextf(ctx, "GetSheetList err: %+v", err)
		return sheet, err
	}
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentSheetTemporaryTableName).Select(selectColumns)
	d.generateCondition(ctx, session, filter)
	// 用于确认版本是否为初始版本
	if filter.Version != nil {
		session = session.Where(DocSegmentSheetTemporaryTblColVersion+sqlNotEqual, *filter.Version)
	}
	session = session.Offset(int(filter.Offset)).Limit(int(filter.Limit))
	if len(filter.OrderColumn) == len(filter.OrderDirection) {
		for i, orderColumn := range filter.OrderColumn {
			if filter.OrderDirection[i] != SqlOrderByAsc && filter.OrderDirection[i] != SqlOrderByDesc {
				log.ErrorContextf(ctx, "GetSheetList invalid order direction: %s", filter.OrderDirection[i])
				continue
			}
			session.Order(orderColumn + " " + filter.OrderDirection[i])
		}
	}
	res := session.Find(&sheet)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetSheetList execute sql failed, err: %+v", res.Error)
		return sheet, res.Error
	}
	return sheet, nil
}

// GetSheetByBizID 获取sheet数据
func (d *DocSegmentSheetTemporaryDao) GetSheetByBizID(ctx context.Context, selectColumns []string, corpBizID,
	appBizID uint64, docBizID uint64, bizID string) (*model.DocSegmentSheetTemporary, error) {
	sheet := &model.DocSegmentSheetTemporary{}
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentSheetTemporaryTableName).Select(selectColumns)
	session = session.Where(DocSegmentSheetTemporaryTblColCorpBizID+sqlEqual, corpBizID).Where(
		DocSegmentSheetTemporaryTblColAppBizID+sqlEqual, appBizID).Where(
		DocSegmentSheetTemporaryTblColDocBizID+sqlEqual, docBizID).Where(
		DocSegmentSheetTemporaryTblColBusinessID+sqlEqual, bizID).Where(
		DocSegmentSheetTemporaryTblColIsDeleted+sqlEqual, IsNotDeleted)
	res := session.Take(&sheet)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetSheetByBizID execute sql failed, err: %+v", res.Error)
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return sheet, res.Error
		}
	}
	return sheet, nil
}

// CreateDocSegmentSheet 创建sheet数据
func (d *DocSegmentSheetTemporaryDao) CreateDocSegmentSheet(ctx context.Context, tx *gorm.DB, sheet *model.DocSegmentSheetTemporary) error {
	if sheet == nil {
		log.ErrorContextf(ctx, "CreateDocSegmentSheet sheet is null")
		return pkg.ErrParams
	}
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	res := tx.WithContext(ctx).Table(docSegmentSheetTemporaryTableName).Create(&sheet)
	if res.Error != nil {
		log.ErrorContextf(ctx, "CreateDocSegmentSheet execute sql failed, err: %+v", res.Error)
		return res.Error
	}
	return nil
}

// GetDocSheetCountByDocBizID 获取sheet总数
func (d *DocSegmentSheetTemporaryDao) GetDocSheetCountByDocBizID(ctx context.Context, corpBizID,
	appBizID, docBizID uint64) (int64, error) {
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentSheetTemporaryTableName)
	session = session.Where(DocSegmentSheetTemporaryTblColCorpBizID+sqlEqual, corpBizID).Where(
		DocSegmentSheetTemporaryTblColAppBizID+sqlEqual, appBizID).Where(
		DocSegmentSheetTemporaryTblColDocBizID+sqlEqual, docBizID).Where(
		DocSegmentSheetTemporaryTblColIsDeleted+sqlEqual, IsNotDeleted)
	count := int64(0)
	res := session.Count(&count)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetDocSheetCountByDocBizID execute sql failed, err: %+v", res.Error)
		return 0, res.Error
	}
	return count, nil
}

// UpdateDocSegmentSheet 更新sheet数据
func (d *DocSegmentSheetTemporaryDao) UpdateDocSegmentSheet(ctx context.Context, tx *gorm.DB, updateColumns []string,
	corpBizID uint64, appBizID uint64, docBizID uint64, businessIDs []string, sheet *model.DocSegmentSheetTemporary) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	session := tx.WithContext(ctx).Table(docSegmentSheetTemporaryTableName).Select(updateColumns).
		Where(DocSegmentSheetTemporaryTblColCorpBizID+sqlEqual, corpBizID).
		Where(DocSegmentSheetTemporaryTblColAppBizID+sqlEqual, appBizID).
		Where(DocSegmentSheetTemporaryTblColDocBizID+sqlEqual, docBizID).
		Where(DocSegmentSheetTemporaryTblColBusinessID+sqlIn, businessIDs)
	sheet.Version = sheet.Version + 1
	res := session.Updates(sheet)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateDocSegmentSheet failed for businessID: %+v, err: %+v", businessIDs, res.Error)
		return res.Error
	}
	return nil
}

// UpdateDocSegmentSheetBySheetName 更新sheet数据
func (d *DocSegmentSheetTemporaryDao) UpdateDocSegmentSheetBySheetName(ctx context.Context, tx *gorm.DB, updateColumns []string,
	corpBizID uint64, appBizID uint64, docBizID uint64, sheetNames []string, sheet *model.DocSegmentSheetTemporary) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	session := tx.WithContext(ctx).Table(docSegmentSheetTemporaryTableName).Select(updateColumns).
		Where(DocSegmentSheetTemporaryTblColCorpBizID+sqlEqual, corpBizID).
		Where(DocSegmentSheetTemporaryTblColAppBizID+sqlEqual, appBizID).
		Where(DocSegmentSheetTemporaryTblColDocBizID+sqlEqual, docBizID).
		Where(DocSegmentSheetTemporaryTblColSheetName+sqlIn, sheetNames)
	sheet.Version = sheet.Version + 1
	res := session.Updates(sheet)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateDocSegmentSheet failed for sheetNames: %+v, err: %+v", sheetNames, res.Error)
		return res.Error
	}
	return nil
}

// DeleteDocSegmentSheet 删除sheet数据
func (d *DocSegmentSheetTemporaryDao) DeleteDocSegmentSheet(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, businessIDs []string) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentSheetTemporaryTblColIsDeleted,
		DocSegmentSheetTemporaryTblColUpdateTime,
		DocSegmentSheetTemporaryTblColVersion}
	sheet := &model.DocSegmentSheetTemporary{
		IsDeleted:  IsDeleted,  // 是否删除
		UpdateTime: time.Now(), // 更新时间
	}
	if err := d.UpdateDocSegmentSheet(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, businessIDs, sheet); err != nil {
		log.ErrorContextf(ctx, "DeleteDocSegmentSheet failed for businessIDs: %+v, err: %+v", businessIDs, err)
		return err
	}
	return nil
}

// DisabledDocSegmentSheet 停用sheet数据
func (d *DocSegmentSheetTemporaryDao) DisabledDocSegmentSheet(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, businessIDs []string) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentSheetTemporaryTblColIsDisabled,
		DocSegmentSheetTemporaryTblColUpdateTime,
		DocSegmentSheetTemporaryTblColVersion}
	sheet := &model.DocSegmentSheetTemporary{
		IsDisabled: model.SegmentIsDisabled, // 是否启用
		UpdateTime: time.Now(),              // 更新时间
	}
	if err := d.UpdateDocSegmentSheet(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, businessIDs, sheet); err != nil {
		log.ErrorContextf(ctx, "DisabledDocSegmentSheet failed for businessIDs: %+v, err: %+v", businessIDs, err)
		return err
	}
	return nil
}

// EnableDocSegmentSheet 启用sheet数据
func (d *DocSegmentSheetTemporaryDao) EnableDocSegmentSheet(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, businessIDs []string) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentSheetTemporaryTblColIsDisabled,
		DocSegmentSheetTemporaryTblColUpdateTime,
		DocSegmentSheetTemporaryTblColVersion}
	sheet := &model.DocSegmentSheetTemporary{
		IsDisabled: model.SegmentIsEnable, // 启用检索增强
		UpdateTime: time.Now(),            // 更新时间
	}
	if err := d.UpdateDocSegmentSheet(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, businessIDs, sheet); err != nil {
		log.ErrorContextf(ctx, "EnableDocSegmentSheet failed for businessIDs: %+v, err: %+v", businessIDs, err)
		return err
	}
	return nil
}

// DisabledRetrievalEnhanceSheet 停用检索增强
func (d *DocSegmentSheetTemporaryDao) DisabledRetrievalEnhanceSheet(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, sheetNames []string) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentSheetTemporaryTblColIsDisabledRetrievalEnhance,
		DocSegmentSheetTemporaryTblColUpdateTime,
		DocSegmentSheetTemporaryTblColVersion}
	sheet := &model.DocSegmentSheetTemporary{
		IsDisabledRetrievalEnhance: model.SheetDisabledRetrievalEnhance, // 是否启用检索增强
		UpdateTime:                 time.Now(),                          // 更新时间
	}
	if err := d.UpdateDocSegmentSheetBySheetName(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, sheetNames, sheet); err != nil {
		log.ErrorContextf(ctx, "DisabledRetrievalEnhanceSheet failed for sheetNames: %+v, err: %+v", sheetNames, err)
		return err
	}
	return nil
}

// EnableRetrievalEnhanceSheet 启用检索增强
func (d *DocSegmentSheetTemporaryDao) EnableRetrievalEnhanceSheet(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, sheetNames []string) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentSheetTemporaryTblColIsDisabledRetrievalEnhance,
		DocSegmentSheetTemporaryTblColUpdateTime,
		DocSegmentSheetTemporaryTblColVersion}
	sheet := &model.DocSegmentSheetTemporary{
		IsDisabledRetrievalEnhance: model.SheetEnableRetrievalEnhance, // 是否启用检索增强
		UpdateTime:                 time.Now(),                        // 更新时间
	}
	if err := d.UpdateDocSegmentSheetBySheetName(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, sheetNames, sheet); err != nil {
		log.ErrorContextf(ctx, "EnableRetrievalEnhanceSheet failed for businessIDs: %+v, err: %+v", sheetNames, err)
		return err
	}
	return nil
}

// DeleteDocSegmentSheetByDocBizID 硬性删除文档干预使用的临时sheet数据
func (d *DocSegmentSheetTemporaryDao) DeleteDocSegmentSheetByDocBizID(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	session := tx.WithContext(ctx).Table(docSegmentSheetTemporaryTableName).
		Where(DocSegmentSheetTemporaryTblColCorpBizID+sqlEqual, corpBizID).
		Where(DocSegmentSheetTemporaryTblColAppBizID+sqlEqual, appBizID).
		Where(DocSegmentSheetTemporaryTblColDocBizID+sqlEqual, docBizID)
	res := session.Delete(&model.DocSegmentSheetTemporary{})
	if res.Error != nil {
		log.ErrorContextf(ctx, "DeleteDocSegmentSheetByDocBizID|docBizID:%d|err:%+v",
			docBizID, res.Error)
		return res.Error
	}
	return nil
}
