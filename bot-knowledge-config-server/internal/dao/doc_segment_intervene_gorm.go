package dao

import (
	"context"
	"errors"
	"fmt"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/pkg"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/model"

	"gorm.io/gorm"
)

const (
	EditAction                     = 0
	InsertAction                   = 1
	DocSegmentInterveneMaxPageSize = 200
)

var globalDocSegmentOrgDataTemporaryDao *DocSegmentOrgDataTemporaryDao

const (
	docSegmentOrgDataTemporaryTableName                 = "t_doc_segment_org_data_temporary"
	DocSegmentOrgDataTemporaryTblColBusinessID          = "business_id"
	DocSegmentOrgDataTemporaryTblColAppBizID            = "app_biz_id"
	DocSegmentOrgDataTemporaryTblColCorpBizID           = "corp_biz_id"
	DocSegmentOrgDataTemporaryTblColStaffBizID          = "staff_biz_id"
	DocSegmentOrgDataTemporaryTblColDocBizID            = "doc_biz_id"
	DocSegmentOrgDataTemporaryTblColOrgData             = "org_data"
	DocSegmentOrgDataTemporaryTblColAddMethod           = "add_method"
	DocSegmentOrgDataTemporaryTblColAction              = "action"
	DocSegmentOrgDataTemporaryTblColSegmentType         = "segment_type"
	DocSegmentOrgDataTemporaryTblColOrgPageNumbers      = "org_page_numbers"
	DocSegmentOrgDataTemporaryTblColOriginOrgDataID     = "origin_org_data_id"
	DocSegmentOrgDataTemporaryTblColLastOrgDataID       = "last_org_data_id"
	DocSegmentOrgDataTemporaryTblColAfterOrgDataID      = "after_org_data_id"
	DocSegmentOrgDataTemporaryTblColLastOriginOrgDataID = "last_origin_org_data_id"
	DocSegmentOrgDataTemporaryTblColIsDeleted           = "is_deleted"
	DocSegmentOrgDataTemporaryTblColIsDisabled          = "is_disabled"
	DocSegmentOrgDataTemporaryTblColCreateTime          = "create_time"
	DocSegmentOrgDataTemporaryTblColUpdateTime          = "update_time"
)

const (
	getTemporarySegmentByDocIDAndKeywords = `
		SELECT
    		/*+ MAX_EXECUTION_TIME(20000) */ %s
		FROM
		    %s
		WHERE
		    corp_biz_id = ? AND app_biz_id = ? AND doc_biz_id = ? AND is_deleted = ? AND org_data LIKE ?
		ORDER BY
		    create_time ASC
		LIMIT ?,?
		`
)

var DocSegmentOrgDataTemporaryTblColList = []string{
	DocSegmentOrgDataTemporaryTblColBusinessID,
	DocSegmentOrgDataTemporaryTblColAppBizID,
	DocSegmentOrgDataTemporaryTblColCorpBizID,
	DocSegmentOrgDataTemporaryTblColStaffBizID,
	DocSegmentOrgDataTemporaryTblColDocBizID,
	DocSegmentOrgDataTemporaryTblColOrgData,
	DocSegmentOrgDataTemporaryTblColAddMethod,
	DocSegmentOrgDataTemporaryTblColAction,
	DocSegmentOrgDataTemporaryTblColSegmentType,
	DocSegmentOrgDataTemporaryTblColOrgPageNumbers,
	DocSegmentOrgDataTemporaryTblColOriginOrgDataID,
	DocSegmentOrgDataTemporaryTblColLastOrgDataID,
	DocSegmentOrgDataTemporaryTblColAfterOrgDataID,
	DocSegmentOrgDataTemporaryTblColLastOriginOrgDataID,
	DocSegmentOrgDataTemporaryTblColIsDeleted,
	DocSegmentOrgDataTemporaryTblColIsDisabled,
	DocSegmentOrgDataTemporaryTblColCreateTime,
	DocSegmentOrgDataTemporaryTblColUpdateTime,
}

type DocSegmentOrgDataTemporaryDao struct {
	BaseDao
}

// GetDocSegmentOrgDataTemporaryDao 获取全局的数据操作对象
func GetDocSegmentOrgDataTemporaryDao() *DocSegmentOrgDataTemporaryDao {
	if globalDocSegmentOrgDataTemporaryDao == nil {
		globalDocSegmentOrgDataTemporaryDao = &DocSegmentOrgDataTemporaryDao{*globalBaseDao}
	}
	return globalDocSegmentOrgDataTemporaryDao
}

type DocSegmentOrgDataTemporaryFilter struct {
	CorpBizID            uint64
	AppBizID             uint64
	DocBizID             uint64
	IsDeleted            *int
	Action               *int
	OrgData              string
	OriginOrgDataIDs     []string
	LastOriginOrgDataIDs []string
	Keywords             string
	Offset               uint32
	Limit                uint32
	OrderColumn          []string
	OrderDirection       []string
}

// 生成查询条件，必须按照索引的顺序排列
func (d *DocSegmentOrgDataTemporaryDao) generateCondition(ctx context.Context, session *gorm.DB, filter *DocSegmentOrgDataTemporaryFilter) {
	if filter.CorpBizID != 0 {
		session = session.Where(DocSegmentOrgDataTemporaryTblColCorpBizID+sqlEqual, filter.CorpBizID)
	}
	if filter.AppBizID != 0 {
		session = session.Where(DocSegmentOrgDataTemporaryTblColAppBizID+sqlEqual, filter.AppBizID)
	}
	if filter.DocBizID != 0 {
		session = session.Where(DocSegmentOrgDataTemporaryTblColDocBizID+sqlEqual, filter.DocBizID)
	}
	// is_deleted必须加入查询条件
	if filter.IsDeleted != nil {
		session = session.Where(DocSegmentOrgDataTemporaryTblColIsDeleted+sqlEqual, *filter.IsDeleted)
	}
	if filter.Action != nil {
		// 编辑的切片
		actionFlag := EditAction
		if *filter.Action == actionFlag && len(filter.OriginOrgDataIDs) != 0 {
			session = session.Where(DocSegmentOrgDataTemporaryTblColAction+sqlEqual, *filter.Action).Where(
				DocSegmentOrgDataTemporaryTblColOriginOrgDataID+sqlIn, filter.OriginOrgDataIDs)
		}
		// 新增的切片
		actionFlag = InsertAction
		if *filter.Action == actionFlag && len(filter.LastOriginOrgDataIDs) != 0 {
			session = session.Where(DocSegmentOrgDataTemporaryTblColAction+sqlEqual, *filter.Action).Where(
				DocSegmentOrgDataTemporaryTblColLastOriginOrgDataID+sqlIn, filter.LastOriginOrgDataIDs)
		}
	}
}

// GetEditOrgData 获取编辑的切片数据
func (d *DocSegmentOrgDataTemporaryDao) GetEditOrgData(ctx context.Context, selectColumns []string,
	filter *DocSegmentOrgDataTemporaryFilter) ([]*model.DocSegmentOrgDataTemporary, error) {
	orgDataList := make([]*model.DocSegmentOrgDataTemporary, 0)
	actionFlag := EditAction
	if filter.Action != nil && *filter.Action != actionFlag {
		// 非编辑动作
		err := errors.New(fmt.Sprintf("action is not edit action, action: %v", *filter.Action))
		log.ErrorContextf(ctx, "GetEditOrgData err: %+v", err)
		return orgDataList, err
	}
	if len(filter.OriginOrgDataIDs) == 0 {
		// 为0正常返回空结果即可
		return orgDataList, nil
	}
	if len(filter.OriginOrgDataIDs) > DocSegmentInterveneMaxPageSize {
		// 限制单次查询最大条数
		err := errors.New(fmt.Sprintf("origin org data ids too many, limit: %d", DocSegmentInterveneMaxPageSize))
		log.ErrorContextf(ctx, "GetEditOrgData err: %+v", err)
		return orgDataList, err
	}
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTemporaryTableName).Select(selectColumns)
	d.generateCondition(ctx, session, filter)
	if len(filter.OrderColumn) == len(filter.OrderDirection) {
		for i, orderColumn := range filter.OrderColumn {
			if filter.OrderDirection[i] != SqlOrderByAsc && filter.OrderDirection[i] != SqlOrderByDesc {
				log.ErrorContextf(ctx, "GetEditOrgData|invalid order direction:%s", filter.OrderDirection[i])
				continue
			}
			session.Order(orderColumn + " " + filter.OrderDirection[i])
		}
	}
	res := session.Find(&orgDataList)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetEditOrgData execute sql failed, err: %+v", res.Error)
		return orgDataList, res.Error
	}
	return orgDataList, nil
}

// GetInsertOrgData 获取新增的切片数据
func (d *DocSegmentOrgDataTemporaryDao) GetInsertOrgData(ctx context.Context, selectColumns []string,
	filter *DocSegmentOrgDataTemporaryFilter) ([]*model.DocSegmentOrgDataTemporary, error) {
	orgDataList := make([]*model.DocSegmentOrgDataTemporary, 0)
	actionFlag := InsertAction
	if filter.Action != nil && *filter.Action != actionFlag {
		// 非新增动作
		err := fmt.Errorf("action is not insert action, action: %v", *filter.Action)
		log.ErrorContextf(ctx, "GetInsertOrgData err: %+v", err)
		return orgDataList, err
	}
	if len(filter.LastOriginOrgDataIDs) == 0 {
		// 为0正常返回空结果即可
		return orgDataList, nil
	}
	if len(filter.LastOriginOrgDataIDs) > DocSegmentInterveneMaxPageSize {
		// 限制单次查询最大条数
		err := fmt.Errorf("origin org data ids too many, limit: %d", DocSegmentInterveneMaxPageSize)
		log.ErrorContextf(ctx, "GetInsertOrgData err: %+v", err)
		return orgDataList, err
	}
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTemporaryTableName).Select(selectColumns)
	d.generateCondition(ctx, session, filter)
	if len(filter.OrderColumn) == len(filter.OrderDirection) {
		for i, orderColumn := range filter.OrderColumn {
			if filter.OrderDirection[i] != SqlOrderByAsc && filter.OrderDirection[i] != SqlOrderByDesc {
				log.ErrorContextf(ctx, "GetInsertOrgDatax|invalid order direction:%s", filter.OrderDirection[i])
				continue
			}
			session.Order(orderColumn + " " + filter.OrderDirection[i])
		}
	}
	res := session.Find(&orgDataList)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetInsertOrgData execute sql failed, err: %+v", res.Error)
		return orgDataList, res.Error
	}
	return orgDataList, nil
}

// GetDocOrgDataByBizID 获取单个切片数据
func (d *DocSegmentOrgDataTemporaryDao) GetDocOrgDataByBizID(ctx context.Context, selectColumns []string, corpBizID,
	appBizID, docBizID uint64, businessID string) (*model.DocSegmentOrgDataTemporary, error) {
	orgData := &model.DocSegmentOrgDataTemporary{}
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTemporaryTableName).Select(selectColumns)
	session = session.Where(DocSegmentOrgDataTemporaryTblColCorpBizID+sqlEqual, corpBizID).Where(
		DocSegmentOrgDataTemporaryTblColAppBizID+sqlEqual, appBizID).Where(
		DocSegmentOrgDataTemporaryTblColDocBizID+sqlEqual, docBizID).Where(
		DocSegmentOrgDataTemporaryTblColBusinessID+sqlEqual, businessID).Where(
		DocSegmentOrgDataTemporaryTblColIsDeleted+sqlEqual, IsNotDeleted)
	res := session.Take(&orgData)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetDocOrgDataByBizID execute sql failed, err: %+v", res.Error)
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return orgData, res.Error
		}
	}
	return orgData, nil
}

// GetDocOrgDataCountByDocBizID 获取切片总数
func (d *DocSegmentOrgDataTemporaryDao) GetDocOrgDataCountByDocBizID(ctx context.Context, corpBizID,
	appBizID, docBizID uint64) (int64, error) {
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTemporaryTableName)
	session = session.Where(DocSegmentOrgDataTemporaryTblColCorpBizID+sqlEqual, corpBizID).Where(
		DocSegmentOrgDataTemporaryTblColAppBizID+sqlEqual, appBizID).Where(
		DocSegmentOrgDataTemporaryTblColDocBizID+sqlEqual, docBizID).Where(
		DocSegmentOrgDataTemporaryTblColIsDeleted+sqlEqual, IsNotDeleted)
	count := int64(0)
	res := session.Count(&count)
	if res.Error != nil {
		log.ErrorContextf(ctx, "execute sql failed, err: %+v", res.Error)
		return 0, res.Error
	}
	return count, nil
}

// GetDocOrgDataByLastOrgDataID 获取切片数据
func (d *DocSegmentOrgDataTemporaryDao) GetDocOrgDataByLastOrgDataID(ctx context.Context, selectColumns []string, corpBizID,
	appBizID, docBizID uint64, lastOrgDataID string) (*model.DocSegmentOrgDataTemporary, error) {
	orgData := &model.DocSegmentOrgDataTemporary{}
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTemporaryTableName).Select(selectColumns)
	session = session.Where(DocSegmentOrgDataTemporaryTblColCorpBizID+sqlEqual, corpBizID).Where(
		DocSegmentOrgDataTemporaryTblColAppBizID+sqlEqual, appBizID).Where(
		DocSegmentOrgDataTemporaryTblColDocBizID+sqlEqual, docBizID).Where(
		DocSegmentOrgDataTemporaryTblColLastOrgDataID+sqlEqual, lastOrgDataID).Where(
		DocSegmentOrgDataTemporaryTblColIsDeleted+sqlEqual, IsNotDeleted)
	res := session.Take(&orgData)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetDocOrgDataByLastOrgDataID execute sql failed, err: %+v", res.Error)
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return orgData, res.Error
		}
	}
	return orgData, nil
}

// GetDocOrgDataByOriginOrgDataID 获取切片数据
func (d *DocSegmentOrgDataTemporaryDao) GetDocOrgDataByOriginOrgDataID(ctx context.Context, selectColumns []string, corpBizID,
	appBizID, docBizID uint64, originOrgDataID string) (*model.DocSegmentOrgDataTemporary, error) {
	orgData := &model.DocSegmentOrgDataTemporary{}
	session := d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTemporaryTableName).Select(selectColumns)
	session = session.Where(DocSegmentOrgDataTemporaryTblColCorpBizID+sqlEqual, corpBizID).Where(
		DocSegmentOrgDataTemporaryTblColAppBizID+sqlEqual, appBizID).Where(
		DocSegmentOrgDataTemporaryTblColDocBizID+sqlEqual, docBizID).Where(
		DocSegmentOrgDataTemporaryTblColOriginOrgDataID+sqlEqual, originOrgDataID).Where(
		DocSegmentOrgDataTemporaryTblColIsDeleted+sqlEqual, IsNotDeleted)
	res := session.Take(&orgData)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetDocOrgDataByOriginOrgDataID execute sql failed, err: %+v", res.Error)
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		} else {
			return orgData, res.Error
		}
	}
	return orgData, nil
}

// GetDocOrgDataByDocBizID 获取文档更新/新增的切片
func (d *DocSegmentOrgDataTemporaryDao) GetDocOrgDataByDocBizID(ctx context.Context, selectColumns []string,
	filter *DocSegmentOrgDataTemporaryFilter) ([]*model.DocSegmentOrgDataTemporary, error) {
	orgDataList := make([]*model.DocSegmentOrgDataTemporary, 0)
	if filter == nil {
		log.ErrorContextf(ctx, "GetDocOrgDataByDocBizID|filter is null")
		return orgDataList, pkg.ErrSystem
	}
	if filter.Limit == 0 {
		// 为0正常返回空结果即可
		return orgDataList, nil
	}
	if filter.Limit > DocSegmentInterveneMaxPageSize {
		// 限制单次查询最大条数
		log.ErrorContextf(ctx, "GetDocOrgDataByDocBizID|is limit too large:%d", filter.Limit)
		return orgDataList, pkg.ErrGetDocSegmentTooLarge
	}
	var session *gorm.DB
	if filter.Keywords != "" {
		querySQL := fmt.Sprintf(getTemporarySegmentByDocIDAndKeywords, strings.Join(selectColumns, ","),
			docSegmentOrgDataTemporaryTableName)
		log.InfoContextf(ctx, "GetDocOrgDataByDocBizID|querySQL:%s", querySQL)
		keywordsArg := fmt.Sprintf("%%%s%%", special.Replace(filter.Keywords))
		res := d.tdsqlGormDB.WithContext(ctx).Raw(querySQL, filter.CorpBizID, filter.AppBizID, filter.DocBizID,
			model.DocIsNotDeleted, keywordsArg, filter.Offset, filter.Limit).Scan(&orgDataList)
		if res.Error != nil {
			log.ErrorContextf(ctx, "GetDocOrgDataByDocBizID|execute sql failed|err: %+v", res.Error)
			return orgDataList, res.Error
		}
		return orgDataList, nil
	} else {
		session = d.tdsqlGormDB.WithContext(ctx).Table(docSegmentOrgDataTemporaryTableName).Select(selectColumns)
	}
	d.generateCondition(ctx, session, filter)
	session = session.Offset(int(filter.Offset)).Limit(int(filter.Limit))
	res := session.Find(&orgDataList)
	if res.Error != nil {
		log.ErrorContextf(ctx, "GetDocOrgDataByDocBizID execute sql failed, err: %+v", res.Error)
		return orgDataList, res.Error
	}
	return orgDataList, nil
}

// CreateDocSegmentOrgData 创建切片数据(事务)
func (d *DocSegmentOrgDataTemporaryDao) CreateDocSegmentOrgData(ctx context.Context, tx *gorm.DB, orgData *model.DocSegmentOrgDataTemporary) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	res := tx.WithContext(ctx).Table(docSegmentOrgDataTemporaryTableName).Create(&orgData)
	if res.Error != nil {
		log.ErrorContextf(ctx, "CreateDocSegmentOrgData execute sql failed, err: %+v", res.Error)
		return res.Error
	}
	return nil
}

// UpdateDocSegmentOrgData 更新切片数据
func (d *DocSegmentOrgDataTemporaryDao) UpdateDocSegmentOrgData(ctx context.Context, tx *gorm.DB, updateColumns []string,
	corpBizID uint64, appBizID uint64, docBizID uint64, businessIDs []string, orgData *model.DocSegmentOrgDataTemporary) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	session := tx.WithContext(ctx).Table(docSegmentOrgDataTemporaryTableName).Select(updateColumns).
		Where(DocSegmentOrgDataTemporaryTblColCorpBizID+sqlEqual, corpBizID).
		Where(DocSegmentOrgDataTemporaryTblColAppBizID+sqlEqual, appBizID).
		Where(DocSegmentOrgDataTemporaryTblColDocBizID+sqlEqual, docBizID).
		Where(DocSegmentOrgDataTemporaryTblColBusinessID+sqlIn, businessIDs)
	res := session.Updates(orgData)
	if res.Error != nil {
		log.ErrorContextf(ctx, "UpdateDocSegmentOrgData failed for businessId: %+v, err: %+v", businessIDs, res.Error)
		return res.Error
	}
	return nil
}

// DeleteDocSegmentOrgData 删除切片数据
func (d *DocSegmentOrgDataTemporaryDao) DeleteDocSegmentOrgData(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, businessIDs []string) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentOrgDataTemporaryTblColIsDeleted, DocSegmentOrgDataTemporaryTblColUpdateTime}
	segment := &model.DocSegmentOrgDataTemporary{
		IsDeleted:  IsDeleted,  // 是否删除
		UpdateTime: time.Now(), // 更新时间
	}
	if err := d.UpdateDocSegmentOrgData(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, businessIDs, segment); err != nil {
		log.ErrorContextf(ctx, "DeleteDocSegmentOrgData failed for businessIds: %+v, err: %+v", businessIDs, err)
		return err
	}
	return nil
}

// DisabledDocSegmentOrgData 停用切片
func (d *DocSegmentOrgDataTemporaryDao) DisabledDocSegmentOrgData(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, businessIDs []string) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentOrgDataTemporaryTblColIsDisabled, DocSegmentOrgDataTemporaryTblColUpdateTime}
	segment := &model.DocSegmentOrgDataTemporary{
		IsDisabled: model.SegmentIsDisabled, // 停用切片
		UpdateTime: time.Now(),              // 更新时间
	}
	if err := d.UpdateDocSegmentOrgData(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, businessIDs, segment); err != nil {
		log.ErrorContextf(ctx, "DisabledDocSegmentOrgData| failed for businessIds: %+v, err: %+v", businessIDs, err)
		return err
	}
	return nil
}

// EnableDocSegmentOrgData 启用切片
func (d *DocSegmentOrgDataTemporaryDao) EnableDocSegmentOrgData(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64, businessIDs []string) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	updateColumns := []string{DocSegmentOrgDataTemporaryTblColIsDisabled, DocSegmentOrgDataTemporaryTblColUpdateTime}
	segment := &model.DocSegmentOrgDataTemporary{
		IsDisabled: model.SegmentIsEnable, // 启用切片
		UpdateTime: time.Now(),            // 更新时间
	}
	if err := d.UpdateDocSegmentOrgData(ctx, tx, updateColumns, corpBizID, appBizID, docBizID, businessIDs, segment); err != nil {
		log.ErrorContextf(ctx, "EnableDocSegmentOrgData| failed for businessIds: %+v, err: %+v", businessIDs, err)
		return err
	}
	return nil
}

// DeleteDocSegmentOrgDataByDocBizID 硬性删除文档干预使用的临时切片数据
func (d *DocSegmentOrgDataTemporaryDao) DeleteDocSegmentOrgDataByDocBizID(ctx context.Context, tx *gorm.DB, corpBizID uint64,
	appBizID uint64, docBizID uint64) error {
	if tx == nil {
		tx = d.tdsqlGormDB
	}
	session := tx.WithContext(ctx).Table(docSegmentOrgDataTemporaryTableName).
		Where(DocSegmentOrgDataTemporaryTblColCorpBizID+sqlEqual, corpBizID).
		Where(DocSegmentOrgDataTemporaryTblColAppBizID+sqlEqual, appBizID).
		Where(DocSegmentOrgDataTemporaryTblColDocBizID+sqlEqual, docBizID)
	res := session.Delete(&model.DocSegmentOrgDataTemporary{})
	if res.Error != nil {
		log.ErrorContextf(ctx, "DeleteDocSegmentOrgDataByDocBizID|docBizID:%d|err:%+v",
			docBizID, res.Error)
		return res.Error
	}
	return nil
}
