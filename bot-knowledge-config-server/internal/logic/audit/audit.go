package audit

import (
	"context"
	"git.code.oa.com/trpc-go/trpc-go/errs"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/dao"
	logicDoc "git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/logic/doc"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/model"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/pkg"
	"github.com/jmoiron/sqlx"
	jsoniter "github.com/json-iterator/go"
	"time"
)

// getAuditStatus 获取审核状态
func getAuditStatus(pass, isAppeal bool) uint32 {
	if pass {
		if isAppeal {
			return model.AuditStatusAppealSuccess
		}
		return model.AuditStatusPass
	} else {
		if isAppeal {
			return model.AuditStatusAppealFail
		}
		return model.AuditStatusFail
	}
}

// getChildAudits 获取子审核任务列表
func getChildAudits(ctx context.Context, audit *model.Audit) ([]*model.AuditStatusSourceList, error) {
	sourceList := make([]*model.AuditStatusSourceList, 0)
	filter := &dao.AuditFilter{
		CorpID:   audit.CorpID,
		RobotID:  audit.RobotID,
		ParentID: audit.ID,
	}
	selectColumns := []string{dao.AuditTblColStatus, dao.AuditTblColType, dao.AuditTblColParams}
	childAudits, err := dao.GetAuditDao().GetAuditList(ctx, selectColumns, filter)
	if err != nil {
		return nil, err
	}
	for _, v := range childAudits {
		auditItem := model.AuditItem{}
		if err := jsoniter.UnmarshalFromString(v.Params, &auditItem); err != nil {
			log.ErrorContextf(ctx, "任务参数解析失败 v.Params:%s,err:%+v",
				v.Params, err)
			return nil, err
		}
		sourceList = append(sourceList, &model.AuditStatusSourceList{
			Status:   v.Status,
			Source:   auditItem.Source,
			Avatar:   auditItem.HeadURL,
			Name:     auditItem.Nick,
			Greeting: auditItem.Greeting,
			Content:  auditItem.Content,
		})
	}
	return sourceList, nil
}

// getChildAuditStatusMap 获取子审核任务状态信息
func getChildAuditStatusMap(ctx context.Context, audit *model.Audit) (map[uint32][]*model.AuditStatusSourceList,
	error) {
	auditStatusSourceMap := make(map[uint32][]*model.AuditStatusSourceList)
	lists, err := getChildAudits(ctx, audit)
	if err != nil {
		return auditStatusSourceMap, err
	}
	for _, v := range lists {
		if _, ok := auditStatusSourceMap[v.Status]; !ok {
			auditStatusSourceMap[v.Status] = make([]*model.AuditStatusSourceList, 0)
		}
		auditStatusSourceMap[v.Status] = append(auditStatusSourceMap[v.Status], v)
	}
	return auditStatusSourceMap, nil
}

func updateDocAuditResult(ctx context.Context, doc *model.Doc, audit *model.Audit,
	auditsMap map[uint32][]*model.AuditStatusSourceList, pass, isAppeal bool, event string) (*model.Doc, *model.Audit, error) {
	log.InfoContextf(ctx, "updateDocAuditResult|start|DocID:%d|event:%s|Audit.RelateID:%d|Audit.Type:%d",
		doc.ID, event, audit.RelateID, audit.Type)
	audit.Status = getAuditStatus(pass, isAppeal)
	if !pass {
		filter := &dao.AuditFilter{
			CorpID:   audit.CorpID,
			RobotID:  audit.RobotID,
			ParentID: audit.ID,
		}
		childAudits, err := dao.GetAuditDao().GetAuditList(ctx, dao.AuditTblColList, filter)
		if err != nil {
			return nil, nil, err
		}
		contentFailed := false
		nameFailed := false
		segmentFailed := false
		segmentPictureFailed := false
		for _, ca := range childAudits {
			// 解析params
			p := model.AuditItem{}
			if err := jsoniter.UnmarshalFromString(ca.Params, &p); err != nil {
				continue
			}
			if p.Source == model.AuditSourceDocName && (ca.Status == model.AuditStatusFail || ca.Status == model.AuditStatusAppealFail) {
				nameFailed = true
			}
			if p.Source == model.AuditSourceDoc && (ca.Status == model.AuditStatusFail || ca.Status == model.AuditStatusAppealFail) {
				contentFailed = true
			}
			if p.Source == model.AuditSourceDocSegment && (ca.Status == model.AuditStatusFail || ca.Status == model.AuditStatusAppealFail) {
				segmentFailed = true
			}
			if p.Source == model.AuditSourceDocSegmentPic && (ca.Status == model.AuditStatusFail || ca.Status == model.AuditStatusAppealFail) {
				segmentPictureFailed = true
			}
		}
		if isAppeal {
			event = model.EventAppealFailed
			if audit.Type == model.AuditBizTypeDocSegment || audit.Type == model.AuditBizTypeDocTableSheet {
				if (segmentFailed && segmentPictureFailed) || (contentFailed && segmentPictureFailed) {
					doc.Message = "文档干预文本内容和图片内容均审核失败，请修改干预内容后重新提交"
				} else if segmentPictureFailed {
					doc.Message = "文档干预图片内容审核失败，请修改干预内容后重新提交"
				} else if segmentFailed || contentFailed {
					doc.Message = "文档干预文本内容审核失败，请修改干预内容后重新提交"
				}
			} else {
				if contentFailed && nameFailed {
					doc.Message = "文档名称和内容均审核失败，请修改文档后重新导入"
				} else if nameFailed {
					doc.Message = "文档名称审核失败，请修改文档名称"
				} else {
					doc.Message = "文档内容审核失败，请修改文档后重新导入"
				}
			}
		} else {
			event = model.EventProcessFailed
			if _, ok := auditsMap[model.AuditStatusTimeoutFail]; ok {
				audit.Status = model.AuditStatusTimeoutFail
				if audit.Type == model.AuditBizTypeDocSegment || audit.Type == model.AuditBizTypeDocTableSheet {
					// todo 重试/申诉 功能待开发
					doc.Message = "文档审核超时，请重新提交干预任务"
				} else {
					doc.Message = "文档审核超时，点击 重试 或 人工申诉"
				}
			} else {
				if audit.Type == model.AuditBizTypeDocSegment || audit.Type == model.AuditBizTypeDocTableSheet {
					if (segmentFailed && segmentPictureFailed) || (contentFailed && segmentPictureFailed) {
						doc.Message = "文档干预文本内容和图片内容均审核失败，请修改干预内容后重新提交"
					} else if segmentPictureFailed {
						doc.Message = "文档干预图片内容审核失败，请修改干预内容后重新提交"
					} else if segmentFailed || contentFailed {
						doc.Message = "文档干预文本内容审核失败，请修改干预内容后重新提交"
					}
				} else {
					if contentFailed && nameFailed {
						doc.Message = "文档名称和内容均审核失败，点击人工申诉或修改文档后重新导入"
					} else if nameFailed {
						doc.Message = "文档名称审核失败，点击人工申诉或修改文档名称"
					} else {
						doc.Message = "文档内容审核失败，点击人工申诉或修改文档后重新导入"
					}
				}
			}
		}
	}
	if event == model.EventUsedCharSizeExceeded && (audit.Type == model.AuditBizTypeDocSegment ||
		audit.Type == model.AuditBizTypeDocTableSheet) {
		doc.Message = "文档干预后字符数超出限制，请修改干预内容后重新提交"
	}
	docFilter := &dao.DocFilter{
		IDs:     []uint64{doc.ID},
		RobotId: doc.RobotID,
	}
	doc.AuditFlag = model.AuditFlagDone
	updateCols := []string{dao.DocTblColStatus, dao.DocTblColMessage, dao.DocTblColAuditFlag}
	err := logicDoc.UpdateDoc(ctx, updateCols, docFilter, doc, event)
	if err != nil {
		return nil, nil, err
	}
	return doc, audit, nil
}

// ProcessDocAuditParentTask 文档审核或者申诉回调处理函数，audit是父审核任务
func ProcessDocAuditParentTask(ctx context.Context, d dao.Dao, audit *model.Audit, pass, isAppeal bool,
	rejectReason string, params model.AuditCheckParams) error {
	log.DebugContextf(ctx, "ProcessDocAuditParentTask audit:%+v pass:%v isAppeal:%v rejectReason:%s",
		audit, pass, isAppeal, rejectReason)
	intervene := false
	if audit.Type == model.AuditBizTypeDocSegment || audit.Type == model.AuditBizTypeDocTableSheet {
		intervene = true
	}
	doc, err := d.GetDocByID(ctx, audit.RelateID, audit.RobotID)
	if err != nil {
		return err
	}
	if doc == nil {
		return pkg.ErrDocNotFound
	}
	if doc.HasDeleted() {
		audit.UpdateTime = time.Now()
		audit.Status = getAuditStatus(true, isAppeal) // 直接把审核状态改成成功
		_ = d.UpdateAuditStatus(ctx, audit)
		log.InfoContextf(ctx, "文档已经被删除，不再走审核逻辑，doc:%+v", doc)
		return nil
	}
	if !isAppeal && !doc.NeedAudit() {
		return nil
	}
	log.DebugContextf(ctx, "ProcessDocAuditParentTask|current DocID:%d", doc.ID)
	auditsMap, err := getChildAuditStatusMap(ctx, audit)
	if err != nil || len(auditsMap) == 0 {
		return pkg.ErrAuditNotFound
	}
	isNeedCharSizeNotice := false
	if err = d.GetDB().Transactionx(ctx, func(tx *sqlx.Tx) error {
		// 更新审核任务状态
		filter := &dao.AuditFilter{
			IDs: []uint64{audit.ID},
		}
		audit.Status = getAuditStatus(pass, isAppeal) // 直接把审核状态改成成功
		updateCols := []string{dao.AuditTblColRetryTimes, dao.AuditTblColStatus, dao.AuditTblColMessage}
		_, err = dao.GetAuditDao().UpdateAudit(ctx, nil, updateCols, filter, audit)
		if err != nil {
			return err
		}
		event := model.EventProcessSuccess
		if pass {
			// 审核通过需要校验字符数是否超限
			if err = d.IsUsedCharSizeExceeded(ctx, doc.CorpID, doc.RobotID); err != nil {
				isNeedCharSizeNotice = true
				event = model.EventUsedCharSizeExceeded
				doc.Message = errs.Msg(d.ConvertErrMsg(ctx, 0, doc.CorpID, pkg.ErrDocParseCharSizeExceeded))
				if intervene {
					// 流转旧文档状态
					log.DebugContextf(ctx, "ProcessDocAuditParentTask intervene|event:%s", event)
					doc, err = DeleteNewDocRecoverOldDoc(ctx, d, audit, params)
					if err != nil {
						return err
					}
					log.DebugContextf(ctx, "ProcessDocAuditParentTask intervene|current DocID:%d", doc.ID)
				}
			} else {
				if isAppeal {
					// 人工申诉成功发送通知，如果发通知失败，不报错
					_ = d.SendNoticeIfDocAppealPass(ctx, tx, doc, audit)
				}
				if err = d.DocParseSegment(ctx, tx, doc, intervene); err != nil {
					return err
				}
			}
		} else {
			event = model.EventProcessFailed
			if intervene {
				// 流转旧文档状态
				log.DebugContextf(ctx, "ProcessDocAuditParentTask intervene|event:%s", event)
				doc, err = DeleteNewDocRecoverOldDoc(ctx, d, audit, params)
				if err != nil {
					return err
				}
				log.DebugContextf(ctx, "ProcessDocAuditParentTask intervene|current DocID:%d", doc.ID)
			}
			// 审核不通过发送通知，如果发通知失败，不报错
			_ = sendAuditNotPassNotice(ctx, d, doc, audit, auditsMap, isAppeal, rejectReason)
		}
		// 更新文档状态
		doc, audit, err = updateDocAuditResult(ctx, doc, audit, auditsMap, pass, isAppeal, event)
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "审核文档失败 err:%+v", err)
		return err
	}
	if isNeedCharSizeNotice {
		var docParses model.DocParse
		if docParses, err = d.GetDocParseByDocID(ctx, doc.ID, doc.RobotID); err != nil {
			log.ErrorContextf(ctx, "查询 文档解析任务失败 args:%+v err:%+v", doc, err)
			return err
		}
		docParses.Status = model.DocParseCallBackCharSizeExceeded
		err = d.UpdateDocParseTask(ctx, docParses) // 更新解析字符状态,重试的时候不会重新解析
		if err != nil {
			return pkg.ErrUpdateDocParseTaskStatusFail
		}
		if err = d.FailCharSizeNotice(ctx, doc); err != nil {
			return err
		}
	}
	return nil
}

func DeleteNewDocRecoverOldDoc(ctx context.Context, d dao.Dao, audit *model.Audit,
	params model.AuditCheckParams) (*model.Doc, error) {
	log.InfoContextf(ctx, "DeleteNewDocRecoverOldDoc|start|OriginDocID:%d|NewDocID:%d",
		params.OriginDocID, audit.RelateID)
	// 删除新文档，回退到旧文档
	newDoc := new(model.Doc)
	newDoc.ID = audit.RelateID
	appModel, err := d.GetAppByID(ctx, audit.RobotID)
	if err != nil {
		log.ErrorContextf(ctx, "GetAppByID failed, err:%v", err)
		return nil, pkg.ErrRobotNotFound
	}
	if appModel == nil {
		log.ErrorContextf(ctx, "appModel is null")
		return nil, pkg.ErrRobotNotFound
	}
	log.InfoContextf(ctx, "DeleteNewDocRecoverOldDoc|AppBizID:%d", appModel.BusinessID)
	err = d.DeleteDocs(ctx, params.StaffID, appModel.BusinessID, []*model.Doc{newDoc})
	if err != nil {
		log.ErrorContextf(ctx, "delete new doc %d failed, %v", newDoc.ID, err)
		return nil, err
	}
	log.InfoContextf(ctx, "DeleteNewDocRecoverOldDoc|new doc is delete succeeded|DocID:%d", newDoc.ID)
	// 旧文档回退到审核中的状态
	oldDoc := new(model.Doc)
	oldDoc.ID = params.OriginDocID
	if err = d.RecoverDocStatusWithInterveneAfterAuditFail(ctx, oldDoc); err != nil {
		log.ErrorContextf(ctx, "RecoverDocStatusWithInterveneAfterAuditFail|DocID:%d|err:%v", oldDoc.ID, err)
		return nil, pkg.ErrSystem
	}
	log.InfoContextf(ctx, "DeleteNewDocRecoverOldDoc|recover old doc succeeded|DocID:%d", oldDoc.ID)
	doc, err := d.GetDocByID(ctx, oldDoc.ID, audit.RobotID)
	if err != nil {
		return nil, err
	}
	if doc == nil {
		return nil, pkg.ErrDocNotFound
	}
	return doc, nil
}
