// Package task 送审任务
package task

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"

	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/dao/redis"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/moss/types/slicex"
	"git.woa.com/baicaoyuan/moss/utils"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/algorithm/kmeans"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/algorithm/kmeans/cluster"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/app"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/client"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/dao"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/logic/knowledge_schema"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/model"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/util"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/pkg"
	utilConfig "git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	"git.woa.com/dialogue-platform/common/v3/utils/rpcutils"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

const (
	defaultDocProcessBatchSize = 20    // 默认插入Vector分批同步数量
	defaultMaxProcessDocCount  = 10000 // 默认插入Vector分批同步数量
	defaultDocClusterThreshold = 100   // 默认文档聚类阈值
)

// KnowledgeGenerateSchemaScheduler 生成schema任务
type KnowledgeGenerateSchemaScheduler struct {
	dao      dao.Dao
	task     task_scheduler.Task
	instance app.Base
	params   *model.KnowledgeGenerateSchemaParams
}

func initKnowledgeGenerateSchemaScheduler() {
	task_scheduler.Register(
		model.KnowledgeGenerateSchemaTask,
		func(t task_scheduler.Task, params model.KnowledgeGenerateSchemaParams) task_scheduler.TaskHandler {
			return &KnowledgeGenerateSchemaScheduler{
				dao:    dao.New(),
				task:   t,
				params: &params,
			}
		},
	)
}

// Prepare 数据准备
func (d *KnowledgeGenerateSchemaScheduler) Prepare(ctx context.Context) (task_scheduler.TaskKV, error) {
	log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Prepare, task: %+v, params: %+v", d.task, d.params)
	// 准备批处理文档数据
	kv := make(task_scheduler.TaskKV)
	// 更新任务状态为处理中
	task := &model.KnowledgeSchemaTask{
		CorpBizId:  d.params.CorpBizID,
		AppBizId:   d.params.AppBizID,
		BusinessID: d.params.TaskBizID,
		Status:     model.TaskStatusProcessing,
	}
	err := dao.GetKnowledgeSchemaTaskDao().UpdateKnowledgeSchemaTask(ctx, nil,
		[]string{dao.KnowledgeSchemaTaskTblColStatus}, task)
	if err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Prepare UpdateKnowledgeSchemaTask err:%+v", err)
		return kv, err
	}

	appDB, err := d.dao.GetAppByAppBizID(ctx, d.params.AppBizID)
	if err != nil {
		return kv, err
	}
	if appDB == nil {
		return kv, pkg.ErrRobotNotFound
	}
	// 只需要生成待发布、已发布文档的schema
	filter := &dao.DocFilter{
		CorpId:  appDB.CorpID,
		RobotId: appDB.ID,
		Status:  []uint32{model.DocStatusWaitRelease, model.DocStatusReleaseSuccess},
	}
	selectColumns := []string{dao.DocTblColId, dao.DocTblColBusinessId, dao.DocTblColCharSize}
	docs, err := dao.GetDocDao().GetDocList(ctx, selectColumns, filter)
	if err != nil {
		return kv, err
	}
	docBizIds := make([]uint64, 0)
	for _, doc := range docs {
		if doc.CharSize == 0 {
			continue
		}
		docBizIds = append(docBizIds, doc.BusinessID)
	}

	batchSize := utilConfig.GetMainConfig().KnowledgeSchema.DocProcessBatchSize
	if batchSize <= 0 {
		batchSize = defaultDocProcessBatchSize
	}
	for index, docChunks := range slicex.Chunk(docBizIds, batchSize) {
		docChunksStr, err := jsoniter.MarshalToString(docChunks)
		if err != nil {
			log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Prepare jsoniter.MarshalToString err:%+v", err)
			kv = make(task_scheduler.TaskKV) // 重置kv
			return kv, err
		}
		log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Prepare index:%d, docBizIds: %+v", index, docChunksStr)
		kv[strconv.Itoa(index)] = docChunksStr
	}
	return kv, nil
}

// Init 初始化
func (d *KnowledgeGenerateSchemaScheduler) Init(ctx context.Context, taskKvMap task_scheduler.TaskKV) error {
	log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Init, task: %+v, params: %+v", d.task, d.params)
	docCount := 0
	for _, taskValue := range taskKvMap {
		docBizIds := make([]uint64, 0)
		err := jsoniter.UnmarshalFromString(taskValue, &docBizIds)
		if err != nil {
			log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Init jsoniter.UnmarshalFromString err:%+v", err)
			return err
		}
		docCount += len(docBizIds)
	}
	maxProcessDocCount := getMaxProcessDocCountThreshold()
	if docCount > maxProcessDocCount {
		// 文档数量超过限制
		err := fmt.Errorf("task(KnowledgeGenerateSchema) Init docCount over limit docCount:%d, MaxProcessDocCount:%d",
			docCount, maxProcessDocCount)
		log.WarnContextf(ctx, "%v", err)
		d.params.Message = fmt.Sprintf("需要生成schema的文档数量超过%d限制", maxProcessDocCount)
		return err
	}
	d.params.NeedCluster = false
	docClusterThreshold := getDocClusterThreshold()
	if docCount > docClusterThreshold {
		// 文档数量超过限制，需要聚类
		d.params.NeedCluster = true
	}
	return nil
}

// Process 任务处理
func (d *KnowledgeGenerateSchemaScheduler) Process(ctx context.Context, progress *task_scheduler.Progress) error {
	log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Process, task: %+v, params: %+v", d.task, d.params)
	var err error
	taskKvMap := progress.TaskKV(ctx)
	allEmbedding := cluster.Observations{}
	docSchemas := make(map[uint64]*model.DocSchema)
	for taskKey, taskValue := range taskKvMap {
		docBizIds := make([]uint64, 0)
		err = jsoniter.UnmarshalFromString(taskValue, &docBizIds)
		if err != nil {
			log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process jsoniter.UnmarshalFromString err:%+v", err)
			return err
		}
		docFilter := &dao.DocFilter{
			CorpId:      d.params.CorpID,
			RobotId:     d.params.AppID,
			BusinessIds: docBizIds,
		}
		selectColumns := []string{dao.DocTblColId, dao.DocTblColBusinessId, dao.DocTblColFileName, dao.DocTblColFileType}
		docs, err := dao.GetDocDao().GetDocList(ctx, selectColumns, docFilter)
		if err != nil {
			return err
		}
		docBizId2Id := make(map[uint64]uint64)
		for _, doc := range docs {
			docBizId2Id[doc.BusinessID] = doc.ID
		}
		// 批量查询已经生成过schema和embedding的文档
		docSchemaFilter := &dao.DocSchemaFilter{
			CorpBizId: d.params.CorpBizID,
			AppBizId:  d.params.AppBizID,
			DocBizIds: docBizIds,
		}
		selectColumns = []string{dao.DocSchemaTblColDocBizId, dao.DocSchemaTblColFileName, dao.DocSchemaTblColSummary,
			dao.DocSchemaTblColVector}
		existDocSchemas, err := dao.GetDocSchemaDao().GetDocSchemaList(ctx, selectColumns, docSchemaFilter)
		if err != nil {
			log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process GetDocSchemaList err:%+v", err)
			return err
		}
		log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Process existDocSchemas length:%d",
			len(existDocSchemas))
		docSchemaNeedDelete := make([]uint64, 0)
		for _, docSchema := range existDocSchemas {
			docId, ok := docBizId2Id[docSchema.DocBizID]
			if !ok {
				docSchemaNeedDelete = append(docSchemaNeedDelete, docSchema.DocBizID)
				continue
			}
			docSchemas[docId] = docSchema
		}

		if len(docSchemaNeedDelete) > 0 {
			err = dao.GetDocSchemaDao().DeleteDocSchema(ctx, nil, d.params.CorpBizID, d.params.AppBizID, docSchemaNeedDelete)
			if err != nil {
				// 清理无效的数据，不影响主流程
				log.WarnContextf(ctx, "task(KnowledgeGenerateSchema) Process DeleteDocSchema err:%+v", err)
			}
		}

		for _, doc := range docs {
			summary := ""
			isNewDocSchema := false
			if _, ok := docSchemas[doc.ID]; !ok {
				isStructFile := false
				if model.StructFileTypeMap[doc.FileType] && !d.params.NeedCluster {
					// 结构化文档在非聚类场景，需要特殊处理，支持text2sql
					summary, isStructFile, err = getStructFileSummary(ctx, d.params, doc)
					if err != nil {
						log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process getStructFileSummary docBizId:%d err: %v",
							doc.BusinessID, err)
						return err
					}
				}
				if !isStructFile {
					// 非结构化文档，或者聚类场景
					summary, err = getCommonDocSummary(ctx, d.params, doc)
					if err != nil {
						log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process KBAgentGetOneDocSummary docBizId:%d err: %v",
							doc.BusinessID, err)
						return err
					}
				}
				if summary == "" {
					// 摘要为空就不用再进行后续的embedding和写入数据库了
					log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process KBAgentGetOneDocSummary docBizId:%d summary is empty",
						doc.BusinessID)
					return pkg.ErrSystem
				}
				newDocSchema := &model.DocSchema{
					CorpBizID: d.params.CorpBizID,
					AppBizID:  d.params.AppBizID,
					DocBizID:  doc.BusinessID,
					DocID:     doc.ID,
					FileName:  doc.FileName,
					Summary:   summary,
					Vector:    make([]byte, 0), // 必须有默认值，不然插入数据库会失败
					IsDeleted: dao.IsNotDeleted,
				}
				docSchemas[doc.ID] = newDocSchema
				isNewDocSchema = true
			}

			docSchema := docSchemas[doc.ID]
			docSchema.CorpBizID = d.params.CorpBizID
			docSchema.AppBizID = d.params.AppBizID
			docSchema.DocBizID = doc.BusinessID
			docSchema.DocID = doc.ID
			docSchema.FileName = doc.FileName // 兼容文件名修改的场景
			// 如果是已经拼接过摘要(以[{开头 且 }]结尾)的结构化文档，在聚类的时候也需要重新拿文档内容生成摘要，同时要更新到数据库
			updateColumns := []string{dao.DocSchemaTblColVector}
			if model.StructFileTypeMap[doc.FileType] && d.params.NeedCluster &&
				strings.HasPrefix(docSchema.Summary, "[{") && strings.HasSuffix(docSchema.Summary, "}]") {
				summary, err = getCommonDocSummary(ctx, d.params, doc)
				if err != nil {
					log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process KBAgentGetOneDocSummary docBizId:%d err: %v",
						doc.BusinessID, err)
					return err
				}
				docSchema.Summary = summary
				docSchema.Vector = []byte{} // 摘要变了，需要重新embedding，所以需要清空向量
				updateColumns = append(updateColumns, dao.DocSchemaTblColSummary)
			}

			if d.params.NeedCluster {
				log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Process d.params.NeedCluster:%+v",
					d.params.NeedCluster)
				float32Vector := make([]float32, 0)
				if len(docSchema.Vector) == 0 {
					// 该文档在数据库中无向量结果
					// 获取embedding结果
					vector, err := client.Embedding(ctx, d.params.AppBizID, docSchema.Summary)
					if err != nil {
						log.WarnContextf(ctx, "task(KnowledgeGenerateSchema) Process embedding failed, docID:%d err: %v", docSchema.DocID, err)
						continue
						// return err 这里不返回，因为embedding失败的文档可以继续处理
					}
					if len(vector) == 0 {
						log.WarnContextf(ctx, "task(KnowledgeGenerateSchema) Process embedding vector is empty, docID:%d", docSchema.DocID)
						continue
						// return err 这里不返回，因为embedding失败的文档可以继续处理
					}
					docSchema.Vector = util.FloatsToBytes(vector)
					float32Vector = vector
					if !isNewDocSchema {
						// 非新增文档schema，需要更新数据库
						err = dao.GetDocSchemaDao().UpdateDocSchema(ctx, nil, updateColumns, docSchema)
						if err != nil {
							log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process UpdateDocSchema err:%+v", err)
							return err
						}
					}
				} else {
					// 该文档在数据库里已有向量结果
					float32Vector = util.BytesToFloats(docSchema.Vector)
				}
				float64Vector := make([]float64, len(float32Vector))
				for i, v := range float32Vector {
					float64Vector[i] = float64(v)
				}
				allEmbedding = append(allEmbedding, cluster.Coordinates{
					ID:     strconv.FormatUint(doc.ID, 10),
					Vector: float64Vector,
				})
			}

			// 先把新文档摘要和向量写入数据库，避免后续重复生成
			if isNewDocSchema {
				err = dao.GetDocSchemaDao().CreateDocSchema(ctx, docSchema)
				if err != nil {
					log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process CreateDocSchema err:%+v", err)
					return err
				}
			}
		}
		log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Process taskKey:%s, docs length:%d", taskKey, len(docs))
	}

	err = generateKnowledgeSchema(ctx, d.dao, d.params, allEmbedding, docSchemas)
	if err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process generateKnowledgeSchema err:%+v", err)
		return err
	}

	for taskKey := range taskKvMap {
		if err := progress.Finish(ctx, taskKey); err != nil {
			log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Finish kv:%s err:%+v", taskKey, err)
			return err
		}
	}

	return nil
}

// Fail 任务失败
func (d *KnowledgeGenerateSchemaScheduler) Fail(ctx context.Context) error {
	log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Fail, appBizId id: %v", d.params.AppBizID)
	// 更新任务状态
	task := &model.KnowledgeSchemaTask{
		CorpBizId:  d.params.CorpBizID,
		AppBizId:   d.params.AppBizID,
		BusinessID: d.params.TaskBizID,
		Status:     model.TaskStatusFailed,
		Message:    d.params.Message,
	}
	err := dao.GetKnowledgeSchemaTaskDao().UpdateKnowledgeSchemaTask(ctx, nil,
		[]string{dao.KnowledgeSchemaTaskTblColStatus, dao.KnowledgeSchemaTaskTblColMessage}, task)
	if err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Fail UpdateKnowledgeSchemaTask err:%+v", err)
		return err
	}

	return nil
}

// Stop 任务停止
func (d *KnowledgeGenerateSchemaScheduler) Stop(_ context.Context) error {
	return nil
}

// Done 任务完成回调
func (d *KnowledgeGenerateSchemaScheduler) Done(ctx context.Context) error {
	log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Done, appBizId id: %v", d.params.AppBizID)
	// 更新任务状态
	task := &model.KnowledgeSchemaTask{
		CorpBizId:  d.params.CorpBizID,
		AppBizId:   d.params.AppBizID,
		BusinessID: d.params.TaskBizID,
		Status:     model.TaskStatusSuccess,
	}
	err := dao.GetKnowledgeSchemaTaskDao().UpdateKnowledgeSchemaTask(ctx, nil,
		[]string{dao.KnowledgeSchemaTaskTblColStatus}, task)
	if err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Done UpdateKnowledgeSchemaTask err:%+v", err)
		return err
	}

	return nil
}

// DocCluster 文档聚类
func DocCluster(ctx context.Context, d dao.Dao, allEmbedding cluster.Observations, allDocSchema map[uint64]*model.DocSchema,
	params *model.KnowledgeGenerateSchemaParams, clusterCount int) ([]*model.DocClusterSchema, error) {
	log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Process allEmbedding length:%d allDocSchema length:%d clusterCount:%d",
		len(allEmbedding), len(allDocSchema), clusterCount)
	if clusterCount <= 1 {
		return nil, errors.New("clusterCount must be greater than 1")
	}
	// 文档按向量聚类
	km := kmeans.New()
	clusters, err := km.Partition(allEmbedding, clusterCount)
	if err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process error partitioning with more clusters than data points, got nil")
		return nil, err
	}

	// 获取当前maxVersion
	maxVersion, err := dao.GetDocClusterSchemaDao().GetDocClusterSchemaDaoMaxVersion(ctx, params.AppBizID)
	if err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process error, GetDocClusterSchemaDaoMaxVersion err:%+v", err)
		return nil, err
	}

	// 向量聚类结果转换成文档聚类结果
	docClusterSchemaList := make([]*model.DocClusterSchema, 0)
	for _, clusterItem := range clusters {
		clusterSchema := &model.DocClusterSchema{
			CorpBizID:  params.CorpBizID,
			AppBizID:   params.AppBizID,
			BusinessID: d.GenerateSeqID(),
			IsDeleted:  dao.IsNotDeleted,
		}
		docClusterSchemaList = append(docClusterSchemaList, clusterSchema)
		kbFileInfos := make([]model.KBFileInfo, 0)
		docIds := make([]uint64, 0)
		for _, observation := range clusterItem.Observations {
			docID, err := strconv.ParseUint(observation.Coordinates().ID, 10, 64)
			if err != nil {
				log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process error, strconv.ParseUint err:%+v", err)
				return nil, err
			}
			docSchema, ok := allDocSchema[docID]
			if !ok {
				log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process error, docID:%d not found", docID)
				return nil, err
			}
			kbFileInfos = append(kbFileInfos, model.KBFileInfo{
				FileName:    docSchema.FileName,
				FileSummary: docSchema.Summary,
			})
			docIds = append(docIds, docID)
		}
		docIdsJson, err := jsoniter.Marshal(docIds)
		if err != nil {
			log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process error, jsoniter.Marshal err:%+v", err)
			return nil, err
		}
		clusterSchema.DocIDs = string(docIdsJson)

		req := &model.GetKBDirSummaryReq{
			BotBizId:  params.AppBizID,
			RequestId: rpcutils.GetDyeingKey(ctx),
			FileInfos: kbFileInfos,
			ModelName: params.SummaryModelName,
		}

		clusterName, clusterSummary, _, err := knowledge_schema.KBAgentGetDirSummary(ctx, req)
		if err != nil {
			log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process error, KBAgentGetDirSummary err:%+v", err)
			return nil, err
		}

		clusterSchema.ClusterName = clusterName
		clusterSchema.Summary = clusterSummary
		clusterSchema.Version = maxVersion + 1
	}

	// 事务写入cluster schema表，避免写入一半失败
	if err := dao.GetTdsqlGormDb(ctx).Transaction(func(tx *gorm.DB) error {
		for _, clusterSchema := range docClusterSchemaList {
			err = dao.GetDocClusterSchemaDao().CreateDocClusterSchema(ctx, tx, clusterSchema)
			if err != nil {
				log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process CreateDocClusterSchema err:%+v", err)
				return err
			}
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process err: %+v", err)
		return nil, err
	}

	return docClusterSchemaList, nil
}

func getDocClusterThreshold() int {
	return utils.When(utilConfig.GetMainConfig().KnowledgeSchema.DocClusterThreshold > 0,
		utilConfig.GetMainConfig().KnowledgeSchema.DocClusterThreshold, defaultDocClusterThreshold)
}

func getMaxProcessDocCountThreshold() int {
	return utils.When(utilConfig.GetMainConfig().KnowledgeSchema.MaxProcessDocCount > 0,
		utilConfig.GetMainConfig().KnowledgeSchema.MaxProcessDocCount, defaultMaxProcessDocCount)
}

func getStructFileSummary(ctx context.Context, params *model.KnowledgeGenerateSchemaParams, doc *model.Doc) (string, bool, error) {
	// 非聚类场景，结构化文档需要特殊处理，支持text2sql
	// 先走结构化逻辑
	var err error
	summary := ""
	isStructFile := false
	text2sqlMeta := make([]model.Text2sqlMetaMappingPreview, 0)
	isStructFile, text2sqlMeta, err = dao.GetDocMetaDataDaoDao().GetDocMetaDataForSchema(
		ctx, doc.ID, params.AppID, model.RunEnvSandbox)
	log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Process StructFile:%s isStructFile:%+v",
		doc.FileName, isStructFile)
	if err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process GetDocMetaDataForSchema err:%+v", err)
		return "", false, err
	}

	if isStructFile {
		bytes, err := jsoniter.Marshal(text2sqlMeta)
		if err != nil {
			log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process jsoniter.Marshal err:%+v", err)
			return "", false, err
		}
		summary = string(bytes)
	}
	return summary, isStructFile, nil
}

func getCommonDocSummary(ctx context.Context, params *model.KnowledgeGenerateSchemaParams, doc *model.Doc) (string, error) {
	var err error
	summary := ""
	getKBDocSummaryReq := &model.GetKBDocSummaryReq{
		RobotID:   params.AppID,
		BotBizId:  params.AppBizID,
		RequestId: rpcutils.GetDyeingKey(ctx),
		DocID:     doc.ID,
		FileName:  doc.FileName,
		ModelName: params.SummaryModelName,
	}
	summary, _, err = knowledge_schema.KBAgentGetOneDocSummary(ctx, getKBDocSummaryReq)
	if err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process KBAgentGetOneDocSummary err:%+v", err)
		return "", err
	}
	return summary, nil
}

func generateKnowledgeSchema(ctx context.Context, db dao.Dao, params *model.KnowledgeGenerateSchemaParams,
	allEmbedding cluster.Observations, docSchemas map[uint64]*model.DocSchema) error {
	log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Process generateKnowledgeSchema params:%+v "+
		"allEmbedding length:%d docSchemas length:%d", params, len(allEmbedding), len(docSchemas))
	knowledgeSchemaList := make([]*model.KnowledgeSchema, 0)
	maxVersion, err := dao.GetKnowledgeSchemaDao().GetKnowledgeSchemaMaxVersion(ctx, params.AppBizID)
	if err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process GetKnowledgeSchemaMaxVersion err:%+v", err)
		return err
	}
	docClusterSchemaList := make([]*model.DocClusterSchema, 0)

	docClusterThreshold := getDocClusterThreshold()
	clusterCount := int(math.Ceil(float64(len(allEmbedding)) / float64(docClusterThreshold)))
	if clusterCount <= 1 {
		// 拿到的embedding结果效果单个集合大小阈值，不用聚类了
		params.NeedCluster = false
	}

	if params.NeedCluster {
		log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Process d.params.NeedCluster:%+v",
			params.NeedCluster)
		docClusterSchemaList, err = DocCluster(ctx, db, allEmbedding, docSchemas,
			params, clusterCount)
		if err != nil {
			log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process DocCluster err:%+v", err)
			return err
		}
		for _, docClusterSchema := range docClusterSchemaList {
			knowledgeSchema := &model.KnowledgeSchema{
				CorpBizId: params.CorpBizID,
				AppBizId:  params.AppBizID,
				Version:   maxVersion + 1,
				ItemType:  model.KnowledgeSchemaItemTypeDocCluster,
				ItemBizId: docClusterSchema.BusinessID,
				Name:      docClusterSchema.ClusterName,
				Summary:   docClusterSchema.Summary,
				IsDeleted: dao.IsNotDeleted,
			}
			knowledgeSchemaList = append(knowledgeSchemaList, knowledgeSchema)
		}
	} else {
		for _, docSchema := range docSchemas {
			knowledgeSchema := &model.KnowledgeSchema{
				CorpBizId: params.CorpBizID,
				AppBizId:  params.AppBizID,
				Version:   maxVersion + 1,
				ItemType:  model.KnowledgeSchemaItemTypeDoc,
				ItemBizId: docSchema.DocID,
				Name:      docSchema.FileName,
				Summary:   docSchema.Summary,
				IsDeleted: dao.IsNotDeleted,
			}
			knowledgeSchemaList = append(knowledgeSchemaList, knowledgeSchema)
		}
	}

	log.DebugContextf(ctx, "task(KnowledgeGenerateSchema) Process knowledgeSchemaList length:%d",
		len(knowledgeSchemaList))
	// 事务写入知识库schema表，避免写入一半的时候被发布任务快照
	if err := dao.GetTdsqlGormDb(ctx).Transaction(func(tx *gorm.DB) error {
		// 先【硬性】删除所有旧版本，再写入新版本
		err = dao.GetKnowledgeSchemaDao().DeleteKnowledgeSchema(ctx, tx, params.CorpBizID, params.AppBizID)
		if err != nil {
			log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process DeleteKnowledgeSchema err:%+v", err)
			return err
		}
		for _, knowledgeSchema := range knowledgeSchemaList {
			err = dao.GetKnowledgeSchemaDao().CreateKnowledgeSchema(ctx, tx, knowledgeSchema)
			if err != nil {
				log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process CreateKnowledgeSchema err:%+v", err)
				return err
			}
		}
		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process err: %+v", err)
		return err
	}

	if params.NeedCluster {
		// 生成成功，设置文件聚类目录缓存
		for _, docClusterSchema := range docClusterSchemaList {
			if err := redis.SetKnowledgeSchemaDocIdByDocClusterId(ctx, params.AppBizID, model.EnvTypeSandbox, docClusterSchema); err != nil {
				log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process redis.SetKnowledgeSchema fail, err: %+v", err)
				return err
			}
		}
	}
	schemaItemsPbList := knowledge_schema.TransformKnowledgeSchema2Pb(knowledgeSchemaList)
	// 生成成功，设置schema缓存
	if err := redis.SetKnowledgeSchema(ctx, params.AppBizID, model.EnvTypeSandbox, schemaItemsPbList); err != nil {
		log.ErrorContextf(ctx, "task(KnowledgeGenerateSchema) Process redis.SetKnowledgeSchema fail, err: %+v", err)
		return err
	}
	return nil
}
