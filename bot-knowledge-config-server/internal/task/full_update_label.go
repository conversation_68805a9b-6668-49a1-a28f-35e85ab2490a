package task

import (
	"context"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/baicaoyuan/moss/types/slicex"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/client"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/dao"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/model"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/internal/vector"
	"git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/pkg"
	utilConfig "git.woa.com/dialogue-platform/bot-config/bot-knowledge-config-server/util/config"
	"git.woa.com/dialogue-platform/bot-config/task_scheduler"
	retrieval "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_retrieval_server"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"
)

// FullUpdateLabelScheduler 全量刷数据标签
type FullUpdateLabelScheduler struct {
	dao    dao.Dao
	vector *vector.SyncVector
	task   task_scheduler.Task
	p      model.FullUpdateLabel
}

const (
	updateAppIDs = "lke_update_app_ids"
)

func initFullUpdateLabelScheduler() {
	task_scheduler.Register(
		model.FullUpdateLabelTask,
		func(t task_scheduler.Task, params model.FullUpdateLabel) task_scheduler.TaskHandler {
			d := dao.New()
			return &FullUpdateLabelScheduler{
				dao:    d,
				vector: vector.NewVectorSync(d.GetDB(), d.GetTdsqlGormDB()),
				task:   t,
				p:      params,
			}
		},
	)
}

// Prepare .
func (f *FullUpdateLabelScheduler) Prepare(ctx context.Context) (kv task_scheduler.TaskKV, err error) {
	log.InfoContextf(ctx, "FullUpdateLabel Prepare, req:%+v", f.p)
	//按应用维度刷数据
	kv = make(task_scheduler.TaskKV)
	if len(f.p.AppIDs) == 0 {
		appIDs, err := dao.GetRobotDao().GetAllValidAppIDs(ctx, f.p.StartID) //可以设置起始应用主键id
		if err != nil {
			log.WarnContextf(ctx, "FullUpdateLabel GetAllValidAppIDs err:%v,req:%+v", err, f.p)
			return nil, err
		}
		for _, id := range appIDs {
			kv[cast.ToString(id)] = ""
		}
		return kv, nil

	}
	apps, err := dao.GetRobotDao().GetAppList(ctx, []string{dao.RobotTblColId}, &dao.RobotFilter{
		IDs:            f.p.AppIDs,
		Offset:         f.p.Offset,
		Limit:          f.p.Limit,
		OrderColumn:    []string{dao.RobotTblColId},
		OrderDirection: []string{dao.SqlOrderByAsc},
	})
	if err != nil {
		log.WarnContextf(ctx, "FullUpdateLabel getAppList err:%v,req:%+v", err, f.p)
		return nil, err
	}
	if len(apps) == 0 {
		log.InfoContextf(ctx, "FullUpdateLabel get apps empty,req:%+v", f.p)
		return nil, nil
	}
	for _, app := range apps {
		kv[cast.ToString(app.ID)] = ""
	}
	return kv, nil
}

// Init .
func (f *FullUpdateLabelScheduler) Init(ctx context.Context, kv task_scheduler.TaskKV) error {
	return nil
}

// Process .
func (f *FullUpdateLabelScheduler) Process(ctx context.Context, progress *task_scheduler.Progress) error {
	for k := range progress.TaskKV(ctx) {
		startTime := time.Now()
		log.InfoContextf(ctx, "FullUpdateLabel Process,k:%v", k)
		robotID := cast.ToUint64(k)
		g, gCtx := errgroup.WithContext(ctx)
		g.SetLimit(4)
		g.Go(func() error { //评测端问答
			qaList, err := dao.GetDocQaDao().GetAllDocQas(gCtx, dao.DocQaTblColList, &dao.DocQaFilter{
				RobotId:   robotID,
				IsDeleted: pkg.GetIntPtr(model.QAIsNotDeleted),
			})
			if err != nil {
				log.ErrorContextf(ctx, "FullUpdateLabel GetAllDocQas err:%v,robotID:%v", err, robotID)
				return err
			}
			for _, qa := range qaList {
				log.DebugContextf(gCtx, "FullUpdateLabel processTestQa qaID:%v", qa.ID)
				err = f.processTestQa(gCtx, qa)
				if err != nil { //柔性放过
					log.ErrorContextf(gCtx, "FullUpdateLabel processTestQa err:%v,qaID:%+v", err, qa.ID)
				}
			}
			return nil
		})
		g.Go(func() error { //评测端文档
			docList, err := dao.GetDocDao().GetDocList(gCtx, dao.DocTblColList, &dao.DocFilter{
				RobotId:   robotID,
				IsDeleted: pkg.GetIntPtr(model.DocIsNotDeleted),
			})
			if err != nil {
				log.ErrorContextf(ctx, "FullUpdateLabel GetDocList err:%v,robotID:%v", err, robotID)
				return err
			}
			for _, doc := range docList {
				log.DebugContextf(ctx, "FullUpdateLabel processTestDoc docID:%v", doc.ID)
				err = f.processTestDoc(gCtx, doc)
				if err != nil {
					log.ErrorContextf(gCtx, "FullUpdateLabel processTestDoc err:%v,docID:%+v", err, doc.ID)
				}
			}
			return nil
		})
		g.Go(func() error { //发布端问答
			nodeList, err := dao.GetRetrievalNodeDao().GetNodeIdsList(gCtx, robotID, []string{dao.NodeTblColRelatedId},
				&dao.RetrievalNodeFilter{APPID: robotID, DocType: model.DocTypeQA})
			if err != nil {
				log.ErrorContextf(ctx, "FullUpdateLabel GetNodeIdsList err:%v,robotID:%v", err, robotID)
				return err
			}
			for _, node := range nodeList {
				if node.RelatedID < 100000000000 { //过滤相似问
					log.DebugContextf(ctx, "FullUpdateLabel processProdQa qaID:%v", node.RelatedID)
					err = f.processProdQa(gCtx, node.RelatedID)
					if err != nil {
						log.ErrorContextf(gCtx, "FullUpdateLabel processProdQa err:%v,qaID:%+v", err, node.RelatedID)
					}
				}
			}
			return nil
		})
		g.Go(func() error { //发布端文档
			nodeList, err := dao.GetRetrievalNodeDao().GetDocNodeList(gCtx, robotID)
			if err != nil {
				log.ErrorContextf(ctx, "FullUpdateLabel GetNodeIdsList err:%v,robotID:%v", err, robotID)
				return err
			}
			for _, node := range nodeList {
				log.DebugContextf(ctx, "FullUpdateLabel processProdDoc docID:%v", node.DocID)
				err = f.processProdDoc(gCtx, robotID, node.DocID)
				if err != nil {
					log.ErrorContextf(ctx, "FullUpdateLabel processProdDoc err:%v,docID:%+v", err, node.DocID)
				}
			}
			return nil
		})
		if err := g.Wait(); err != nil { //柔性放过
			log.ErrorContextf(ctx, "FullUpdateLabel Process err:%v,robotID:%v", err, robotID)
		}
		if err := progress.Finish(ctx, k); err != nil {
			log.ErrorContextf(ctx, "FullUpdateLabel Finish key:%s,err:%+v", k, err)
			return err
		}
		_, err := f.dao.RedisCli().Do(ctx, "SET", updateAppIDs, k)
		if err != nil {
			log.ErrorContextf(ctx, "FullUpdateLabel redis hset err:%v,k:%v", err, k)
		}
		log.InfoContextf(ctx, "FullUpdateLabel Finish key:%s,cost:%v", k, time.Since(startTime))
	}
	return nil
}

func (f *FullUpdateLabelScheduler) processTestQa(ctx context.Context, qa *model.DocQA) error {
	if qa == nil || qa.IsDeleted == model.QAIsDeleted || !qa.IsAccepted() || qa.IsExpire() || qa.IsCharExceeded() {
		log.InfoContextf(ctx, "FullUpdateLabel processTestQa skip qaID:%v", qa.ID)
		return nil
	}
	//1.获取应用信息 得到embedding 版本
	appDB, err := f.dao.GetAppByID(ctx, qa.RobotID)
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processTestQa GetAppByID err:%v,qaID:%v", qa.ID)
		return err
	}
	if appDB.HasDeleted() {
		log.InfoContextf(ctx, "FullUpdateLabel processTestQa skip qaID:%v", qa.ID)
		return nil
	}
	embeddingConf, _, err := appDB.GetEmbeddingConf()
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processTestQa GetEmbeddingConf err:%v,appId:%v", err, appDB.ID)
		return err
	}
	embeddingVersion := embeddingConf.Version
	//2.获取问答的分类
	cate := &model.CateInfo{}
	if qa.CategoryID > 0 {
		cate, err = f.dao.GetCateByID(ctx, model.QACate, uint64(qa.CategoryID), qa.CorpID, qa.RobotID)
		if err != nil {
			log.ErrorContextf(ctx, "FullUpdateLabel processTestQa getCateInfo err:%v,qaID:%+v", err, qa.ID)
			return err
		}
	}
	labels := make([]*retrieval.VectorLabel, 0)
	labels = append(labels, &retrieval.VectorLabel{
		Name:  utilConfig.GetMainConfig().Permissions.CateRetrievalKey, //分类向量统一key
		Value: cast.ToString(cate.BusinessID),
	})
	//3.获取评测端问答相似问
	similarQuestions, err := f.dao.GetSimilarQuestionsByQA(ctx, qa)
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processTestQa GetSimilarQuestionsByQA err:%v,qaID:%v", err, qa.ID)
		return err
	}
	//5.写评测端 es和向量
	//标准问需要双写两个向量库
	req := &retrieval.UpdateLabelReq{
		RobotId:          qa.RobotID,
		AppBizId:         appDB.BusinessID,
		EnvType:          retrieval.EnvType_Test,
		IndexId:          model.ReviewVersionID,
		Ids:              []uint64{qa.ID},
		DocType:          model.DocTypeQA,
		QaType:           model.QATypeStandard,
		EmbeddingVersion: embeddingVersion,
		Labels:           labels,
	}
	_, err = client.UpdateVectorLabel(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processTestQa UpdateVectorLabel err:%v,req:%+v", err, req)
		return err
	}
	req = &retrieval.UpdateLabelReq{
		RobotId:          qa.RobotID,
		AppBizId:         appDB.BusinessID,
		EnvType:          retrieval.EnvType_Test,
		IndexId:          model.SimilarVersionID,
		Ids:              []uint64{qa.ID},
		DocType:          model.DocTypeQA,
		QaType:           model.QATypeStandard,
		EmbeddingVersion: embeddingVersion,
		Labels:           labels,
	}
	_, err = client.UpdateVectorLabel(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processTestQa UpdateVectorLabel err:%v,req:%+v", err, req)
		return err
	}
	sleepSwitch, sleepMillisecond := utilConfig.GetMainConfig().Permissions.UpdateVectorSleepSwitch,
		utilConfig.GetMainConfig().Permissions.UpdateVectorSleepMillisecond
	if len(similarQuestions) > 0 {
		for _, sims := range slicex.Chunk(similarQuestions, defaultUpdateSize) {
			tmp := make([]uint64, 0, defaultUpdateSize)
			for _, v := range sims {
				tmp = append(tmp, v.SimilarID)
			}
			req := &retrieval.UpdateLabelReq{
				RobotId:          qa.RobotID,
				AppBizId:         appDB.BusinessID,
				EnvType:          retrieval.EnvType_Test,
				IndexId:          model.ReviewVersionID,
				Ids:              tmp, //相似问业务id
				DocType:          model.DocTypeQA,
				QaType:           model.QATypeSimilar,
				EmbeddingVersion: embeddingVersion,
				Labels:           labels,
			}
			_, err = client.UpdateVectorLabel(ctx, req)
			if err != nil {
				log.ErrorContextf(ctx, "FullUpdateLabel processTestQa UpdateVectorLabel err:%v,req:%+v", err, req)
				return err
			}
			if sleepSwitch {
				time.Sleep(time.Duration(sleepMillisecond) * time.Millisecond)
			}
		}
	}
	return nil
}

func (f *FullUpdateLabelScheduler) processTestDoc(ctx context.Context, doc *model.Doc) error {
	//1.获取应用信息 得到embedding 版本
	appDB, err := f.dao.GetAppByID(ctx, doc.RobotID)
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processTestDoc GetAppByID err:%v,docID:%v", doc.ID)
		return err
	}
	if appDB.HasDeleted() {
		log.InfoContextf(ctx, "FullUpdateLabel processTestDoc skip docID:%v", doc.ID)
		return nil
	}
	embeddingConf, _, err := appDB.GetEmbeddingConf()
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processTestDoc GetEmbeddingConf err:%v,appId:%v", err, appDB.ID)
		return err
	}
	embeddingVersion := embeddingConf.Version
	//2.获取doc分类
	cateInfo := &model.CateInfo{}
	if doc.CategoryID > 0 {
		cateInfo, err = f.dao.GetCateByID(ctx, model.DocCate, uint64(doc.CategoryID), doc.CorpID, doc.RobotID)
		if err != nil {
			log.ErrorContextf(ctx, "FullUpdateLabel processTestDoc getCateInfo err:%v,docID:%+v", err, doc.ID)
			return err
		}
	}
	labels := make([]*retrieval.VectorLabel, 0)
	labels = append(labels, &retrieval.VectorLabel{
		Name:  utilConfig.GetMainConfig().Permissions.CateRetrievalKey, //分类向量统一key
		Value: cast.ToString(cateInfo.BusinessID),
	})
	//3.获取评测端文档切片
	segs, err := getDocNotDeleteSegment(ctx, doc, f.dao)
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processTestDoc getDocNotDeleteSegment err:%v,docID:%v", err, doc.ID)
		return err
	}
	//4.写评测端 es和向量
	segmentMap, text2sqlMap := make([]uint64, 0), make(map[uint32][]uint64, 0)
	for _, segment := range segs {
		if segment.IsSegmentForQA() || segment.SegmentType == model.SegmentTypeText2SQLMeta {
			continue
		}
		if segment.SegmentType == model.SegmentTypeText2SQLContent { //是表格需要获取列数
			cells, err := getColumnsCount(ctx, segment.PageContent)
			if err != nil {
				log.ErrorContextf(ctx, "FullUpdateLabel processTestDoc getColumnsCount err:%v,segmentID:%vcontent:%v",
					err, segment.ID, segment.PageContent)
				continue
			}
			text2sqlMap[cells] = append(text2sqlMap[cells], segment.ID)
		} else {
			segmentMap = append(segmentMap, segment.ID)
		}
	}
	log.DebugContextf(ctx, "FullUpdateLabel processTestDoc docID:%v,segmentMap:%v,text2sqlMap:%v", doc.ID, segmentMap, text2sqlMap)
	if err = batchUpdateSegVector(ctx, appDB.ID, appDB.BusinessID, embeddingVersion, retrieval.EnvType_Test,
		labels, segmentMap, text2sqlMap); err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processTestDoc batchUpdateSegVector err:%v,segmentMap:%v,text2sqlMap:%v",
			err, segmentMap, text2sqlMap)
		return err
	}
	return nil
}

func (f *FullUpdateLabelScheduler) processProdQa(ctx context.Context, qaID uint64) error {
	//1.获取问答信息 注意可能该问答已经删除
	qa, err := f.dao.GetQAByID(ctx, qaID)
	if err != nil || qa == nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processProdQa GetQAByID err:%v,qaID:%v", err, qaID)
		return err
	}
	//2.获取应用信息 得到embedding 版本
	appDB, err := f.dao.GetAppByID(ctx, qa.RobotID)
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processProdQa GetAppByID err:%v,qaID:%v", qa.ID)
		return err
	}
	if appDB.HasDeleted() {
		log.InfoContextf(ctx, "FullUpdateLabel processProdQa skip qaID:%v", qa.ID)
		return nil
	}
	embeddingConf, _, err := appDB.GetEmbeddingConf()
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processProdQa GetEmbeddingConf err:%v,appId:%v", err, appDB.ID)
		return err
	}
	embeddingVersion := embeddingConf.Version
	//2.获取问答的分类 注意分类也可能删除
	cate := &model.CateInfo{}
	if qa.CategoryID > 0 {
		cate, err = f.dao.GetCateByID(ctx, model.QACate, uint64(qa.CategoryID), qa.CorpID, qa.RobotID)
		if err != nil {
			log.ErrorContextf(ctx, "FullUpdateLabel processProdQa getCateInfo err:%v,qaID:%+v", err, qa.ID)
			return err
		}
	}
	labels := make([]*retrieval.VectorLabel, 0)
	labels = append(labels, &retrieval.VectorLabel{
		Name:  utilConfig.GetMainConfig().Permissions.CateRetrievalKey, //分类向量统一key
		Value: cast.ToString(cate.BusinessID),
	})
	//3.获取发布端问答相似问
	prodSims, err := dao.GetRetrievalNodeDao().GetNodeIdsList(ctx, appDB.ID,
		[]string{dao.NodeTblColId, dao.NodeTblColRelatedId},
		&dao.RetrievalNodeFilter{
			APPID:    appDB.ID,
			DocType:  model.DocTypeQA,
			ParentID: qaID,
		})
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processProdQa GetNodeIdsList err:%v,qaID:%v", err, qaID)
		return err
	}
	//5.写发布端 es和向量
	req := &retrieval.UpdateLabelReq{
		RobotId:          qa.RobotID,
		AppBizId:         appDB.BusinessID,
		EnvType:          retrieval.EnvType_Prod,
		IndexId:          model.ReviewVersionID,
		Ids:              []uint64{qa.ID},
		DocType:          model.DocTypeQA,
		QaType:           model.QATypeStandard,
		EmbeddingVersion: embeddingVersion,
		Labels:           labels,
	}
	_, err = client.UpdateVectorLabel(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processProdQa UpdateVectorLabel err:%v,req:%+v", err, req)
		return err
	}
	req = &retrieval.UpdateLabelReq{
		RobotId:          qa.RobotID,
		AppBizId:         appDB.BusinessID,
		EnvType:          retrieval.EnvType_Prod,
		IndexId:          model.SimilarVersionID,
		Ids:              []uint64{qa.ID},
		DocType:          model.DocTypeQA,
		QaType:           model.QATypeStandard,
		EmbeddingVersion: embeddingVersion,
		Labels:           labels,
	}
	_, err = client.UpdateVectorLabel(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processProdQa UpdateVectorLabel err:%v,req:%+v", err, req)
		return err
	}
	sleepSwitch, sleepMillisecond := utilConfig.GetMainConfig().Permissions.UpdateVectorSleepSwitch,
		utilConfig.GetMainConfig().Permissions.UpdateVectorSleepMillisecond
	if len(prodSims) > 0 {
		for _, sims := range slicex.Chunk(prodSims, defaultUpdateSize) {
			tmp := make([]uint64, 0, defaultUpdateSize)
			for _, v := range sims {
				tmp = append(tmp, v.RelatedID)
			}
			req := &retrieval.UpdateLabelReq{
				RobotId:          qa.RobotID,
				AppBizId:         appDB.BusinessID,
				EnvType:          retrieval.EnvType_Prod,
				IndexId:          model.ReviewVersionID,
				Ids:              tmp, //相似问业务id
				DocType:          model.DocTypeQA,
				QaType:           model.QATypeSimilar,
				EmbeddingVersion: embeddingVersion,
				Labels:           labels,
			}
			_, err = client.UpdateVectorLabel(ctx, req)
			if err != nil {
				log.ErrorContextf(ctx, "FullUpdateLabel processProdQa UpdateVectorLabel err:%v,req:%+v", err, req)
				return err
			}
			if sleepSwitch {
				time.Sleep(time.Duration(sleepMillisecond) * time.Millisecond)
			}
		}
	}
	return nil
}

func (f *FullUpdateLabelScheduler) processProdDoc(ctx context.Context, appID, docID uint64) error {
	//1.获取文档信息 注意文档可能已经删除
	doc, err := f.dao.GetDocByID(ctx, docID, appID)
	if err != nil || doc == nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processProdDoc GetDocByID err:%v,docID:%v", err, docID)
		return err
	}
	//2.获取应用信息 得到embedding 版本
	appDB, err := f.dao.GetAppByID(ctx, doc.RobotID)
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processProdDoc GetAppByID err:%v,docID:%v", doc.ID)
		return err
	}
	if appDB.HasDeleted() {
		log.InfoContextf(ctx, "FullUpdateLabel processProdDoc skip docID:%v", doc.ID)
		return nil
	}
	embeddingConf, _, err := appDB.GetEmbeddingConf()
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processProdDoc GetEmbeddingConf err:%v,appId:%v", err, appDB.ID)
		return err
	}
	embeddingVersion := embeddingConf.Version
	//3.获取doc分类 注意文档分类可能已经删除
	cateInfo := &model.CateInfo{}
	if doc.CategoryID > 0 {
		cateInfo, err = f.dao.GetCateByID(ctx, model.DocCate, uint64(doc.CategoryID), doc.CorpID, doc.RobotID)
		if err != nil {
			log.ErrorContextf(ctx, "FullUpdateLabel processProdDoc getCateInfo err:%v,docID:%+v", err, doc.ID)
			return err
		}
	}
	labels := make([]*retrieval.VectorLabel, 0)
	labels = append(labels, &retrieval.VectorLabel{
		Name:  utilConfig.GetMainConfig().Permissions.CateRetrievalKey, //分类向量统一key
		Value: cast.ToString(cateInfo.BusinessID),
	})
	//4.获取发布端文档切片
	prodSegs, err := dao.GetRetrievalNodeDao().GetNodeIdsList(ctx, appDB.ID,
		[]string{dao.NodeTblColId, dao.NodeTblColRelatedId, dao.NodeTblColPageContent, dao.NodeTblColSegmentType},
		&dao.RetrievalNodeFilter{
			APPID:   appDB.ID,
			DocType: model.DocTypeSegment,
			DocID:   docID,
		})
	if err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processProdDoc GetNodeIdsList err:%v,docID:%v", err, docID)
		return err
	}
	//5.写发布端es和向量
	segmentMap, text2sqlMap := make([]uint64, 0), make(map[uint32][]uint64, 0)
	for _, segment := range prodSegs {
		if segment.SegmentType == model.SegmentTypeText2SQLMeta {
			continue
		}
		if segment.SegmentType == model.SegmentTypeText2SQLContent { //是表格需要获取列数
			cells, err := getColumnsCount(ctx, segment.PageContent)
			if err != nil {
				log.ErrorContextf(ctx, "FullUpdateLabel processProdDoc getColumnsCount err:%v,segmentID:%vcontent:%v",
					err, segment.ID, segment.PageContent)
				continue
			}
			text2sqlMap[cells] = append(text2sqlMap[cells], segment.RelatedID)
		} else {
			segmentMap = append(segmentMap, segment.RelatedID)
		}
	}
	log.DebugContextf(ctx, "FullUpdateLabel processProdDoc docID:%v,segmentMap:%v,text2sqlMap:%v", docID, segmentMap, text2sqlMap)
	if err = batchUpdateSegVector(ctx, appDB.ID, appDB.BusinessID, embeddingVersion, retrieval.EnvType_Prod,
		labels, segmentMap, text2sqlMap); err != nil {
		log.ErrorContextf(ctx, "FullUpdateLabel processProdDoc batchUpdateSegVector err:%v,segmentMap:%v,text2sqlMap:%v",
			err, segmentMap, text2sqlMap)
		return err
	}
	return nil
}

// Done .
func (b *FullUpdateLabelScheduler) Done(ctx context.Context) error {
	log.InfoContextf(ctx, "task FullUpdateLabel finish")
	return nil
}

// Fail .
func (b *FullUpdateLabelScheduler) Fail(ctx context.Context) error {
	log.InfoContextf(ctx, "task FullUpdateLabel fail")
	return nil
}

// Stop .
func (b *FullUpdateLabelScheduler) Stop(ctx context.Context) error {
	return nil
}
